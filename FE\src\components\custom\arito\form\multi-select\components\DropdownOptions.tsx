import SearchIcon from '@mui/icons-material/Search';
import React, { useState, useRef } from 'react';
import { IconButton } from '@mui/material';
import SearchModal from '@/components/custom/arito/search-modal';
import { SelectOption } from '@/interfaces/select-option';

interface DropdownOptionsProps {
  isOpen: boolean;
  availableOptions: SelectOption[];
  handleSelectOption: ((option: SelectOption) => void) | undefined;
  withSearch?: boolean;
  searchColumns?: Array<{
    field: string;
    header: string;
  }>;
  docType?: string;
  searchFilters?: any[][];
  searchModalOpen: boolean;
  openSearchModal: () => void;
  closeSearchModal: () => void;
}

export const DropdownOptions: React.FC<DropdownOptionsProps> = ({
  isOpen,
  availableOptions,
  handleSelectOption,
  withSearch,
  searchColumns,
  docType,
  searchFilters,
  searchModalOpen,
  openSearchModal,
  closeSearchModal
}) => {
  const searchModalRef = useRef<any>(null);

  const handleSearchClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (searchModalRef.current) {
      // Set the data directly on the modal instance
      searchModalRef.current.setData(availableOptions);
    }
    openSearchModal();
  };

  const handleSearchSelect = (selectedRow: Record<string, any>) => {
    if (!handleSelectOption || !searchColumns || searchColumns.length < 2) return;

    const maField = searchColumns[0].field;
    const tenField = searchColumns[1].field;

    const selectedOption = {
      value: selectedRow[maField],
      label: selectedRow[tenField],
      [maField]: selectedRow[maField],
      [tenField]: selectedRow[tenField]
    };
    handleSelectOption(selectedOption);

    closeSearchModal();
  };

  if (!isOpen) return null;

  return (
    <div className='absolute z-10 mt-1 max-h-60 w-full overflow-y-auto rounded border border-gray-300 bg-white shadow-lg'>
      {availableOptions.length > 0 ? (
        availableOptions.map(option => (
          <div
            key={option.value}
            className={`px-4 py-2 ${handleSelectOption ? 'cursor-pointer hover:bg-gray-100' : ''}`}
            onClick={() => handleSelectOption?.(option)}
          >
            {option.label}
          </div>
        ))
      ) : (
        <div className='px-4 py-2 text-gray-500'>Không còn mục nào</div>
      )}
      {withSearch && (
        <>
          <div className='border-t border-gray-200'>
            <div className='flex justify-center p-2'>
              <IconButton
                onClick={e => handleSearchClick(e)}
                size='small'
                sx={{
                  color: '#0b87c9',
                  border: '1px solid transparent',
                  padding: '8px',
                  '&:hover': {
                    border: '1px solid rgb(65, 165, 218, 0.5)',
                    backgroundColor: 'rgba(32, 139, 197, 0.08)'
                  }
                }}
              >
                <SearchIcon style={{ fontSize: '20px' }} />
                <span className='ml-2 text-sm'>Tìm kiếm</span>
              </IconButton>
            </div>
          </div>
          {searchModalOpen && (
            <SearchModal
              visible={true}
              onClose={closeSearchModal}
              onSelect={handleSearchSelect}
              title='Tìm kiếm'
              columns={
                searchColumns?.map(col => ({
                  ...col,
                  headerName: col.header,
                  flex: 1
                })) || []
              }
              items={availableOptions}
              docType='mockData'
              filters={searchFilters || []}
            />
          )}
        </>
      )}
    </div>
  );
};
