import React from 'react';

type IconProps = {
  icon: number;
  className?: string;
  marginX?: string;
  marginY?: string;
};

const ICON_SIZE = 16;
const SPRITE_COLUMNS = 30;
const AritoIcon: React.FC<IconProps> = ({ icon, className, marginX, marginY }) => {
  const row = Math.floor((icon - 1) / SPRITE_COLUMNS);
  const col = (icon - 1) % SPRITE_COLUMNS;
  const backgroundPosition = `-${col * ICON_SIZE}px -${row * ICON_SIZE}px`;

  return (
    <div
      className={`inline-block size-4 bg-no-repeat ${className || ''}`}
      style={{
        backgroundImage: 'url(/images/all_icon_1.png)',
        backgroundPosition,
        marginLeft: marginX,
        marginRight: marginX,
        marginTop: marginY,
        marginBottom: marginY
      }}
    />
  );
};

export default AritoIcon;
