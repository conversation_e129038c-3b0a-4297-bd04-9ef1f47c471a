import { Box } from '@mui/material';
import { RelatedFieldDisplay } from './related-field';
import { SearchFieldProps } from '../../types';
import { SearchInput } from './search-input';

interface SearchFieldContainerProps extends SearchFieldProps {
  isMobile: boolean;
}

export const SearchField = ({
  field,
  fieldId,
  disabled,
  type,
  displayText,
  relatedFieldValue,
  onSearchClick,
  onInputChange,
  isMobile
}: SearchFieldContainerProps) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        width: '100%',
        flexDirection: {
          xs: 'column',
          sm: 'row'
        }
      }}
    >
      <Box
        sx={{
          position: 'relative',
          flexShrink: 0,
          width: '100%',
          maxWidth: {
            xs: '100%',
            sm: relatedFieldValue ? '33%' : '100%'
          }
        }}
      >
        <SearchInput
          field={field}
          fieldId={fieldId}
          disabled={disabled}
          type={type}
          displayText={displayText}
          relatedFieldValue={relatedFieldValue}
          onSearchClick={onSearchClick}
          onInputChange={onInputChange}
        />
      </Box>

      <RelatedFieldDisplay value={relatedFieldValue} isMobile={isMobile} />
    </Box>
  );
};
