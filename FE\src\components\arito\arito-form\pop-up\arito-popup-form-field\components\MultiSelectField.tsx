import { Autocomplete, Chip, TextField, Box } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { Controller } from 'react-hook-form';
import React from 'react';
import { fieldStyles } from '../styles/constants';

interface SelectOption {
  value: string | number;
  label: string;
}

interface MultiSelectFieldProps {
  name: string;
  control: any;
  disabled?: boolean;
  isViewMode?: boolean;
  fieldId: string;
  options?: SelectOption[];
}

export const MultiSelectField: React.FC<MultiSelectFieldProps> = ({
  name,
  control,
  disabled = false,
  isViewMode = false,
  fieldId,
  options = []
}) => {
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={[]}
      render={({ field }) => (
        <Autocomplete
          multiple
          id={fieldId}
          options={options}
          getOptionLabel={option => {
            // Support both option objects and direct values
            if (typeof option === 'string' || typeof option === 'number') {
              const foundOption = options?.find(opt => opt.value === option);
              return foundOption ? foundOption.label : String(option);
            }
            return option.label;
          }}
          value={field.value || []}
          onChange={(_, newValue) => {
            // Convert to array of values if needed
            const values = newValue.map(item =>
              typeof item === 'string' || typeof item === 'number' ? item : item.value
            );
            field.onChange(values);
          }}
          isOptionEqualToValue={(option, value) => {
            if (typeof value === 'string' || typeof value === 'number') {
              return option.value === value;
            }
            return option.value === value.value;
          }}
          disabled={disabled || isViewMode}
          renderInput={params => (
            <TextField
              {...params}
              variant='standard'
              size='small'
              sx={{
                '& .MuiInput-root': {
                  fontSize: '14px',
                  '&:before': {
                    borderBottom: '1px solid #e5e7eb'
                  },
                  '&:hover:not(.Mui-disabled):before': {
                    borderBottom: '1px solid #2563EB'
                  },
                  '&.Mui-focused:after': {
                    borderBottom: '1px solid #2563EB'
                  }
                },
                '& .MuiInput-input': {
                  padding: '4px 8px'
                },
                '& .MuiAutocomplete-endAdornment': {
                  top: '4px'
                }
              }}
            />
          )}
          renderTags={(selectedOptions, getTagProps) =>
            selectedOptions.map((option, index) => {
              // Handle both object and primitive value types
              const optionLabel =
                typeof option === 'object'
                  ? option.label
                  : options?.find(opt => opt.value === option)?.label || String(option);

              return (
                <Chip
                  {...getTagProps({ index })}
                  key={index}
                  label={optionLabel}
                  size='small'
                  sx={fieldStyles.chips.chip}
                  deleteIcon={<CloseIcon style={{ fontSize: '14px' }} />}
                />
              );
            })
          }
          sx={{
            '& .MuiAutocomplete-tag': {
              margin: '2px'
            }
          }}
          ChipProps={{
            size: 'small'
          }}
        />
      )}
    />
  );
};
