import { <PERSON><PERSON>, Printer, RefreshCw, Search, Sheet } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';
import { SearchFormValues } from '../types';
import { formatDateRange } from '../utlis';

interface ActionBarProps {
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  searchParams?: SearchFormValues;
  className?: string;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  searchParams,
  className
}) => {
  const dateRangeText = formatDateRange(searchParams);

  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div className='flex flex-col'>
          <h1 className='text-xl font-bold'>Bảng kê hàng bán bị trả lại</h1>
          {dateRangeText && (
            <p className='mt-1 text-sm text-gray-600'>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              {dateRangeText}
            </p>
          )}
        </div>
      }
    >
      {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />}
      <AritoMenuButton
        title='In ấn'
        icon={<Printer />}
        items={[
          {
            title: 'Mẫu số lượng và giá trị',
            icon: <AritoIcon icon={20} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='destructive' />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
      )}
      {onExportDataClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
