import { MenuItem, Select } from '@mui/material';
import { Controller } from 'react-hook-form';
import React from 'react';
import { fieldStyles } from '../styles/constants';

interface SelectOption {
  value: string | number;
  label: string;
}

interface SelectFieldProps {
  name: string;
  control: any;
  disabled?: boolean;
  isViewMode?: boolean;
  fieldId: string;
  options?: SelectOption[];
}

export const SelectField: React.FC<SelectFieldProps> = ({
  name,
  control,
  disabled = false,
  isViewMode = false,
  fieldId,
  options = []
}) => {
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={options.length > 0 ? options[0].value : ''}
      render={({ field }) => (
        <Select
          {...field}
          value={field.value?.toString() || (options.length > 0 ? options[0].value.toString() : '')}
          disabled={disabled || isViewMode}
          size='small'
          defaultValue={options[0]?.value}
          fullWidth
          id={fieldId}
          sx={{
            '& .MuiOutlinedInput-notchedOutline': {
              border: 'none'
            },
            '&.MuiOutlinedInput-root': {
              borderBottom: '1px solid #e5e7eb',
              borderRadius: 0,
              '&:hover': {
                borderBottom: '1px solid #2563EB'
              },
              '&.Mui-focused': {
                borderBottom: '1px solid #2563EB'
              }
            },
            '& .MuiSelect-select': {
              padding: '4px 8px',
              fontSize: '12px'
            },
            backgroundColor: 'transparent',
            position: 'relative',
            top: '-4px'
          }}
          MenuProps={{
            PaperProps: {
              sx: {
                borderRadius: '4px',
                marginTop: '4px',
                '& .MuiMenuItem-root': {
                  fontSize: '12px',
                  minHeight: '30px',
                  padding: '4px 8px'
                }
              }
            }
          }}
        >
          {options.map(option => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      )}
    />
  );
};
