import { Controller, useFormContext } from 'react-hook-form';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import React from 'react';
import { StyledFileInput, FileInputContainer, FileList, FileItem } from '../inputs/StyledFileInput';
import { InputFieldWrapper, ErrorMessage } from '../components/InputFieldWrapper';
import { StyledInput } from '../inputs/StyledInput';
import { FormRow } from '../components/FormRow';
import { CommonFieldProps } from '../types';

export const FileField: React.FC<CommonFieldProps> = ({ field, maxLabelWidth, isViewMode }) => {
  const { key, label, required, disabled = isViewMode, minWidth, valueRender, multiple, accept } = field;

  const { setValue } = useFormContext();

  // Label style
  const labelStyle = {
    width: `${maxLabelWidth}px`,
    minWidth: `${maxLabelWidth}px`,
    fontSize: '12px',
    fontWeight: 400,
    marginRight: '8px',
    whiteSpace: 'nowrap' as const,
    overflow: 'visible' as const,
    display: 'inline-block'
  };

  // Input wrapper style
  const inputWrapperStyle = {
    flex: '1 1 auto',
    position: 'relative' as const,
    minWidth: minWidth ? (typeof minWidth === 'number' ? `${minWidth}px` : minWidth) : '0',
    display: 'flex',
    flexDirection: 'column' as const
  };

  // Container style
  const containerStyle = {
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    position: 'relative' as const,
    overflow: 'visible'
  };

  // File list helper function
  const getFileList = (files: File[] | null) => {
    if (!files || files.length === 0) return null;

    return (
      <FileList>
        {files.map((file, index) => (
          <FileItem key={index}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: 'calc(100% - 20px)'
              }}
            >
              <span style={{ marginRight: '4px' }}>•</span>
              <span>{file.name}</span>
            </div>
            {!disabled && (
              <span
                onClick={() => {
                  if (!valueRender) {
                    const newFiles = [...files];
                    newFiles.splice(index, 1);
                    setValue(key, multiple ? newFiles : null, { shouldValidate: true });
                  }
                }}
                style={{
                  fontSize: '14px',
                  cursor: 'pointer',
                  color: '#999',
                  fontWeight: 'bold',
                  marginLeft: '4px'
                }}
              >
                ×
              </span>
            )}
          </FileItem>
        ))}
      </FileList>
    );
  };

  // Colon element for view mode
  const colonElement = disabled && (
    <span
      style={{
        marginRight: '10px',
        fontSize: '12px',
        fontWeight: 400
      }}
    >
      :
    </span>
  );

  // Label content
  const labelContent = (
    <span style={labelStyle}>
      {label}
      {!disabled && required && <span style={{ color: 'red' }}>*</span>}
    </span>
  );

  return (
    <FormRow>
      {labelContent}
      <InputFieldWrapper style={inputWrapperStyle}>
        <div style={containerStyle}>
          {colonElement}
          <Controller
            name={key}
            render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
              <>
                {valueRender ? (
                  <StyledInput type='text' value={valueRender(value) || ''} disabled={true} />
                ) : (
                  <FileInputContainer>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <UploadFileIcon
                        style={{
                          fontSize: '16px',
                          color: '#0b87c9',
                          marginRight: '4px',
                          flexShrink: 0
                        }}
                      />
                      <StyledFileInput
                        type='file'
                        onChange={e => {
                          const files = e.target.files;
                          if (multiple) {
                            const newFiles = Array.from(files || []);
                            const existingFiles = Array.isArray(value) ? value : [];
                            onChange([...existingFiles, ...newFiles]);
                          } else {
                            onChange(files?.[0] || null);
                          }
                        }}
                        multiple={multiple}
                        accept={accept}
                        disabled={disabled}
                        ref={ref}
                      />
                    </div>

                    {/* Hidden placeholder for the input field itself */}
                    <div
                      style={{
                        position: 'absolute',
                        left: '24px',
                        color: 'transparent',
                        fontSize: '1px',
                        width: '1px',
                        height: '1px',
                        overflow: 'hidden'
                      }}
                    />

                    {/* Show files list */}
                    {getFileList(
                      multiple ? (Array.isArray(value) ? value : value ? [value] : []) : value ? [value] : []
                    )}
                  </FileInputContainer>
                )}
                {error && <ErrorMessage>{error.message}</ErrorMessage>}
              </>
            )}
          />
        </div>
      </InputFieldWrapper>
    </FormRow>
  );
};

export default FileField;
