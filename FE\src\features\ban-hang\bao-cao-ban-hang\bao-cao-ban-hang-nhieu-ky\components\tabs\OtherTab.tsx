import React, { useState } from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';
import { FormField } from '@/components/custom/arito/form/form-field';
import DocumentNumberRange from '../DocumentNumberRange';
import { Label } from '@/components/ui/label';

const OtherTab: React.FC = () => {
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);

  const handleSaveFilterTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving filter template:', data);
    setSaveFilterTemplateDialogOpen(false);
  };

  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã giao dịch:</Label>
          <FormField
            name='transactionCode'
            label=''
            type='text'
            className='w-32'
            withSearch
            searchEndpoint='/api/transactions'
            searchResultLabelKey='transactionName'
            searchResultValueKey='transactionCode'
            searchColumns={[
              {
                field: 'transactionCode',
                headerName: 'Mã giao dịch',
                width: 120
              },
              {
                field: 'transactionName',
                headerName: 'Tên giao dịch',
                width: 200
              },
              {
                field: 'documentCode',
                headerName: 'Mã chứng từ',
                width: 120
              }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản vật tư:</Label>
          <FormField
            name='itemAccount'
            label=''
            type='text'
            className='w-32'
            withSearch
            searchEndpoint='/api/accounts'
            searchResultLabelKey='accountName'
            searchResultValueKey='accountCode'
            searchColumns={[
              {
                field: 'accountCode',
                headerName: 'Mã tài khoản',
                width: 120
              },
              {
                field: 'accountName',
                headerName: 'Tên tài khoản',
                width: 200
              },
              {
                field: 'parentAccount',
                headerName: 'Tài khoản mẹ',
                width: 120
              },
              {
                field: 'isLedgerAccount',
                headerName: 'Tk sổ cái',
                width: 100
              },
              {
                field: 'isDetailAccount',
                headerName: 'Tk chi tiết',
                width: 100
              },
              { field: 'accountLevel', headerName: 'Bậc tk', width: 80 }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản doanh thu:</Label>
          <FormField
            name='revenueAccount'
            label=''
            type='text'
            className='w-32'
            withSearch
            searchEndpoint='/api/accounts'
            searchResultLabelKey='accountName'
            searchResultValueKey='accountCode'
            searchColumns={[
              {
                field: 'accountCode',
                headerName: 'Mã tài khoản',
                width: 120
              },
              {
                field: 'accountName',
                headerName: 'Tên tài khoản',
                width: 200
              },
              {
                field: 'parentAccount',
                headerName: 'Tài khoản mẹ',
                width: 120
              },
              {
                field: 'isLedgerAccount',
                headerName: 'Tk sổ cái',
                width: 100
              },
              {
                field: 'isDetailAccount',
                headerName: 'Tk chi tiết',
                width: 100
              },
              { field: 'accountLevel', headerName: 'Bậc tk', width: 80 }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản giá vốn:</Label>
          <FormField
            name='costAccount'
            label=''
            type='text'
            className='w-32'
            withSearch
            searchEndpoint='/api/accounts'
            searchResultLabelKey='accountName'
            searchResultValueKey='accountCode'
            searchColumns={[
              {
                field: 'accountCode',
                headerName: 'Mã tài khoản',
                width: 120
              },
              {
                field: 'accountName',
                headerName: 'Tên tài khoản',
                width: 200
              },
              {
                field: 'parentAccount',
                headerName: 'Tài khoản mẹ',
                width: 120
              },
              {
                field: 'isLedgerAccount',
                headerName: 'Tk sổ cái',
                width: 100
              },
              {
                field: 'isDetailAccount',
                headerName: 'Tk chi tiết',
                width: 100
              },
              { field: 'accountLevel', headerName: 'Bậc tk', width: 80 }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã lô:</Label>
          <FormField
            name='batchCode'
            label=''
            type='text'
            className='w-32'
            withSearch
            searchEndpoint='/api/batches'
            searchResultLabelKey='batchName'
            searchResultValueKey='batchCode'
            searchColumns={[
              {
                field: 'batchCode',
                headerName: 'Mã lô',
                width: 120
              },
              {
                field: 'batchName',
                headerName: 'Tên lô',
                width: 200
              }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vị trí:</Label>
          <FormField
            name='locationCode'
            label=''
            type='text'
            className='w-32'
            withSearch
            searchEndpoint='/api/locations'
            searchResultLabelKey='locationName'
            searchResultValueKey='locationCode'
            searchColumns={[
              {
                field: 'locationCode',
                headerName: 'Mã vị trí',
                width: 120
              },
              {
                field: 'locationName',
                headerName: 'Tên vị trí',
                width: 200
              }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số c/từ (từ/đến):</Label>
          <DocumentNumberRange fromDocumentNumberName='fromDocumentNumber' toDocumentNumberName='toDocumentNumber' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Diễn giải:</Label>
          <FormField name='description' label='' type='text' className='w-32' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu lọc báo cáo:</Label>
          <div className='flex items-center gap-2'>
            <FormField
              name='reportFilterTemplate'
              label=''
              type='select'
              options={[{ value: 'user_filter', label: 'Người dùng tự lọc' }]}
              className='w-48'
            />

            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => setSaveFilterTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />
    </div>
  );
};

export default OtherTab;
