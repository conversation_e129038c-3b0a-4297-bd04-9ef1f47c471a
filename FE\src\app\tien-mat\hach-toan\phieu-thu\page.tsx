'use client';

import ReceiptVoucherPage from '@/features/tien-mat/hach-toan/phieu-thu';

export default function Page() {
  // Mock data for initial state
  const initialRows: any[] = [
    {
      id: 1,
      status: 'Chờ duyệt',
      voucherNumber: 'PT001',
      date: '2024-05-01',
      customerCode: 'KH001',
      customerName: 'Công ty ABC',
      description: 'Thu tiền khách hàng',
      debitAccount: '111',
      voucherType: 'Thu tiền mặt',
      totalAmount: 5000000,
      foreignCurrency: '',
      departmentCode: 'BP001',
      createdBy: 'User1',
      createdAt: '2024-05-01 08:00',
      isPosted: false,
      details: [
        {
          id: '1',
          accountCode: '111',
          accountName: 'Tiền mặt',
          debitAccount: '111',
          creditAccount: '131',
          amount: 3000000,
          description: 'Thu tiền khách hàng',
          departmentCode: 'BP001',
          departmentName: 'Phòng kinh doanh'
        },
        {
          id: '2',
          accountCode: '111',
          accountName: 'Tiền mặt',
          debitAccount: '111',
          creditAccount: '511',
          amount: 2000000,
          description: 'Thu tiền bán hàng',
          departmentCode: 'BP001',
          departmentName: 'Phòng kinh doanh'
        }
      ]
    },
    {
      id: 2,
      status: 'Đã duyệt',
      voucherNumber: 'PT002',
      date: '2024-05-02',
      customerCode: 'KH002',
      customerName: 'Công ty XYZ',
      description: 'Thu tiền bán hàng',
      debitAccount: '111',
      voucherType: 'Thu tiền mặt',
      totalAmount: 7500000,
      foreignCurrency: '',
      departmentCode: 'BP002',
      createdBy: 'User2',
      createdAt: '2024-05-02 09:30',
      isPosted: true,
      details: [
        {
          id: '3',
          accountCode: '111',
          accountName: 'Tiền mặt',
          debitAccount: '111',
          creditAccount: '131',
          amount: 4500000,
          description: 'Thu tiền khách hàng',
          departmentCode: 'BP002',
          departmentName: 'Phòng tài chính'
        },
        {
          id: '4',
          accountCode: '111',
          accountName: 'Tiền mặt',
          debitAccount: '111',
          creditAccount: '511',
          amount: 3000000,
          description: 'Thu tiền bán hàng',
          departmentCode: 'BP002',
          departmentName: 'Phòng tài chính'
        }
      ]
    },
    {
      id: 3,
      status: 'Chờ duyệt',
      voucherNumber: 'PT003',
      date: '2024-05-03',
      customerCode: 'KH003',
      customerName: 'Công ty DEF',
      description: 'Thu tiền ứng trước',
      debitAccount: '111',
      voucherType: 'Thu tiền ứng trước',
      totalAmount: 3500000,
      foreignCurrency: '',
      departmentCode: 'BP001',
      createdBy: 'User1',
      createdAt: '2024-05-03 10:15',
      isPosted: false,
      details: [
        {
          id: '5',
          accountCode: '111',
          accountName: 'Tiền mặt',
          debitAccount: '111',
          creditAccount: '131',
          amount: 3500000,
          description: 'Thu tiền ứng trước',
          departmentCode: 'BP001',
          departmentName: 'Phòng kinh doanh'
        }
      ]
    },
    {
      id: 4,
      status: 'Từ chối',
      voucherNumber: 'PT004',
      date: '2024-05-04',
      customerCode: 'KH004',
      customerName: 'Công ty GHI',
      description: 'Thu tiền khách hàng',
      debitAccount: '111',
      voucherType: 'Thu tiền mặt',
      totalAmount: 2000000,
      foreignCurrency: '',
      departmentCode: 'BP002',
      createdBy: 'User2',
      createdAt: '2024-05-04 14:30',
      isPosted: false,
      details: [
        {
          id: '6',
          accountCode: '111',
          accountName: 'Tiền mặt',
          debitAccount: '111',
          creditAccount: '131',
          amount: 2000000,
          description: 'Thu tiền khách hàng',
          departmentCode: 'BP002',
          departmentName: 'Phòng tài chính'
        }
      ]
    }
  ];

  return <ReceiptVoucherPage initialRows={initialRows} />;
}
