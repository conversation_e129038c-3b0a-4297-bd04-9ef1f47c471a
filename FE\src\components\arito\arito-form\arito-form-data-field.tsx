'use client';

import { Box, Checkbox, FormControlLabel, TextField, Typography } from '@mui/material';
import dayjs from 'dayjs';

export const DataField = ({
  label,
  value,
  type = 'text',
  options,
  className
}: {
  label: string;
  value: any;
  type?: 'text' | 'number' | 'select' | 'checkbox' | 'date';
  options?: { value: string; label: string }[];
  className?: string;
}) => {
  const renderValue = () => {
    const commonTextFieldProps = {
      variant: 'standard' as const,
      fullWidth: true,
      disabled: true,
      size: 'small' as const,
      sx: {
        '& .MuiInput-root': {
          fontSize: '14px',
          '&:before': {
            borderBottom: '1px solid #e5e7eb'
          }
        },
        '& .MuiInput-input': {
          padding: '4px 8px',
          color: 'rgba(0, 0, 0, 0.87)'
        }
      }
    };

    switch (type) {
      case 'select':
        const selectedOption = options?.find(option => option.value === value);
        return <TextField {...commonTextFieldProps} value={selectedOption?.label || ''} />;
      case 'checkbox':
        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              position: 'relative',
              top: '-4px'
            }}
          >
            <FormControlLabel
              control={
                <Checkbox
                  checked={Boolean(value)}
                  disabled
                  sx={{
                    color: '#d1d5db',
                    '&.Mui-checked': {
                      color: '#2563EB'
                    }
                  }}
                />
              }
              label={
                <Typography variant='body2' sx={{ color: '#000000' }}>
                  {label}
                </Typography>
              }
            />
          </Box>
        );
      case 'date':
        return <TextField {...commonTextFieldProps} value={value ? dayjs(value).format('DD/MM/YYYY') : ''} />;
      case 'number':
      case 'text':
      default:
        return (
          <TextField
            {...commonTextFieldProps}
            value={value !== null && value !== undefined ? new Intl.NumberFormat('vi-VN').format(value) : ''}
          />
        );
    }
  };

  return (
    <div>
      <div className={`form-group ${className}`}>
        {type !== 'checkbox' && (
          <Typography
            variant='body2'
            component='label'
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: { xs: 'center', sm: 'flex-start' },
              color: '#000000',
              minWidth: 'fit-content',
              fontSize: '0.75rem',
              width: { xs: '100%', sm: 'auto' }
            }}
          >
            {label}
          </Typography>
        )}
        {renderValue()}
      </div>
    </div>
  );
};
