"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/danh-muc/mua-hang/gia-mua/page",{

/***/ "(app-pages-browser)/./src/hooks/queries/useGiaMua.ts":
/*!****************************************!*\
  !*** ./src/hooks/queries/useGiaMua.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useGiaMua: function() { return /* binding */ useGiaMua; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\n\n\n/**\r\n * Hook for managing GiaMua (Purchase Price) data\r\n *\r\n * This hook provides functions to fetch, create, update, and delete purchase prices.\r\n */ const useGiaMua = function() {\n    let initialGiaMuas = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];\n    const [giaMuas, setGiaMuas] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialGiaMuas);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const { entity } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const fetchGiaMuas = async ()=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.GIA_MUA, \"/\"));\n            setGiaMuas(response.data.results);\n        } catch (error) {\n            console.error(\"Error fetching purchase prices:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const addGiaMua = async (newGiaMua)=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) throw new Error(\"Entity slug is required\");\n        setIsLoading(true);\n        try {\n            let payload;\n            // Check if input is GiaMuaFormData and validate/convert it\n            if (isGiaMuaFormData(newGiaMua)) {\n                validateGiaMuaFormData(newGiaMua);\n                payload = convertFormDataToInput(newGiaMua);\n            } else {\n                payload = newGiaMua;\n            }\n            // Thêm header Content-Type để đảm bảo server hiểu đúng định dạng dữ liệu\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.GIA_MUA, \"/\"), payload, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const addedGiaMua = response.data;\n            setGiaMuas((prev)=>[\n                    ...prev,\n                    addedGiaMua\n                ]);\n            return addedGiaMua;\n        } catch (error) {\n            console.error(\"Error adding purchase price:\", error);\n            if (error.response) {\n                var _error_response_data, _error_response_data1;\n                // Tạo thông báo lỗi chi tiết\n                let errorMessage = ((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"\";\n                // Handle validation errors which come as an object with field names as keys\n                if (typeof error.response.data === \"object\" && !((_error_response_data1 = error.response.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.detail)) {\n                    const validationErrors = [];\n                    for(const field in error.response.data){\n                        if (Array.isArray(error.response.data[field])) {\n                            validationErrors.push(\"\".concat(field, \": \").concat(error.response.data[field].join(\", \")));\n                        }\n                    }\n                    if (validationErrors.length > 0) {\n                        errorMessage = validationErrors.join(\"\\n\");\n                    } else {\n                        errorMessage = JSON.stringify(error.response.data);\n                    }\n                }\n                if (!errorMessage) {\n                    errorMessage = error.message || \"Unknown error\";\n                }\n                throw new Error(errorMessage);\n            }\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateGiaMua = async (uuid, updatedGiaMua)=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) throw new Error(\"Entity slug is required\");\n        setIsLoading(true);\n        try {\n            let payload;\n            // Check if input is GiaMuaFormData and validate/convert it\n            if (isGiaMuaFormData(updatedGiaMua)) {\n                validateGiaMuaFormData(updatedGiaMua);\n                payload = convertFormDataToInput(updatedGiaMua);\n            } else {\n                payload = updatedGiaMua;\n            }\n            // Thêm header Content-Type để đảm bảo server hiểu đúng định dạng dữ liệu\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.GIA_MUA, \"/\").concat(uuid, \"/\"), payload, {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            const updatedGiaMuaData = response.data;\n            // Cập nhật state giaMuas với dữ liệu mới\n            setGiaMuas((prev)=>prev.map((giaMua)=>giaMua.uuid === updatedGiaMuaData.uuid ? updatedGiaMuaData : giaMua));\n            return updatedGiaMuaData;\n        } catch (error) {\n            console.error(\"Error updating purchase price:\", error);\n            if (error.response) {\n                var _error_response_data, _error_response_data1;\n                // Tạo thông báo lỗi chi tiết\n                let errorMessage = ((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || \"\";\n                // Handle validation errors which come as an object with field names as keys\n                if (typeof error.response.data === \"object\" && !((_error_response_data1 = error.response.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.detail)) {\n                    const validationErrors = [];\n                    for(const field in error.response.data){\n                        if (Array.isArray(error.response.data[field])) {\n                            validationErrors.push(\"\".concat(field, \": \").concat(error.response.data[field].join(\", \")));\n                        }\n                    }\n                    if (validationErrors.length > 0) {\n                        errorMessage = validationErrors.join(\"\\n\");\n                    } else {\n                        errorMessage = JSON.stringify(error.response.data);\n                    }\n                }\n                if (!errorMessage) {\n                    errorMessage = error.message || \"Unknown error\";\n                }\n                throw new Error(errorMessage);\n            }\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const deleteGiaMua = async (uuid)=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) throw new Error(\"Entity slug is required\");\n        setIsLoading(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(\"/entities/\".concat(entity.slug, \"/erp/purchase-prices/\").concat(uuid, \"/\"));\n            setGiaMuas((prev)=>prev.filter((giaMua)=>giaMua.uuid !== uuid));\n        } catch (error) {\n            console.error(\"Error deleting purchase price:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const refreshGiaMuas = async ()=>{\n        await fetchGiaMuas();\n    };\n    // Helper function to check if data is GiaMuaFormData\n    const isGiaMuaFormData = (data)=>{\n        return \"formData\" in data && \"vatTu\" in data && \"donViTinh\" in data && \"nhaCungCap\" in data && \"ngoaiTe\" in data;\n    };\n    // Helper function to validate GiaMuaFormData\n    const validateGiaMuaFormData = (data)=>{\n        var _data_vatTu, _data_donViTinh, _data_ngoaiTe;\n        // 1. Mã vật tư - bắt buộc\n        if (!((_data_vatTu = data.vatTu) === null || _data_vatTu === void 0 ? void 0 : _data_vatTu.uuid)) {\n            throw new Error(\"M\\xe3 vật tư kh\\xf4ng được bỏ trống\");\n        }\n        // 2. Đơn vị tính - bắt buộc (có thể có giá trị mặc định)\n        if (!((_data_donViTinh = data.donViTinh) === null || _data_donViTinh === void 0 ? void 0 : _data_donViTinh.uuid)) {\n            throw new Error(\"Đơn vị t\\xednh kh\\xf4ng được bỏ trống\");\n        }\n        // 3. Ngày hiệu lực - bắt buộc\n        if (!data.formData.ngay_hieu_luc || data.formData.ngay_hieu_luc.trim() === \"\") {\n            throw new Error(\"Ng\\xe0y hiệu lực kh\\xf4ng được bỏ trống\");\n        }\n        // 4. Nhà cung cấp - không bắt buộc (đã bỏ validation)\n        // if (!data.nhaCungCap?.uuid) {\n        //   throw new Error('Nhà cung cấp không được bỏ trống');\n        // }\n        // 5. Ngoại tệ - bắt buộc (có thể có giá trị mặc định)\n        if (!((_data_ngoaiTe = data.ngoaiTe) === null || _data_ngoaiTe === void 0 ? void 0 : _data_ngoaiTe.uuid)) {\n            throw new Error(\"Ngoại tệ kh\\xf4ng được bỏ trống\");\n        }\n    // 6. Số lượng từ - không bắt buộc (đã bỏ validation)\n    // if (\n    //   data.formData.so_luong_tu === '' ||\n    //   data.formData.so_luong_tu === null ||\n    //   data.formData.so_luong_tu === undefined\n    // ) {\n    //   throw new Error('Số lượng từ không được bỏ trống');\n    // }\n    // 7. Giá mua - không bắt buộc (không có validation)\n    };\n    // Helper function to convert GiaMuaFormData to GiaMuaInput\n    const convertFormDataToInput = (data)=>{\n        var _data_vatTu, _data_donViTinh, _data_nhaCungCap, _data_ngoaiTe;\n        return {\n            ma_vat_tu: ((_data_vatTu = data.vatTu) === null || _data_vatTu === void 0 ? void 0 : _data_vatTu.uuid) || \"\",\n            don_vi_tinh: ((_data_donViTinh = data.donViTinh) === null || _data_donViTinh === void 0 ? void 0 : _data_donViTinh.uuid) || \"\",\n            ngay_hieu_luc: data.formData.ngay_hieu_luc || null,\n            nha_cung_cap: ((_data_nhaCungCap = data.nhaCungCap) === null || _data_nhaCungCap === void 0 ? void 0 : _data_nhaCungCap.uuid) || \"\",\n            ngoai_te: ((_data_ngoaiTe = data.ngoaiTe) === null || _data_ngoaiTe === void 0 ? void 0 : _data_ngoaiTe.uuid) || \"\",\n            so_luong_tu: Number(data.formData.so_luong_tu) || 0,\n            gia_mua: Number(data.formData.gia_mua) || 0,\n            trang_thai: Number(data.formData.trang_thai) || 1\n        };\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetchGiaMuas();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        entity === null || entity === void 0 ? void 0 : entity.slug\n    ]);\n    return {\n        giaMuas,\n        isLoading,\n        addGiaMua,\n        updateGiaMua,\n        deleteGiaMua,\n        refreshGiaMuas\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy9xdWVyaWVzL3VzZUdpYU11YS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEM7QUFJTTtBQUNUO0FBQ2I7QUFtQjVCOzs7O0NBSUMsR0FDTSxNQUFNSyxZQUFZO1FBQUNDLGtGQUEyQixFQUFFO0lBQ3JELE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHUiwrQ0FBUUEsQ0FBV007SUFDakQsTUFBTSxDQUFDRyxXQUFXQyxhQUFhLEdBQUdWLCtDQUFRQSxDQUFVO0lBRXBELE1BQU0sRUFBRVcsTUFBTSxFQUFFLEdBQUdULCtEQUFPQTtJQUUxQixNQUFNVSxlQUFlO1FBQ25CLElBQUksRUFBQ0QsbUJBQUFBLDZCQUFBQSxPQUFRRSxJQUFJLEdBQUU7UUFFbkJILGFBQWE7UUFDYixJQUFJO1lBQ0YsTUFBTUksV0FBVyxNQUFNVixnREFBR0EsQ0FBQ1csR0FBRyxDQUFpQixhQUFnQ1osT0FBbkJRLE9BQU9FLElBQUksRUFBQyxTQUEwQixPQUFuQlYsa0RBQVVBLENBQUNhLE9BQU8sRUFBQztZQUNsR1IsV0FBV00sU0FBU0csSUFBSSxDQUFDQyxPQUFPO1FBQ2xDLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsbUNBQW1DQTtRQUNuRCxTQUFVO1lBQ1JULGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTVcsWUFBWSxPQUFPQztRQUN2QixJQUFJLEVBQUNYLG1CQUFBQSw2QkFBQUEsT0FBUUUsSUFBSSxHQUFFLE1BQU0sSUFBSVUsTUFBTTtRQUVuQ2IsYUFBYTtRQUNiLElBQUk7WUFDRixJQUFJYztZQUVKLDJEQUEyRDtZQUMzRCxJQUFJQyxpQkFBaUJILFlBQVk7Z0JBQy9CSSx1QkFBdUJKO2dCQUN2QkUsVUFBVUcsdUJBQXVCTDtZQUNuQyxPQUFPO2dCQUNMRSxVQUFVRjtZQUNaO1lBRUEseUVBQXlFO1lBQ3pFLE1BQU1SLFdBQVcsTUFBTVYsZ0RBQUdBLENBQUN3QixJQUFJLENBQVMsYUFBZ0N6QixPQUFuQlEsT0FBT0UsSUFBSSxFQUFDLFNBQTBCLE9BQW5CVixrREFBVUEsQ0FBQ2EsT0FBTyxFQUFDLE1BQUlRLFNBQVM7Z0JBQ3RHSyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUNBLE1BQU1DLGNBQWNoQixTQUFTRyxJQUFJO1lBRWpDVCxXQUFXdUIsQ0FBQUEsT0FBUTt1QkFBSUE7b0JBQU1EO2lCQUFZO1lBQ3pDLE9BQU9BO1FBQ1QsRUFBRSxPQUFPWCxPQUFZO1lBQ25CQyxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDQTtZQUM5QyxJQUFJQSxNQUFNTCxRQUFRLEVBQUU7b0JBRUNLLHNCQUc2QkE7Z0JBSmhELDZCQUE2QjtnQkFDN0IsSUFBSWEsZUFBZWIsRUFBQUEsdUJBQUFBLE1BQU1MLFFBQVEsQ0FBQ0csSUFBSSxjQUFuQkUsMkNBQUFBLHFCQUFxQmMsTUFBTSxLQUFJO2dCQUVsRCw0RUFBNEU7Z0JBQzVFLElBQUksT0FBT2QsTUFBTUwsUUFBUSxDQUFDRyxJQUFJLEtBQUssWUFBWSxHQUFDRSx3QkFBQUEsTUFBTUwsUUFBUSxDQUFDRyxJQUFJLGNBQW5CRSw0Q0FBQUEsc0JBQXFCYyxNQUFNLEdBQUU7b0JBQzNFLE1BQU1DLG1CQUFtQixFQUFFO29CQUMzQixJQUFLLE1BQU1DLFNBQVNoQixNQUFNTCxRQUFRLENBQUNHLElBQUksQ0FBRTt3QkFDdkMsSUFBSW1CLE1BQU1DLE9BQU8sQ0FBQ2xCLE1BQU1MLFFBQVEsQ0FBQ0csSUFBSSxDQUFDa0IsTUFBTSxHQUFHOzRCQUM3Q0QsaUJBQWlCSSxJQUFJLENBQUMsR0FBYW5CLE9BQVZnQixPQUFNLE1BQTBDLE9BQXRDaEIsTUFBTUwsUUFBUSxDQUFDRyxJQUFJLENBQUNrQixNQUFNLENBQUNJLElBQUksQ0FBQzt3QkFDckU7b0JBQ0Y7b0JBQ0EsSUFBSUwsaUJBQWlCTSxNQUFNLEdBQUcsR0FBRzt3QkFDL0JSLGVBQWVFLGlCQUFpQkssSUFBSSxDQUFDO29CQUN2QyxPQUFPO3dCQUNMUCxlQUFlUyxLQUFLQyxTQUFTLENBQUN2QixNQUFNTCxRQUFRLENBQUNHLElBQUk7b0JBQ25EO2dCQUNGO2dCQUVBLElBQUksQ0FBQ2UsY0FBYztvQkFDakJBLGVBQWViLE1BQU13QixPQUFPLElBQUk7Z0JBQ2xDO2dCQUVBLE1BQU0sSUFBSXBCLE1BQU1TO1lBQ2xCO1lBQ0EsTUFBTWI7UUFDUixTQUFVO1lBQ1JULGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTWtDLGVBQWUsT0FBT0MsTUFBY0M7UUFDeEMsSUFBSSxFQUFDbkMsbUJBQUFBLDZCQUFBQSxPQUFRRSxJQUFJLEdBQUUsTUFBTSxJQUFJVSxNQUFNO1FBRW5DYixhQUFhO1FBQ2IsSUFBSTtZQUNGLElBQUljO1lBRUosMkRBQTJEO1lBQzNELElBQUlDLGlCQUFpQnFCLGdCQUFnQjtnQkFDbkNwQix1QkFBdUJvQjtnQkFDdkJ0QixVQUFVRyx1QkFBdUJtQjtZQUNuQyxPQUFPO2dCQUNMdEIsVUFBVXNCO1lBQ1o7WUFFQSx5RUFBeUU7WUFDekUsTUFBTWhDLFdBQVcsTUFBTVYsZ0RBQUdBLENBQUMyQyxHQUFHLENBQVMsYUFBZ0M1QyxPQUFuQlEsT0FBT0UsSUFBSSxFQUFDLFNBQTZCZ0MsT0FBdEIxQyxrREFBVUEsQ0FBQ2EsT0FBTyxFQUFDLEtBQVEsT0FBTDZCLE1BQUssTUFBSXJCLFNBQVM7Z0JBQzdHSyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7WUFDRjtZQUNBLE1BQU1tQixvQkFBb0JsQyxTQUFTRyxJQUFJO1lBRXZDLHlDQUF5QztZQUN6Q1QsV0FBV3VCLENBQUFBLE9BQVFBLEtBQUtrQixHQUFHLENBQUNDLENBQUFBLFNBQVdBLE9BQU9MLElBQUksS0FBS0csa0JBQWtCSCxJQUFJLEdBQUdHLG9CQUFvQkU7WUFFcEcsT0FBT0Y7UUFDVCxFQUFFLE9BQU83QixPQUFZO1lBQ25CQyxRQUFRRCxLQUFLLENBQUMsa0NBQWtDQTtZQUNoRCxJQUFJQSxNQUFNTCxRQUFRLEVBQUU7b0JBRUNLLHNCQUc2QkE7Z0JBSmhELDZCQUE2QjtnQkFDN0IsSUFBSWEsZUFBZWIsRUFBQUEsdUJBQUFBLE1BQU1MLFFBQVEsQ0FBQ0csSUFBSSxjQUFuQkUsMkNBQUFBLHFCQUFxQmMsTUFBTSxLQUFJO2dCQUVsRCw0RUFBNEU7Z0JBQzVFLElBQUksT0FBT2QsTUFBTUwsUUFBUSxDQUFDRyxJQUFJLEtBQUssWUFBWSxHQUFDRSx3QkFBQUEsTUFBTUwsUUFBUSxDQUFDRyxJQUFJLGNBQW5CRSw0Q0FBQUEsc0JBQXFCYyxNQUFNLEdBQUU7b0JBQzNFLE1BQU1DLG1CQUFtQixFQUFFO29CQUMzQixJQUFLLE1BQU1DLFNBQVNoQixNQUFNTCxRQUFRLENBQUNHLElBQUksQ0FBRTt3QkFDdkMsSUFBSW1CLE1BQU1DLE9BQU8sQ0FBQ2xCLE1BQU1MLFFBQVEsQ0FBQ0csSUFBSSxDQUFDa0IsTUFBTSxHQUFHOzRCQUM3Q0QsaUJBQWlCSSxJQUFJLENBQUMsR0FBYW5CLE9BQVZnQixPQUFNLE1BQTBDLE9BQXRDaEIsTUFBTUwsUUFBUSxDQUFDRyxJQUFJLENBQUNrQixNQUFNLENBQUNJLElBQUksQ0FBQzt3QkFDckU7b0JBQ0Y7b0JBQ0EsSUFBSUwsaUJBQWlCTSxNQUFNLEdBQUcsR0FBRzt3QkFDL0JSLGVBQWVFLGlCQUFpQkssSUFBSSxDQUFDO29CQUN2QyxPQUFPO3dCQUNMUCxlQUFlUyxLQUFLQyxTQUFTLENBQUN2QixNQUFNTCxRQUFRLENBQUNHLElBQUk7b0JBQ25EO2dCQUNGO2dCQUVBLElBQUksQ0FBQ2UsY0FBYztvQkFDakJBLGVBQWViLE1BQU13QixPQUFPLElBQUk7Z0JBQ2xDO2dCQUVBLE1BQU0sSUFBSXBCLE1BQU1TO1lBQ2xCO1lBQ0EsTUFBTWI7UUFDUixTQUFVO1lBQ1JULGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTXlDLGVBQWUsT0FBT047UUFDMUIsSUFBSSxFQUFDbEMsbUJBQUFBLDZCQUFBQSxPQUFRRSxJQUFJLEdBQUUsTUFBTSxJQUFJVSxNQUFNO1FBRW5DYixhQUFhO1FBQ2IsSUFBSTtZQUNGLE1BQU1OLGdEQUFHQSxDQUFDZ0QsTUFBTSxDQUFDLGFBQWdEUCxPQUFuQ2xDLE9BQU9FLElBQUksRUFBQyx5QkFBNEIsT0FBTGdDLE1BQUs7WUFDdEVyQyxXQUFXdUIsQ0FBQUEsT0FBUUEsS0FBS3NCLE1BQU0sQ0FBQ0gsQ0FBQUEsU0FBVUEsT0FBT0wsSUFBSSxLQUFLQTtRQUMzRCxFQUFFLE9BQU8xQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxrQ0FBa0NBO1lBQ2hELE1BQU1BO1FBQ1IsU0FBVTtZQUNSVCxhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU00QyxpQkFBaUI7UUFDckIsTUFBTTFDO0lBQ1I7SUFFQSxxREFBcUQ7SUFDckQsTUFBTWEsbUJBQW1CLENBQUNSO1FBQ3hCLE9BQU8sY0FBY0EsUUFBUSxXQUFXQSxRQUFRLGVBQWVBLFFBQVEsZ0JBQWdCQSxRQUFRLGFBQWFBO0lBQzlHO0lBRUEsNkNBQTZDO0lBQzdDLE1BQU1TLHlCQUF5QixDQUFDVDtZQUV6QkEsYUFLQUEsaUJBZUFBO1FBckJMLDBCQUEwQjtRQUMxQixJQUFJLEdBQUNBLGNBQUFBLEtBQUtzQyxLQUFLLGNBQVZ0QyxrQ0FBQUEsWUFBWTRCLElBQUksR0FBRTtZQUNyQixNQUFNLElBQUl0QixNQUFNO1FBQ2xCO1FBRUEseURBQXlEO1FBQ3pELElBQUksR0FBQ04sa0JBQUFBLEtBQUt1QyxTQUFTLGNBQWR2QyxzQ0FBQUEsZ0JBQWdCNEIsSUFBSSxHQUFFO1lBQ3pCLE1BQU0sSUFBSXRCLE1BQU07UUFDbEI7UUFFQSw4QkFBOEI7UUFDOUIsSUFBSSxDQUFDTixLQUFLd0MsUUFBUSxDQUFDQyxhQUFhLElBQUl6QyxLQUFLd0MsUUFBUSxDQUFDQyxhQUFhLENBQUNDLElBQUksT0FBTyxJQUFJO1lBQzdFLE1BQU0sSUFBSXBDLE1BQU07UUFDbEI7UUFFQSxzREFBc0Q7UUFDdEQsZ0NBQWdDO1FBQ2hDLHlEQUF5RDtRQUN6RCxJQUFJO1FBRUosc0RBQXNEO1FBQ3RELElBQUksR0FBQ04sZ0JBQUFBLEtBQUsyQyxPQUFPLGNBQVozQyxvQ0FBQUEsY0FBYzRCLElBQUksR0FBRTtZQUN2QixNQUFNLElBQUl0QixNQUFNO1FBQ2xCO0lBRUEscURBQXFEO0lBQ3JELE9BQU87SUFDUCx3Q0FBd0M7SUFDeEMsMENBQTBDO0lBQzFDLDRDQUE0QztJQUM1QyxNQUFNO0lBQ04sd0RBQXdEO0lBQ3hELElBQUk7SUFFSixvREFBb0Q7SUFDdEQ7SUFFQSwyREFBMkQ7SUFDM0QsTUFBTUkseUJBQXlCLENBQUNWO1lBRWpCQSxhQUNFQSxpQkFFQ0Esa0JBQ0pBO1FBTFosT0FBTztZQUNMNEMsV0FBVzVDLEVBQUFBLGNBQUFBLEtBQUtzQyxLQUFLLGNBQVZ0QyxrQ0FBQUEsWUFBWTRCLElBQUksS0FBSTtZQUMvQmlCLGFBQWE3QyxFQUFBQSxrQkFBQUEsS0FBS3VDLFNBQVMsY0FBZHZDLHNDQUFBQSxnQkFBZ0I0QixJQUFJLEtBQUk7WUFDckNhLGVBQWV6QyxLQUFLd0MsUUFBUSxDQUFDQyxhQUFhLElBQUk7WUFDOUNLLGNBQWM5QyxFQUFBQSxtQkFBQUEsS0FBSytDLFVBQVUsY0FBZi9DLHVDQUFBQSxpQkFBaUI0QixJQUFJLEtBQUk7WUFDdkNvQixVQUFVaEQsRUFBQUEsZ0JBQUFBLEtBQUsyQyxPQUFPLGNBQVozQyxvQ0FBQUEsY0FBYzRCLElBQUksS0FBSTtZQUNoQ3FCLGFBQWFDLE9BQU9sRCxLQUFLd0MsUUFBUSxDQUFDUyxXQUFXLEtBQUs7WUFDbERFLFNBQVNELE9BQU9sRCxLQUFLd0MsUUFBUSxDQUFDVyxPQUFPLEtBQUs7WUFDMUNDLFlBQVlGLE9BQU9sRCxLQUFLd0MsUUFBUSxDQUFDWSxVQUFVLEtBQUs7UUFDbEQ7SUFDRjtJQUVBcEUsZ0RBQVNBLENBQUM7UUFDUlc7SUFDQSx1REFBdUQ7SUFDekQsR0FBRztRQUFDRCxtQkFBQUEsNkJBQUFBLE9BQVFFLElBQUk7S0FBQztJQUVqQixPQUFPO1FBQ0xOO1FBQ0FFO1FBQ0FZO1FBQ0F1QjtRQUNBTztRQUNBRztJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvaG9va3MvcXVlcmllcy91c2VHaWFNdWEudHM/MzlmMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBHaWFNdWEsIEdpYU11YUlucHV0LCBHaWFNdWFSZXNwb25zZSB9IGZyb20gJ0AvdHlwZXMvc2NoZW1hcy9naWEtbXVhLnR5cGUnO1xyXG5pbXBvcnQgeyBGb3JtVmFsdWVzIH0gZnJvbSAnQC9mZWF0dXJlcy9kYW5oLW11Yy9tdWEtaGFuZy9naWEtbXVhL3NjaGVtYXMnO1xyXG5pbXBvcnQgeyBWYXRUdSwgRG9uVmlUaW5oLCBEb2lUdW9uZywgTmdvYWlUZSB9IGZyb20gJ0AvdHlwZXMvc2NoZW1hcyc7XHJcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2NvbnRleHRzL2F1dGgtY29udGV4dCc7XHJcbmltcG9ydCB7IFFVRVJZX0tFWVMgfSBmcm9tICdAL2NvbnN0YW50cyc7XHJcbmltcG9ydCBhcGkgZnJvbSAnQC9saWIvYXBpJztcclxuXHJcbmludGVyZmFjZSBHaWFNdWFGb3JtRGF0YSB7XHJcbiAgZm9ybURhdGE6IEZvcm1WYWx1ZXM7XHJcbiAgdmF0VHU6IFZhdFR1IHwgbnVsbDtcclxuICBkb25WaVRpbmg6IERvblZpVGluaCB8IG51bGw7XHJcbiAgbmhhQ3VuZ0NhcDogRG9pVHVvbmcgfCBudWxsO1xyXG4gIG5nb2FpVGU6IE5nb2FpVGUgfCBudWxsO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgVXNlR2lhTXVhUmV0dXJuIHtcclxuICBnaWFNdWFzOiBHaWFNdWFbXTtcclxuICBpc0xvYWRpbmc6IGJvb2xlYW47XHJcbiAgYWRkR2lhTXVhOiAobmV3R2lhTXVhOiBHaWFNdWFJbnB1dCB8IEdpYU11YUZvcm1EYXRhKSA9PiBQcm9taXNlPEdpYU11YT47XHJcbiAgdXBkYXRlR2lhTXVhOiAodXVpZDogc3RyaW5nLCB1cGRhdGVkR2lhTXVhOiBHaWFNdWFJbnB1dCB8IEdpYU11YUZvcm1EYXRhKSA9PiBQcm9taXNlPEdpYU11YT47XHJcbiAgZGVsZXRlR2lhTXVhOiAodXVpZDogc3RyaW5nKSA9PiBQcm9taXNlPHZvaWQ+O1xyXG4gIHJlZnJlc2hHaWFNdWFzOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xyXG59XHJcblxyXG4vKipcclxuICogSG9vayBmb3IgbWFuYWdpbmcgR2lhTXVhIChQdXJjaGFzZSBQcmljZSkgZGF0YVxyXG4gKlxyXG4gKiBUaGlzIGhvb2sgcHJvdmlkZXMgZnVuY3Rpb25zIHRvIGZldGNoLCBjcmVhdGUsIHVwZGF0ZSwgYW5kIGRlbGV0ZSBwdXJjaGFzZSBwcmljZXMuXHJcbiAqL1xyXG5leHBvcnQgY29uc3QgdXNlR2lhTXVhID0gKGluaXRpYWxHaWFNdWFzOiBHaWFNdWFbXSA9IFtdKTogVXNlR2lhTXVhUmV0dXJuID0+IHtcclxuICBjb25zdCBbZ2lhTXVhcywgc2V0R2lhTXVhc10gPSB1c2VTdGF0ZTxHaWFNdWFbXT4oaW5pdGlhbEdpYU11YXMpO1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZTxib29sZWFuPih0cnVlKTtcclxuXHJcbiAgY29uc3QgeyBlbnRpdHkgfSA9IHVzZUF1dGgoKTtcclxuXHJcbiAgY29uc3QgZmV0Y2hHaWFNdWFzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKCFlbnRpdHk/LnNsdWcpIHJldHVybjtcclxuXHJcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5nZXQ8R2lhTXVhUmVzcG9uc2U+KGAvZW50aXRpZXMvJHtlbnRpdHkuc2x1Z30vZXJwLyR7UVVFUllfS0VZUy5HSUFfTVVBfS9gKTtcclxuICAgICAgc2V0R2lhTXVhcyhyZXNwb25zZS5kYXRhLnJlc3VsdHMpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgcHVyY2hhc2UgcHJpY2VzOicsIGVycm9yKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgYWRkR2lhTXVhID0gYXN5bmMgKG5ld0dpYU11YTogR2lhTXVhSW5wdXQgfCBHaWFNdWFGb3JtRGF0YSk6IFByb21pc2U8R2lhTXVhPiA9PiB7XHJcbiAgICBpZiAoIWVudGl0eT8uc2x1ZykgdGhyb3cgbmV3IEVycm9yKCdFbnRpdHkgc2x1ZyBpcyByZXF1aXJlZCcpO1xyXG5cclxuICAgIHNldElzTG9hZGluZyh0cnVlKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIGxldCBwYXlsb2FkOiBHaWFNdWFJbnB1dDtcclxuXHJcbiAgICAgIC8vIENoZWNrIGlmIGlucHV0IGlzIEdpYU11YUZvcm1EYXRhIGFuZCB2YWxpZGF0ZS9jb252ZXJ0IGl0XHJcbiAgICAgIGlmIChpc0dpYU11YUZvcm1EYXRhKG5ld0dpYU11YSkpIHtcclxuICAgICAgICB2YWxpZGF0ZUdpYU11YUZvcm1EYXRhKG5ld0dpYU11YSk7XHJcbiAgICAgICAgcGF5bG9hZCA9IGNvbnZlcnRGb3JtRGF0YVRvSW5wdXQobmV3R2lhTXVhKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBwYXlsb2FkID0gbmV3R2lhTXVhO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBUaMOqbSBoZWFkZXIgQ29udGVudC1UeXBlIMSR4buDIMSR4bqjbSBi4bqjbyBzZXJ2ZXIgaGnhu4N1IMSRw7puZyDEkeG7i25oIGThuqFuZyBk4buvIGxp4buHdVxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5wb3N0PEdpYU11YT4oYC9lbnRpdGllcy8ke2VudGl0eS5zbHVnfS9lcnAvJHtRVUVSWV9LRVlTLkdJQV9NVUF9L2AsIHBheWxvYWQsIHtcclxuICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXHJcbiAgICAgICAgfVxyXG4gICAgICB9KTtcclxuICAgICAgY29uc3QgYWRkZWRHaWFNdWEgPSByZXNwb25zZS5kYXRhO1xyXG5cclxuICAgICAgc2V0R2lhTXVhcyhwcmV2ID0+IFsuLi5wcmV2LCBhZGRlZEdpYU11YV0pO1xyXG4gICAgICByZXR1cm4gYWRkZWRHaWFNdWE7XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFkZGluZyBwdXJjaGFzZSBwcmljZTonLCBlcnJvcik7XHJcbiAgICAgIGlmIChlcnJvci5yZXNwb25zZSkge1xyXG4gICAgICAgIC8vIFThuqFvIHRow7RuZyBiw6FvIGzhu5dpIGNoaSB0aeG6v3RcclxuICAgICAgICBsZXQgZXJyb3JNZXNzYWdlID0gZXJyb3IucmVzcG9uc2UuZGF0YT8uZGV0YWlsIHx8ICcnO1xyXG5cclxuICAgICAgICAvLyBIYW5kbGUgdmFsaWRhdGlvbiBlcnJvcnMgd2hpY2ggY29tZSBhcyBhbiBvYmplY3Qgd2l0aCBmaWVsZCBuYW1lcyBhcyBrZXlzXHJcbiAgICAgICAgaWYgKHR5cGVvZiBlcnJvci5yZXNwb25zZS5kYXRhID09PSAnb2JqZWN0JyAmJiAhZXJyb3IucmVzcG9uc2UuZGF0YT8uZGV0YWlsKSB7XHJcbiAgICAgICAgICBjb25zdCB2YWxpZGF0aW9uRXJyb3JzID0gW107XHJcbiAgICAgICAgICBmb3IgKGNvbnN0IGZpZWxkIGluIGVycm9yLnJlc3BvbnNlLmRhdGEpIHtcclxuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZXJyb3IucmVzcG9uc2UuZGF0YVtmaWVsZF0pKSB7XHJcbiAgICAgICAgICAgICAgdmFsaWRhdGlvbkVycm9ycy5wdXNoKGAke2ZpZWxkfTogJHtlcnJvci5yZXNwb25zZS5kYXRhW2ZpZWxkXS5qb2luKCcsICcpfWApO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBpZiAodmFsaWRhdGlvbkVycm9ycy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9IHZhbGlkYXRpb25FcnJvcnMuam9pbignXFxuJyk7XHJcbiAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBKU09OLnN0cmluZ2lmeShlcnJvci5yZXNwb25zZS5kYXRhKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmICghZXJyb3JNZXNzYWdlKSB7XHJcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlIHx8ICdVbmtub3duIGVycm9yJztcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvck1lc3NhZ2UpO1xyXG4gICAgICB9XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCB1cGRhdGVHaWFNdWEgPSBhc3luYyAodXVpZDogc3RyaW5nLCB1cGRhdGVkR2lhTXVhOiBHaWFNdWFJbnB1dCB8IEdpYU11YUZvcm1EYXRhKTogUHJvbWlzZTxHaWFNdWE+ID0+IHtcclxuICAgIGlmICghZW50aXR5Py5zbHVnKSB0aHJvdyBuZXcgRXJyb3IoJ0VudGl0eSBzbHVnIGlzIHJlcXVpcmVkJyk7XHJcblxyXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgbGV0IHBheWxvYWQ6IEdpYU11YUlucHV0O1xyXG5cclxuICAgICAgLy8gQ2hlY2sgaWYgaW5wdXQgaXMgR2lhTXVhRm9ybURhdGEgYW5kIHZhbGlkYXRlL2NvbnZlcnQgaXRcclxuICAgICAgaWYgKGlzR2lhTXVhRm9ybURhdGEodXBkYXRlZEdpYU11YSkpIHtcclxuICAgICAgICB2YWxpZGF0ZUdpYU11YUZvcm1EYXRhKHVwZGF0ZWRHaWFNdWEpO1xyXG4gICAgICAgIHBheWxvYWQgPSBjb252ZXJ0Rm9ybURhdGFUb0lucHV0KHVwZGF0ZWRHaWFNdWEpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHBheWxvYWQgPSB1cGRhdGVkR2lhTXVhO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBUaMOqbSBoZWFkZXIgQ29udGVudC1UeXBlIMSR4buDIMSR4bqjbSBi4bqjbyBzZXJ2ZXIgaGnhu4N1IMSRw7puZyDEkeG7i25oIGThuqFuZyBk4buvIGxp4buHdVxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5wdXQ8R2lhTXVhPihgL2VudGl0aWVzLyR7ZW50aXR5LnNsdWd9L2VycC8ke1FVRVJZX0tFWVMuR0lBX01VQX0vJHt1dWlkfS9gLCBwYXlsb2FkLCB7XHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJ1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICAgIGNvbnN0IHVwZGF0ZWRHaWFNdWFEYXRhID0gcmVzcG9uc2UuZGF0YTtcclxuXHJcbiAgICAgIC8vIEPhuq1wIG5o4bqtdCBzdGF0ZSBnaWFNdWFzIHbhu5tpIGThu68gbGnhu4d1IG3hu5tpXHJcbiAgICAgIHNldEdpYU11YXMocHJldiA9PiBwcmV2Lm1hcChnaWFNdWEgPT4gKGdpYU11YS51dWlkID09PSB1cGRhdGVkR2lhTXVhRGF0YS51dWlkID8gdXBkYXRlZEdpYU11YURhdGEgOiBnaWFNdWEpKSk7XHJcblxyXG4gICAgICByZXR1cm4gdXBkYXRlZEdpYU11YURhdGE7XHJcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIHB1cmNoYXNlIHByaWNlOicsIGVycm9yKTtcclxuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlKSB7XHJcbiAgICAgICAgLy8gVOG6oW8gdGjDtG5nIGLDoW8gbOG7l2kgY2hpIHRp4bq/dFxyXG4gICAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSBlcnJvci5yZXNwb25zZS5kYXRhPy5kZXRhaWwgfHwgJyc7XHJcblxyXG4gICAgICAgIC8vIEhhbmRsZSB2YWxpZGF0aW9uIGVycm9ycyB3aGljaCBjb21lIGFzIGFuIG9iamVjdCB3aXRoIGZpZWxkIG5hbWVzIGFzIGtleXNcclxuICAgICAgICBpZiAodHlwZW9mIGVycm9yLnJlc3BvbnNlLmRhdGEgPT09ICdvYmplY3QnICYmICFlcnJvci5yZXNwb25zZS5kYXRhPy5kZXRhaWwpIHtcclxuICAgICAgICAgIGNvbnN0IHZhbGlkYXRpb25FcnJvcnMgPSBbXTtcclxuICAgICAgICAgIGZvciAoY29uc3QgZmllbGQgaW4gZXJyb3IucmVzcG9uc2UuZGF0YSkge1xyXG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShlcnJvci5yZXNwb25zZS5kYXRhW2ZpZWxkXSkpIHtcclxuICAgICAgICAgICAgICB2YWxpZGF0aW9uRXJyb3JzLnB1c2goYCR7ZmllbGR9OiAke2Vycm9yLnJlc3BvbnNlLmRhdGFbZmllbGRdLmpvaW4oJywgJyl9YCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICAgIGlmICh2YWxpZGF0aW9uRXJyb3JzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgZXJyb3JNZXNzYWdlID0gdmFsaWRhdGlvbkVycm9ycy5qb2luKCdcXG4nKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGVycm9yTWVzc2FnZSA9IEpTT04uc3RyaW5naWZ5KGVycm9yLnJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKCFlcnJvck1lc3NhZ2UpIHtcclxuICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yLm1lc3NhZ2UgfHwgJ1Vua25vd24gZXJyb3InO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yTWVzc2FnZSk7XHJcbiAgICAgIH1cclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGRlbGV0ZUdpYU11YSA9IGFzeW5jICh1dWlkOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+ID0+IHtcclxuICAgIGlmICghZW50aXR5Py5zbHVnKSB0aHJvdyBuZXcgRXJyb3IoJ0VudGl0eSBzbHVnIGlzIHJlcXVpcmVkJyk7XHJcblxyXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgYXdhaXQgYXBpLmRlbGV0ZShgL2VudGl0aWVzLyR7ZW50aXR5LnNsdWd9L2VycC9wdXJjaGFzZS1wcmljZXMvJHt1dWlkfS9gKTtcclxuICAgICAgc2V0R2lhTXVhcyhwcmV2ID0+IHByZXYuZmlsdGVyKGdpYU11YSA9PiBnaWFNdWEudXVpZCAhPT0gdXVpZCkpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVsZXRpbmcgcHVyY2hhc2UgcHJpY2U6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgcmVmcmVzaEdpYU11YXMgPSBhc3luYyAoKTogUHJvbWlzZTx2b2lkPiA9PiB7XHJcbiAgICBhd2FpdCBmZXRjaEdpYU11YXMoKTtcclxuICB9O1xyXG5cclxuICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gY2hlY2sgaWYgZGF0YSBpcyBHaWFNdWFGb3JtRGF0YVxyXG4gIGNvbnN0IGlzR2lhTXVhRm9ybURhdGEgPSAoZGF0YTogR2lhTXVhSW5wdXQgfCBHaWFNdWFGb3JtRGF0YSk6IGRhdGEgaXMgR2lhTXVhRm9ybURhdGEgPT4ge1xyXG4gICAgcmV0dXJuICdmb3JtRGF0YScgaW4gZGF0YSAmJiAndmF0VHUnIGluIGRhdGEgJiYgJ2RvblZpVGluaCcgaW4gZGF0YSAmJiAnbmhhQ3VuZ0NhcCcgaW4gZGF0YSAmJiAnbmdvYWlUZScgaW4gZGF0YTtcclxuICB9O1xyXG5cclxuICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gdmFsaWRhdGUgR2lhTXVhRm9ybURhdGFcclxuICBjb25zdCB2YWxpZGF0ZUdpYU11YUZvcm1EYXRhID0gKGRhdGE6IEdpYU11YUZvcm1EYXRhKTogdm9pZCA9PiB7XHJcbiAgICAvLyAxLiBNw6MgduG6rXQgdMawIC0gYuG6r3QgYnXhu5ljXHJcbiAgICBpZiAoIWRhdGEudmF0VHU/LnV1aWQpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdNw6MgduG6rXQgdMawIGtow7RuZyDEkcaw4bujYyBi4buPIHRy4buRbmcnKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyAyLiDEkMahbiB24buLIHTDrW5oIC0gYuG6r3QgYnXhu5ljIChjw7MgdGjhu4MgY8OzIGdpw6EgdHLhu4sgbeG6t2MgxJHhu4tuaClcclxuICAgIGlmICghZGF0YS5kb25WaVRpbmg/LnV1aWQpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCfEkMahbiB24buLIHTDrW5oIGtow7RuZyDEkcaw4bujYyBi4buPIHRy4buRbmcnKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyAzLiBOZ8OgeSBoaeG7h3UgbOG7sWMgLSBi4bqvdCBideG7mWNcclxuICAgIGlmICghZGF0YS5mb3JtRGF0YS5uZ2F5X2hpZXVfbHVjIHx8IGRhdGEuZm9ybURhdGEubmdheV9oaWV1X2x1Yy50cmltKCkgPT09ICcnKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignTmfDoHkgaGnhu4d1IGzhu7FjIGtow7RuZyDEkcaw4bujYyBi4buPIHRy4buRbmcnKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyA0LiBOaMOgIGN1bmcgY+G6pXAgLSBraMO0bmcgYuG6r3QgYnXhu5ljICjEkcOjIGLhu48gdmFsaWRhdGlvbilcclxuICAgIC8vIGlmICghZGF0YS5uaGFDdW5nQ2FwPy51dWlkKSB7XHJcbiAgICAvLyAgIHRocm93IG5ldyBFcnJvcignTmjDoCBjdW5nIGPhuqVwIGtow7RuZyDEkcaw4bujYyBi4buPIHRy4buRbmcnKTtcclxuICAgIC8vIH1cclxuXHJcbiAgICAvLyA1LiBOZ2/huqFpIHThu4cgLSBi4bqvdCBideG7mWMgKGPDsyB0aOG7gyBjw7MgZ2nDoSB0cuG7iyBt4bq3YyDEkeG7i25oKVxyXG4gICAgaWYgKCFkYXRhLm5nb2FpVGU/LnV1aWQpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdOZ2/huqFpIHThu4cga2jDtG5nIMSRxrDhu6NjIGLhu48gdHLhu5FuZycpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIDYuIFPhu5EgbMaw4bujbmcgdOG7qyAtIGtow7RuZyBi4bqvdCBideG7mWMgKMSRw6MgYuG7jyB2YWxpZGF0aW9uKVxyXG4gICAgLy8gaWYgKFxyXG4gICAgLy8gICBkYXRhLmZvcm1EYXRhLnNvX2x1b25nX3R1ID09PSAnJyB8fFxyXG4gICAgLy8gICBkYXRhLmZvcm1EYXRhLnNvX2x1b25nX3R1ID09PSBudWxsIHx8XHJcbiAgICAvLyAgIGRhdGEuZm9ybURhdGEuc29fbHVvbmdfdHUgPT09IHVuZGVmaW5lZFxyXG4gICAgLy8gKSB7XHJcbiAgICAvLyAgIHRocm93IG5ldyBFcnJvcignU+G7kSBsxrDhu6NuZyB04burIGtow7RuZyDEkcaw4bujYyBi4buPIHRy4buRbmcnKTtcclxuICAgIC8vIH1cclxuXHJcbiAgICAvLyA3LiBHacOhIG11YSAtIGtow7RuZyBi4bqvdCBideG7mWMgKGtow7RuZyBjw7MgdmFsaWRhdGlvbilcclxuICB9O1xyXG5cclxuICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gY29udmVydCBHaWFNdWFGb3JtRGF0YSB0byBHaWFNdWFJbnB1dFxyXG4gIGNvbnN0IGNvbnZlcnRGb3JtRGF0YVRvSW5wdXQgPSAoZGF0YTogR2lhTXVhRm9ybURhdGEpOiBHaWFNdWFJbnB1dCA9PiB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBtYV92YXRfdHU6IGRhdGEudmF0VHU/LnV1aWQgfHwgJycsXHJcbiAgICAgIGRvbl92aV90aW5oOiBkYXRhLmRvblZpVGluaD8udXVpZCB8fCAnJyxcclxuICAgICAgbmdheV9oaWV1X2x1YzogZGF0YS5mb3JtRGF0YS5uZ2F5X2hpZXVfbHVjIHx8IG51bGwsXHJcbiAgICAgIG5oYV9jdW5nX2NhcDogZGF0YS5uaGFDdW5nQ2FwPy51dWlkIHx8ICcnLFxyXG4gICAgICBuZ29haV90ZTogZGF0YS5uZ29haVRlPy51dWlkIHx8ICcnLFxyXG4gICAgICBzb19sdW9uZ190dTogTnVtYmVyKGRhdGEuZm9ybURhdGEuc29fbHVvbmdfdHUpIHx8IDAsXHJcbiAgICAgIGdpYV9tdWE6IE51bWJlcihkYXRhLmZvcm1EYXRhLmdpYV9tdWEpIHx8IDAsXHJcbiAgICAgIHRyYW5nX3RoYWk6IE51bWJlcihkYXRhLmZvcm1EYXRhLnRyYW5nX3RoYWkpIHx8IDFcclxuICAgIH07XHJcbiAgfTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGZldGNoR2lhTXVhcygpO1xyXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFtlbnRpdHk/LnNsdWddKTtcclxuXHJcbiAgcmV0dXJuIHtcclxuICAgIGdpYU11YXMsXHJcbiAgICBpc0xvYWRpbmcsXHJcbiAgICBhZGRHaWFNdWEsXHJcbiAgICB1cGRhdGVHaWFNdWEsXHJcbiAgICBkZWxldGVHaWFNdWEsXHJcbiAgICByZWZyZXNoR2lhTXVhc1xyXG4gIH07XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUF1dGgiLCJRVUVSWV9LRVlTIiwiYXBpIiwidXNlR2lhTXVhIiwiaW5pdGlhbEdpYU11YXMiLCJnaWFNdWFzIiwic2V0R2lhTXVhcyIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImVudGl0eSIsImZldGNoR2lhTXVhcyIsInNsdWciLCJyZXNwb25zZSIsImdldCIsIkdJQV9NVUEiLCJkYXRhIiwicmVzdWx0cyIsImVycm9yIiwiY29uc29sZSIsImFkZEdpYU11YSIsIm5ld0dpYU11YSIsIkVycm9yIiwicGF5bG9hZCIsImlzR2lhTXVhRm9ybURhdGEiLCJ2YWxpZGF0ZUdpYU11YUZvcm1EYXRhIiwiY29udmVydEZvcm1EYXRhVG9JbnB1dCIsInBvc3QiLCJoZWFkZXJzIiwiYWRkZWRHaWFNdWEiLCJwcmV2IiwiZXJyb3JNZXNzYWdlIiwiZGV0YWlsIiwidmFsaWRhdGlvbkVycm9ycyIsImZpZWxkIiwiQXJyYXkiLCJpc0FycmF5IiwicHVzaCIsImpvaW4iLCJsZW5ndGgiLCJKU09OIiwic3RyaW5naWZ5IiwibWVzc2FnZSIsInVwZGF0ZUdpYU11YSIsInV1aWQiLCJ1cGRhdGVkR2lhTXVhIiwicHV0IiwidXBkYXRlZEdpYU11YURhdGEiLCJtYXAiLCJnaWFNdWEiLCJkZWxldGVHaWFNdWEiLCJkZWxldGUiLCJmaWx0ZXIiLCJyZWZyZXNoR2lhTXVhcyIsInZhdFR1IiwiZG9uVmlUaW5oIiwiZm9ybURhdGEiLCJuZ2F5X2hpZXVfbHVjIiwidHJpbSIsIm5nb2FpVGUiLCJtYV92YXRfdHUiLCJkb25fdmlfdGluaCIsIm5oYV9jdW5nX2NhcCIsIm5oYUN1bmdDYXAiLCJuZ29haV90ZSIsInNvX2x1b25nX3R1IiwiTnVtYmVyIiwiZ2lhX211YSIsInRyYW5nX3RoYWkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/queries/useGiaMua.ts\n"));

/***/ })

});