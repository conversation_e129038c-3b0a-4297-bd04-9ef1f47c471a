import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface DescriptionFieldProps {
  name?: string;
  label?: string;
  className?: string;
  labelClassName?: string;
  inputClassName?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
}

export const DescriptionField: React.FC<DescriptionFieldProps> = ({
  name = 'description',
  label = 'Diễn giải',
  className = 'grid grid-cols-[150px,1fr] items-center',
  labelClassName,
  inputClassName,
  disabled = false,
  formMode = 'add'
}) => {
  return (
    <div className={className}>
      <Label className={cn('mt-3 flex items-center text-left text-sm font-normal sm:mb-0', labelClassName)}>
        {label}
      </Label>
      <FormField
        name={name}
        type='text'
        label=''
        className={inputClassName}
        disabled={disabled || formMode === 'view'}
      />
    </div>
  );
};

export default DescriptionField;
