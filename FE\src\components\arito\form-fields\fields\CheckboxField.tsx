import { Controller } from 'react-hook-form';
import React from 'react';
import { InputFieldWrapper, ErrorMessage, CheckboxContainer } from '../components/InputFieldWrapper';
import { FormRow } from '../components/FormRow';
import { CommonFieldProps } from '../types';

export const CheckboxField: React.FC<CommonFieldProps> = ({ field, maxLabelWidth, isViewMode }) => {
  const { key, label, required, disabled = isViewMode, valueRender, labelPosition = 'left' } = field;

  // Label style
  const labelStyle = {
    width: labelPosition === 'left' ? `${maxLabelWidth}px` : 'auto',
    minWidth: labelPosition === 'left' ? `${maxLabelWidth}px` : 'auto',
    fontSize: '12px',
    fontWeight: 400,
    marginRight: '8px',
    whiteSpace: 'nowrap' as const,
    overflow: 'visible' as const,
    display: 'inline-block'
  };

  // Checkbox wrapper style for right-aligned labels
  const rightLabelWrapperStyle = {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    paddingLeft: `${maxLabelWidth}px`,
    gap: '8px'
  };

  // Checkbox input style
  const checkboxStyle: React.CSSProperties = {
    height: '14px',
    width: '14px',
    margin: 0,
    flexShrink: 0,
    opacity: 1,
    accentColor: disabled ? 'black' : undefined,
    filter: disabled ? 'none' : undefined,
    WebkitAppearance: disabled ? 'checkbox' : undefined,
    appearance: disabled ? ('checkbox' as const) : undefined
  };

  // Label content for left position
  const labelContent = (
    <span style={labelStyle}>
      {label}
      {!disabled && required && <span style={{ color: 'red' }}>*</span>}
    </span>
  );

  if (labelPosition === 'right') {
    return (
      <FormRow>
        <div style={rightLabelWrapperStyle}>
          <Controller
            name={key}
            render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
              <>
                {valueRender ? (
                  <input type='checkbox' checked={!!valueRender(value)} disabled={true} style={checkboxStyle} />
                ) : (
                  <input
                    type='checkbox'
                    checked={!!value}
                    onChange={e => onChange(e.target.checked)}
                    disabled={disabled}
                    ref={ref}
                    style={checkboxStyle}
                  />
                )}
                <span style={{ fontSize: '12px', fontWeight: 400 }}>{label}</span>
                {error && <ErrorMessage>{error.message}</ErrorMessage>}
              </>
            )}
          />
        </div>
      </FormRow>
    );
  }

  return (
    <FormRow>
      {labelContent}
      <InputFieldWrapper style={{ width: 'auto' }}>
        <CheckboxContainer>
          <Controller
            name={key}
            render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
              <>
                {valueRender ? (
                  <input type='checkbox' checked={!!valueRender(value)} disabled={true} style={checkboxStyle} />
                ) : (
                  <input
                    type='checkbox'
                    checked={!!value}
                    onChange={e => onChange(e.target.checked)}
                    disabled={disabled}
                    ref={ref}
                    style={checkboxStyle}
                  />
                )}
                {error && <ErrorMessage>{error.message}</ErrorMessage>}
              </>
            )}
          />
        </CheckboxContainer>
      </InputFieldWrapper>
    </FormRow>
  );
};

export default CheckboxField;
