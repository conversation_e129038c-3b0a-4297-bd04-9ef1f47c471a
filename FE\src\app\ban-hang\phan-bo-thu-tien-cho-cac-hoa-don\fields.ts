import { FieldConfig } from '../../../components/arito/arito-form-fields';

export const invoiceReceiptAllocationFilterFields: FieldConfig[] = [
  {
    key: 'ngay_tu_den',
    name: 'ngay_tu_den',
    label: '<PERSON><PERSON><PERSON> từ/đến',
    type: 'daterange',
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'ngoai_te',
    name: 'ngoai_te',
    label: 'Ngoại tệ',
    type: 'search',
    searchModalTitle: '<PERSON>h mục ngoại tệ',
    searchColumns: [
      { field: 'ma_ngoai_te', headerName: 'Mã ngoại tệ', width: 100 },
      { field: 'ten_ngoai_te', headerName: 'Tên ngoại tệ', width: 200 }
    ],
    docType: 'Currency',
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'tai_khoan',
    name: 'tai_khoan',
    label: 'T<PERSON><PERSON> khoản',
    type: 'search',
    searchModalTitle: '<PERSON>h mục tài khoản',
    searchColumns: [
      { field: 'ma_tai_khoan', headerName: 'Mã tài khoản', width: 100 },
      { field: 'ten_tai_khoan', headerName: 'Tên tài khoản', width: 300 },
      { field: 'tai_khoan_me', headerName: 'Tài khoản mẹ', width: 100 },
      { field: 'tk_so_cai', headerName: 'Tk số cái', width: 100 },
      { field: 'tk_chi_tiet', headerName: 'Tk chi tiết', width: 100 },
      { field: 'bac_tk', headerName: 'Bậc tk', width: 100 }
    ],
    docType: 'Account',
    gridPosition: {
      row: 2,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'ma_khach_hang',
    name: 'ma_khach_hang',
    label: 'Mã khách hàng',
    type: 'search',
    searchModalTitle: 'Danh mục khách hàng',
    searchColumns: [
      { field: 'code', headerName: 'Mã', width: 100 },
      { field: 'name', headerName: 'Tên khách hàng', width: 200 }
    ],
    docType: 'Customer',
    gridPosition: {
      row: 3,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'nhom_khach_hang',
    name: 'nhom_khach_hang',
    label: 'Nhóm khách hàng',
    type: 'compound',
    inputs: [
      {
        key: 'nhom_khach_hang_1',
        name: 'nhom_khach_hang_1',
        type: 'search',
        widthPercentage: 33.7,
        searchModalTitle: 'Danh mục nhóm khách hàng 1',
        showSearchDisplay: false,
        searchColumns: [
          { field: 'code', headerName: 'Mã', width: 100 },
          { field: 'name', headerName: 'Tên nhóm', width: 200 }
        ],
        docType: 'CustomerGroup'
      },
      {
        key: 'nhom_khach_hang_2',
        name: 'nhom_khach_hang_2',
        type: 'search',
        widthPercentage: 33.3,
        searchModalTitle: 'Danh mục nhóm khách hàng 2',
        showSearchDisplay: false,
        searchColumns: [
          { field: 'code', headerName: 'Mã', width: 100 },
          { field: 'name', headerName: 'Tên nhóm', width: 200 }
        ],
        docType: 'CustomerGroup'
      },
      {
        key: 'nhom_khach_hang_3',
        name: 'nhom_khach_hang_3',
        type: 'search',
        widthPercentage: 33,
        searchModalTitle: 'Danh mục nhóm khách hàng 3',
        showSearchDisplay: false,
        searchColumns: [
          { field: 'code', headerName: 'Mã', width: 100 },
          { field: 'name', headerName: 'Tên nhóm', width: 200 }
        ],
        docType: 'CustomerGroup'
      }
    ],
    gridPosition: {
      row: 4,
      col: 0,
      colSpan: 12
    }
  },
  {
    key: 'don_vi',
    name: 'don_vi',
    label: 'Đơn vị',
    type: 'search',
    searchModalTitle: 'Danh mục đơn vị',
    searchColumns: [
      { field: 'code', headerName: 'Mã', width: 100 },
      { field: 'name', headerName: 'Tên đơn vị', width: 300 }
    ],
    docType: 'Unit',
    gridPosition: {
      row: 5,
      col: 0,
      colSpan: 12
    }
  },
  {
    key: 'loai_phieu_thu',
    name: 'loai_phieu_thu',
    label: 'Loại phiếu thu',
    type: 'select',
    options: [
      { value: '0', label: 'Tất cả' },
      { value: '1', label: 'Chưa phân bổ hết' },
      { value: '2', label: 'Đã phân bổ hết' }
    ],
    gridPosition: {
      row: 6,
      col: 0,
      colSpan: 12
    }
  },
  {
    key: 'xu_ly',
    name: 'xu_ly',
    label: 'Xử lý',
    type: 'select',
    options: [
      { value: '0', label: 'Người dùng tự phân bổ' },
      { value: '1', label: 'Gợi ý tự động theo ngày hóa đơn, số hóa đơn' },
      { value: '2', label: 'Gợi tự động theo hạn thanh toán, số hóa đơn' }
    ],
    gridPosition: {
      row: 7,
      col: 0,
      colSpan: 12
    }
  },
  {
    key: 'tu_dung_luu_phan_bo_theo_goi_y',
    name: 'tu_dung_luu_phan_bo_theo_goi_y',
    label: 'Tự động lưu phân bổ theo gợi ý',
    type: 'checkbox',
    labelPosition: 'right',
    gridPosition: {
      row: 8,
      col: 0,
      colSpan: 20
    }
  }
];
