import React, { useState } from 'react';

import { Button } from '@mui/material';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoDialog } from '@/components/custom/arito/dialog';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';

interface ReviewProgressDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (values: any) => void;
}

const ReviewProgressDialog: React.FC<ReviewProgressDialogProps> = ({ isOpen, onClose, onSubmit }) => {
  const [formValues, setFormValues] = useState();

  const handleSubmit = (data: any) => {
    setFormValues(data);
    onSubmit(data);
  };

  const handleDirectSubmit = () => {
    onSubmit(formValues);
    onClose();
  };

  return (
    <AritoDialog
      open={isOpen}
      onClose={onClose}
      title='Chuyển đổi người duyệt đang trong tiến trình duyệt'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button variant='contained' onClick={handleDirectSubmit}>
            <AritoIcon icon={884} />
            <span className='ml-1'>Đồng ý</span>
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} />
            <span className='ml-1'>Huỷ</span>
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        initialData={formValues}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[600px] min-w-[600px] overflow-y-auto'>
            <div className='space-y-2 p-4'>
              <div className='flex flex-col space-y-2'>
                <div className='flex items-center'>
                  <FormField
                    labelClassName='w-40 min-w-40'
                    className='w-96'
                    label='Người duyệt cũ:'
                    name='oldReviewer'
                    type='select'
                    options={[
                      { value: '<EMAIL>', label: 'THỰC TẬP SINH (<EMAIL>)' },
                      { value: '<EMAIL>', label: '<EMAIL> (<EMAIL>)' },
                      { value: '<EMAIL>', label: 'NGUYỄN THANH HUYỀN (<EMAIL>)' }
                    ]}
                  />
                </div>
                <div className='flex items-center'>
                  <FormField
                    labelClassName='w-40 min-w-40'
                    className='w-96'
                    label='Người duyệt mới:'
                    name='newReviewer'
                    type='select'
                    options={[
                      { value: '<EMAIL>', label: 'THỰC TẬP SINH (<EMAIL>)' },
                      { value: '<EMAIL>', label: '<EMAIL> (<EMAIL>)' },
                      { value: '<EMAIL>', label: 'NGUYỄN THANH HUYỀN (<EMAIL>)' }
                    ]}
                  />
                </div>
                <div className='flex items-center'>
                  <FormField labelClassName='w-40 min-w-40' className='w-96' label='Ghi chú:' name='note' />
                </div>
              </div>
            </div>
          </div>
        }
      />
    </AritoDialog>
  );
};

export default ReviewProgressDialog;
