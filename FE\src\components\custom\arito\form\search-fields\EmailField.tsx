import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface EmailFieldProps {
  name?: string;
  label?: string;
  labelClassName?: string;
  className?: string;
  inputClassName?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
  placeholder?: string;
}

export const EmailField: React.FC<EmailFieldProps> = ({
  name = 'email',
  label = 'Email',
  labelClassName,
  className = 'flex items-center',
  inputClassName = 'w-full',
  disabled = false,
  formMode = 'add',
  placeholder = 'Nhập email'
}) => {
  return (
    <div className={className}>
      <Label className={cn('mt-3 flex items-center text-left text-sm font-normal sm:mb-0', labelClassName)}>
        {label}
      </Label>
      <FormField
        name={name}
        type='text'
        className={inputClassName}
        label=''
        disabled={disabled || formMode === 'view'}
        placeholder={placeholder}
      />
    </div>
  );
};

export default EmailField;
