import React, { useState } from 'react';
import BasicInfoTab from './khai-bao-thong-tin-khach-hang/BasicInfoTab';
import { FormField } from '@/components/custom/arito/form/form-field';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import DetailTab from './khai-bao-thong-tin-khach-hang/DetailTab';
import { AritoModal } from '@/components/custom/arito/modal';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const MoreInfoTabType14 = ({ formMode }: Props) => {
  const [showCustomerInfoForm, setShowCustomerInfoForm] = useState(false);
  const [customerInfoData, setCustomerInfoData] = useState<any[]>([]);

  const handleShowForm = () => {
    setShowCustomerInfoForm(true);
  };

  const handleCloseForm = () => {
    setShowCustomerInfoForm(false);
  };

  const handleSubmitForm = (data: any) => {
    console.log('Form data:', data);
    setShowCustomerInfoForm(false);
  };

  const handleTableDataChange = (newData: any[]) => {
    setCustomerInfoData(newData);
  };
  return (
    <div className='grid grid-cols-1 gap-x-8 space-y-2 p-4 lg:grid-cols-1 lg:space-y-0'>
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Mô tả'
        name='description'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Địa chỉ giao hàng'
        name='shippingAddress'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Lĩnh vực hoạt động'
        name='businessField'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <button
        type='button'
        className='mt-6 flex w-[300px] items-center justify-center gap-x-2 rounded-md border border-gray-300 p-2 text-xs font-semibold hover:bg-gray-100'
        onClick={handleShowForm}
      >
        Khai báo thêm thông tin khách hàng
      </button>

      <AritoModal
        open={showCustomerInfoForm}
        onClose={handleCloseForm}
        title='Sửa thuộc tính'
        titleIcon={<AritoIcon icon={12} />}
        maxWidth='md'
      >
        <div className='flex size-full flex-col overflow-hidden'>
          <div className='max-h-[calc(100vh-120px)] flex-1 overflow-auto'>
            <AritoForm<any>
              mode={formMode}
              hasAritoActionBar={false}
              className='!static !w-full'
              onSubmit={handleSubmitForm}
              onClose={handleCloseForm}
              headerFields={<BasicInfoTab formMode={formMode} />}
              tabs={[
                {
                  id: 'chi-tiet',
                  label: 'Chi tiết',
                  component: <DetailTab value={customerInfoData} onChange={handleTableDataChange} formMode={formMode} />
                }
              ]}
            />
          </div>

          <BottomBar
            mode={formMode}
            onSubmit={() => {}}
            onClose={() => {
              setShowCustomerInfoForm(false);
            }}
          />
        </div>
      </AritoModal>
    </div>
  );
};

export default MoreInfoTabType14;
