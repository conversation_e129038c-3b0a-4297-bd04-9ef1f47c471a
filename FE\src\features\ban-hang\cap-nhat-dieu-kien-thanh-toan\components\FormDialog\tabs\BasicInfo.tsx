import { SearchField, FormField } from '@/components/custom/arito/form';
import { customerSearchColsDefinition } from './search-cols-definition';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { KhachHang } from '@/types/schemas';
import { FormMode } from '@/types/form';

interface BasicInfoProps {
  khachHang: KhachHang | null;
  setKhachHang: (customer: KhachHang) => void;
  formMode: FormMode;
}

const BasicInfo: React.FC<BasicInfoProps> = ({ khachHang, setKhachHang, formMode }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mã khách hàng:</Label>
            <div className='flex-1'>
              <SearchField<KhachHang>
                type='text'
                displayRelatedField='customer_name'
                columnDisplay='customer_code'
                className='w-96'
                searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
                relatedFieldValue={khachHang?.customer_code || ''}
                dialogTitle='Danh sách khách hàng'
                value={khachHang?.customer_code || ''}
                onRowSelection={setKhachHang}
                searchColumns={customerSearchColsDefinition}
                disabled={formMode === 'view'}
              />
            </div>
          </div>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mã điều kiện:</Label>
            <FormField name='ma_dk' label='' type='text' className='w-96' disabled={formMode === 'view'} />
          </div>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tên điều kiện:</Label>
            <FormField name='ten_dk' label='' type='text' className='w-96' disabled={formMode === 'view'} />
          </div>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tên khác:</Label>
            <FormField name='ten_dk2' label='' type='text' className='w-96' disabled={formMode === 'view'} />
          </div>
          <div className='flex items-center'>
            <FormField
              name='loai_dk'
              label='Loại điều kiện'
              labelClassName='w-40'
              type='select'
              options={[
                { value: '1', label: '1 - Thanh toán vào ngày cố định trong tháng' },
                { value: '2', label: '2 - Thanh toán vào ngày cố định trong tuần' }
              ]}
              className='w-[400px]'
              disabled={formMode === 'view'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
