'use client';

import { useEffect, useState } from 'react';
import { NavMenu, MenuItem } from './arito-nav-menu';

// Component for mobile menu items that can be nested
export function MobileMenuItemComponent({
  item,
  isLast,
  level = 1,
  isExpanded,
  onToggle
}: {
  item: any;
  isLast?: boolean;
  level: number;
  isExpanded: boolean;
  onToggle: (title: string) => void;
}) {
  const [expandedNestedItem, setExpandedNestedItem] = useState<string | null>(null);
  const hasSubItems = item.items && item.items.length > 0;
  const paddingLeft = `${level * 0.75}rem`;

  useEffect(() => {
    if (!isExpanded) {
      setExpandedNestedItem(null);
    }
  }, [isExpanded]);

  return (
    <div className={`${isLast ? '' : 'border-t border-teal-700'}`}>
      <div
        className='flex cursor-pointer items-center justify-between bg-teal-700 px-3 py-2 text-white'
        style={{ paddingLeft: `calc(0.75rem + ${paddingLeft})` }}
        onClick={() => {
          if (item.href) {
            // Handle navigation if there's a link
            window.location.href = item.href;
          } else {
            // Otherwise toggle expansion
            onToggle(item.title);
          }
        }}
      >
        <div className='flex items-center gap-2'>
          <span className='flex h-4 w-4 items-center justify-center'>{item.icon}</span>
          <span className='text-xs'>{item.title}</span>
        </div>

        {hasSubItems && (
          <svg
            xmlns='http://www.w3.org/2000/svg'
            className={`h-3 w-3 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
            fill='none'
            viewBox='0 0 24 24'
            stroke='currentColor'
          >
            <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M19 9l-7 7-7-7' />
          </svg>
        )}
      </div>

      {isExpanded && hasSubItems && (
        <div className='bg-teal-700'>
          {item.items.map((subItem: any, idx: number) => (
            <MobileMenuItemComponent
              key={subItem.title}
              item={subItem}
              isLast={idx === item.items.length - 1}
              level={level + 1}
              isExpanded={expandedNestedItem === subItem.title}
              onToggle={title => {
                if (title === expandedNestedItem) {
                  setExpandedNestedItem(null);
                } else {
                  setExpandedNestedItem(title);
                }
              }}
            />
          ))}
        </div>
      )}
    </div>
  );
}

// Container for mobile navigation sections
export function MobileNavMenu({
  menu,
  isLast,
  isOpen,
  onToggle
}: {
  menu: NavMenu;
  isLast?: boolean;
  isOpen: boolean;
  onToggle: (title: string) => void;
}) {
  const [expandedSubItem, setExpandedSubItem] = useState<string | null>(null);

  useEffect(() => {
    if (!isOpen) {
      setExpandedSubItem(null);
    }
  }, [isOpen]);

  // Group items by their group property
  const groupedItems: { [key: number]: MenuItem[] } = {};
  menu.items.forEach(item => {
    const group = item.group || 0;
    if (!groupedItems[group]) {
      groupedItems[group] = [];
    }
    groupedItems[group].push(item);
  });

  // Get sorted group keys
  const groupKeys = Object.keys(groupedItems).map(Number).sort();

  return (
    <div className={`border-b ${isLast ? 'border-transparent' : 'border-teal-600'}`}>
      <button
        className={`flex w-full items-center justify-between px-3 py-2 text-white ${isOpen ? 'bg-teal-700' : 'bg-teal-600'}`}
        onClick={() => onToggle(menu.title)}
      >
        <span className='text-sm'>{menu.title}</span>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className={`h-3.5 w-3.5 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill='none'
          viewBox='0 0 24 24'
          stroke='currentColor'
        >
          <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M19 9l-7 7-7-7' />
        </svg>
      </button>

      {isOpen && (
        <div className='bg-teal-700'>
          {groupKeys.map((group, groupIndex) => (
            <div key={group}>
              {groupIndex > 0 && <div className='mx-3 border-t border-teal-600'></div>}
              {groupedItems[group].map((item, idx) => (
                <MobileMenuItemComponent
                  key={item.title}
                  item={item}
                  isLast={idx === groupedItems[group].length - 1 && groupIndex === groupKeys.length - 1}
                  level={1}
                  isExpanded={expandedSubItem === item.title}
                  onToggle={title => {
                    if (title === expandedSubItem) {
                      setExpandedSubItem(null);
                    } else {
                      setExpandedSubItem(title);
                    }
                  }}
                />
              ))}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
