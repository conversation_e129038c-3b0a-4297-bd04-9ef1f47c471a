import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface RecipientSearchFieldProps {
  name?: string;
  label?: string;
  labelClassName?: string;
  className?: string;
  inputClassName?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
  searchEndpoint?: string;
}

// Define the search columns for recipients
const recipientSearchColumns = [
  { field: 'ma_nguoi_nhan', headerName: 'Mã người nhận', flex: 1 },
  { field: 'ten_nguoi_nhan', headerName: 'Tên người nhận', flex: 2 },
  { field: 'dien_thoai', headerName: 'Điện thoại', flex: 1 },
  { field: 'email', headerName: 'Email', flex: 1 }
];

export const RecipientSearchField: React.FC<RecipientSearchFieldProps> = ({
  name = 'recipient',
  label = 'Người nhận',
  labelClassName,
  className = 'flex items-center',
  inputClassName = 'w-full',
  disabled = false,
  formMode = 'add',
  searchEndpoint = 'accounting/recipients'
}) => {
  return (
    <div className={className}>
      <Label className={cn('mt-3 flex items-center text-left text-sm font-normal sm:mb-0', labelClassName)}>
        {label}
      </Label>
      <FormField
        name={name}
        type='text'
        className={inputClassName}
        label=''
        searchEndpoint={searchEndpoint}
        searchColumns={recipientSearchColumns}
        defaultSearchColumn='ma_nguoi_nhan'
        disabled={disabled || formMode === 'view'}
      />
    </div>
  );
};

export default RecipientSearchField;
