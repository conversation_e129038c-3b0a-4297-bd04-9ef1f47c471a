import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const FilterByObjectTab: React.FC = () => {
  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Bộ phận:</Label>

          <FormField
            name='department'
            label=''
            type='text'
            withSearch
            searchEndpoint='/api/departments'
            searchResultLabelKey='departmentName'
            searchResultValueKey='departmentCode'
            searchColumns={[
              {
                field: 'departmentCode',
                headerName: 'Mã bộ phận',
                width: 120
              },
              {
                field: 'departmentName',
                headerName: 'Tên bộ phận',
                width: 200
              }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Vụ việc:</Label>

          <FormField
            name='project'
            label=''
            type='text'
            withSearch
            searchEndpoint='/api/projects'
            searchResultLabelKey='projectName'
            searchResultValueKey='projectCode'
            searchColumns={[
              {
                field: 'projectCode',
                headerName: 'Mã vụ việc',
                width: 120
              },
              {
                field: 'projectName',
                headerName: 'Tên vụ việc',
                width: 200
              }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Hợp đồng:</Label>

          <FormField
            name='contract'
            label=''
            type='text'
            withSearch
            searchEndpoint='/api/contracts'
            searchResultLabelKey='contractName'
            searchResultValueKey='contractCode'
            searchColumns={[
              {
                field: 'contractCode',
                headerName: 'Mã hợp đồng',
                width: 120
              },
              {
                field: 'contractName',
                headerName: 'Tên hợp đồng',
                width: 200
              }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Đợt thanh toán:</Label>

          <FormField
            name='paymentPhase'
            label=''
            type='text'
            withSearch
            searchEndpoint='/api/payment-phases'
            searchResultLabelKey='phaseName'
            searchResultValueKey='phaseCode'
            searchColumns={[
              { field: 'phaseCode', headerName: 'Mã đợt', width: 120 },
              {
                field: 'phaseName',
                headerName: 'Tên đợt thanh toán',
                width: 200
              },
              {
                field: 'paymentDate',
                headerName: 'Ngày thanh toán',
                width: 120
              },
              { field: 'percentage', headerName: 'Tỷ lệ (%)', width: 100 },
              { field: 'amount', headerName: 'Tiền', width: 120 }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Khế ước:</Label>

          <FormField
            name='agreement'
            label=''
            type='text'
            withSearch
            searchEndpoint='/api/agreements'
            searchResultLabelKey='agreementName'
            searchResultValueKey='agreementCode'
            searchColumns={[
              {
                field: 'agreementCode',
                headerName: 'Mã khế ước',
                width: 120
              },
              {
                field: 'agreementName',
                headerName: 'Tên khế ước',
                width: 200
              }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Phí:</Label>

          <FormField
            name='fee'
            label=''
            type='text'
            withSearch
            searchEndpoint='/api/fees'
            searchResultLabelKey='feeName'
            searchResultValueKey='feeCode'
            searchColumns={[
              { field: 'feeCode', headerName: 'Mã phí', width: 120 },
              { field: 'feeName', headerName: 'Tên phí', width: 200 }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Sản phẩm:</Label>

          <FormField
            name='product'
            label=''
            type='text'
            withSearch
            searchEndpoint='/api/products'
            searchResultLabelKey='productName'
            searchResultValueKey='productCode'
            searchColumns={[
              {
                field: 'productCode',
                headerName: 'Mã sản phẩm',
                width: 120
              },
              {
                field: 'productName',
                headerName: 'Tên sản phẩm',
                width: 200
              }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Lệnh sản xuất:</Label>

          <FormField
            name='productionOrder'
            label=''
            type='text'
            withSearch
            searchEndpoint='/api/production-orders'
            searchResultLabelKey='orderName'
            searchResultValueKey='orderCode'
            searchColumns={[
              { field: 'orderCode', headerName: 'Số lệnh', width: 120 },
              { field: 'description', headerName: 'Diễn giải', width: 200 }
            ]}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>C/p không h/lệ:</Label>

          <FormField
            name='invalidExpense'
            label=''
            type='text'
            withSearch
            searchEndpoint='/api/invalid-expenses'
            searchResultLabelKey='expenseName'
            searchResultValueKey='expenseCode'
            searchColumns={[
              {
                field: 'expenseCode',
                headerName: 'Mã chi phí',
                width: 120
              },
              {
                field: 'expenseName',
                headerName: 'Tên chi phí',
                width: 200
              }
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default FilterByObjectTab;
