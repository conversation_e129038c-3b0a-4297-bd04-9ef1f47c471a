import { useState } from 'react';
import { transactionColumns, accountColumns, batchColumns, locationColumns } from '../../columns';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { DocumentNumberRangeField } from '@/components/custom/arito/form/search-fields';
import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const OtherTab: React.FC = () => {
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);

  const handleSaveFilterTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving filter template:', data);
    setSaveFilterTemplateDialogOpen(false);
  };

  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã giao dịch:</Label>
          <FormField
            name='transactionCode'
            inputClassName='w-96'
            label=''
            type='text'
            searchEndpoint='/api/transactions'
            searchResultLabelKey='transactionName'
            searchResultValueKey='transactionCode'
            searchColumns={transactionColumns}
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản vật tư:</Label>
          <div className='flex-1'>
            <FormField
              name='itemAccount'
              label=''
              type='text'
              searchEndpoint='/api/accounts'
              searchResultLabelKey='accountName'
              searchResultValueKey='accountCode'
              searchColumns={accountColumns}
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản doanh thu:</Label>
          <div className='flex-1'>
            <FormField
              name='revenueAccount'
              label=''
              type='text'
              searchEndpoint='/api/accounts'
              searchResultLabelKey='accountName'
              searchResultValueKey='accountCode'
              searchColumns={accountColumns}
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản giá vốn:</Label>
          <div className='flex-1'>
            <FormField
              name='costAccount'
              label=''
              type='text'
              searchEndpoint='/api/accounts'
              searchResultLabelKey='accountName'
              searchResultValueKey='accountCode'
              searchColumns={accountColumns}
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã lô:</Label>
          <div className='flex-1'>
            <FormField
              name='batchCode'
              label=''
              type='text'
              searchEndpoint='/api/batches'
              searchResultLabelKey='batchName'
              searchResultValueKey='batchCode'
              searchColumns={batchColumns}
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vị trí:</Label>
          <div className='flex-1'>
            <FormField
              name='locationCode'
              label=''
              type='text'
              searchEndpoint='/api/locations'
              searchResultLabelKey='locationName'
              searchResultValueKey='locationCode'
              searchColumns={locationColumns}
            />
          </div>
        </div>
        <DocumentNumberRangeField formMode={'view'} label='Số c/từ (từ/đến)' />
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Diễn giải:</Label>
          <div className='flex-1'>
            <FormField name='description' label='' type='text' className='w-96' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu lọc báo cáo:</Label>
          <div className='flex items-center gap-1'>
            <FormField
              name='reportFilterTemplate'
              label=''
              type='select'
              options={[{ value: 'user_filter', label: 'Người dùng tự lọc' }]}
              className='w-96'
            />

            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => setSaveFilterTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />
    </div>
  );
};

export default OtherTab;
