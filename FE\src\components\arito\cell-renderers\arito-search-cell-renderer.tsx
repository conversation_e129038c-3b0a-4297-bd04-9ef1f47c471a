import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import SearchIcon from '@mui/icons-material/Search';
import { IconButton } from '@mui/material';
import React, { useState } from 'react';
import AritoSearchModal from '@/components/custom/arito/arito-search-modal';

export function AritoSearchCellRenderer({
  params,
  docType,
  columns,
  onSelect
}: {
  params: GridRenderCellParams;
  docType: string;
  columns: GridColDef[];
  onSelect?: (selectedRow: any, params: GridRenderCellParams) => void;
}) {
  const [searchModalOpen, setSearchModalOpen] = useState(false);

  const handleSearchClick = () => {
    setSearchModalOpen(true);
  };

  const handleSearchSelect = (selectedRow: any) => {
    // Call the onSelect callback if provided
    if (onSelect) {
      onSelect(selectedRow, params);
    }
    setSearchModalOpen(false);
  };

  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        height: '100%',
        width: '100%',
        position: 'relative'
      }}
    >
      <div style={{ flexGrow: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}>{params.value}</div>

      <IconButton
        onClick={handleSearchClick}
        size='small'
        sx={{
          boxSizing: 'border-box',
          padding: '2px',
          color: '#0b87c9',
          border: '1px solid transparent',
          cursor: 'default',
          '&:hover': {
            border: '1px solid rgb(65, 165, 218, 0.5)',
            backgroundColor: 'rgba(32, 139, 197, 0.08)'
          }
        }}
      >
        <SearchIcon style={{ fontSize: '16px' }} />
      </IconButton>

      {searchModalOpen && (
        <AritoSearchModal
          visible={searchModalOpen}
          onClose={() => setSearchModalOpen(false)}
          onSelect={handleSearchSelect}
          title={`Search ${params.colDef.headerName || ''}`}
          columns={columns || []}
          docType={docType}
        />
      )}
    </div>
  );
}
