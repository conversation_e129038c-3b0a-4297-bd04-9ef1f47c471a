"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/danh-muc/mua-hang/gia-mua/page",{

/***/ "(app-pages-browser)/./src/features/danh-muc/mua-hang/gia-mua/schemas.ts":
/*!***********************************************************!*\
  !*** ./src/features/danh-muc/mua-hang/gia-mua/schemas.ts ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formSchema: function() { return /* binding */ formSchema; },\n/* harmony export */   initialValues: function() { return /* binding */ initialValues; }\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n\nconst formSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    uuid: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    ngay_hieu_luc: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    so_luong_tu: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n    ]).optional(),\n    gia_mua: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n    ]).optional(),\n    trang_thai: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().default(1),\n    // Các trường dữ liệu đối tượng (cho edit mode)\n    ma_vat_tu_data: zod__WEBPACK_IMPORTED_MODULE_0__.z.any().optional(),\n    don_vi_tinh_data: zod__WEBPACK_IMPORTED_MODULE_0__.z.any().optional(),\n    nha_cung_cap_data: zod__WEBPACK_IMPORTED_MODULE_0__.z.any().optional(),\n    ngoai_te_data: zod__WEBPACK_IMPORTED_MODULE_0__.z.any().optional()\n});\nconst initialValues = {\n    uuid: undefined,\n    ngay_hieu_luc: \"\",\n    so_luong_tu: 0,\n    gia_mua: 0,\n    trang_thai: 1,\n    // Các trường dữ liệu đối tượng\n    ma_vat_tu_data: null,\n    don_vi_tinh_data: null,\n    nha_cung_cap_data: null,\n    ngoai_te_data: null\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/danh-muc/mua-hang/gia-mua/schemas.ts\n"));

/***/ })

});