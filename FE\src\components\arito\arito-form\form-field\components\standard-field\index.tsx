import { Box, Checkbox, FormControlLabel, MenuItem, Select, TextField, Typography } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { AritoInputTable } from '@/components/custom/arito';
import { StandardFieldProps } from '../../types';

interface SelectOption {
  value: any;
  label: string;
}

export const StandardField = ({
  field,
  fieldId,
  type,
  disabled,
  options,
  isMobile,
  label,
  columns
}: StandardFieldProps) => {
  switch (type) {
    case 'table':
      return (
        <div className='h-[300px] w-screen'>
          <AritoInputTable
            value={field.value ? field.value : []}
            columns={columns || []}
            onChange={field.onChange}
            mode={disabled ? 'view' : 'edit'}
          />
        </div>
      );
    case 'select':
      const selectOptions = options as SelectOption[];
      return (
        <Select
          {...field}
          disabled={disabled}
          size='small'
          fullWidth
          id={fieldId}
          sx={{
            '& .MuiOutlinedInput-notchedOutline': {
              border: 'none'
            },
            '&.MuiOutlinedInput-root': {
              borderBottom: '1px solid #e5e7eb',
              borderRadius: 0,
              '&:hover': {
                borderBottom: '1px solid #2563EB'
              },
              '&.Mui-focused': {
                borderBottom: '1px solid #2563EB'
              }
            },
            '& .MuiSelect-select': {
              padding: '4px 8px',
              fontSize: '14px'
            },
            backgroundColor: 'transparent',
            position: 'relative',
            top: '-4px'
          }}
          MenuProps={{
            PaperProps: {
              sx: {
                borderRadius: '4px',
                marginTop: '4px'
              }
            }
          }}
        >
          {selectOptions?.map((option: SelectOption) => (
            <MenuItem key={option.value.toString()} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      );
    case 'checkbox':
      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            position: 'relative',
            top: '-4px'
          }}
        >
          <FormControlLabel
            control={
              <Checkbox
                {...field}
                checked={field.value}
                disabled={disabled}
                id={fieldId}
                title={label}
                sx={{
                  color: '#d1d5db',
                  '&.Mui-checked': {
                    color: '#2563EB'
                  },
                  '&:hover': {
                    color: disabled ? '#d1d5db' : '#2563EB'
                  }
                }}
              />
            }
            label={
              <Typography variant='body2' sx={{ color: '#000000', fontSize: '0.875rem' }}>
                {label}
              </Typography>
            }
          />
        </Box>
      );
    case 'date':
      return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DatePicker
            value={field.value ? dayjs(field.value) : null}
            onChange={(newValue: dayjs.Dayjs | null) => {
              field.onChange(newValue ? newValue.format('YYYY-MM-DD') : null);
            }}
            disabled={disabled}
            format='DD/MM/YYYY'
            localeText={{
              cancelButtonLabel: 'Hủy',
              toolbarTitle: 'Chọn ngày',
              todayButtonLabel: 'Hôm nay',
              previousMonth: 'Tháng trước',
              nextMonth: 'Tháng sau'
            }}
            slotProps={{
              textField: {
                size: 'small',
                fullWidth: true,
                variant: 'standard',
                inputProps: { id: fieldId },
                sx: {
                  '& .MuiInput-root': {
                    fontSize: '14px',
                    '&:before': {
                      borderBottom: '1px solid #e5e7eb'
                    },
                    '&:hover:not(.Mui-disabled):before': {
                      borderBottom: '1px solid #2563EB'
                    },
                    '&.Mui-focused:after': {
                      borderBottom: '1px solid #2563EB'
                    }
                  },
                  '& .MuiInput-input': {
                    padding: '4px 8px'
                  },
                  '& .MuiInputAdornment-root': {
                    marginRight: '0'
                  },
                  '& .MuiIconButton-root': {
                    padding: '4px'
                  },
                  '& .MuiSvgIcon-root': {
                    color: '#2563EB',
                    marginRight: '8px',
                    fontSize: '18px'
                  },
                  position: 'relative',
                  top: '-4px',
                  marginTop: '8px'
                }
              }
            }}
          />
        </LocalizationProvider>
      );
    default:
      return (
        <TextField
          {...field}
          type={type}
          disabled={disabled}
          fullWidth
          size='small'
          variant='standard'
          id={fieldId}
          sx={{
            '& .MuiInput-root': {
              fontSize: '14px',
              '&:before': {
                borderBottom: '1px solid #e5e7eb'
              },
              '&:hover:not(.Mui-disabled):before': {
                borderBottom: '1px solid #2563EB'
              },
              '&.Mui-focused:after': {
                borderBottom: '1px solid #2563EB'
              }
            },
            '& .MuiInput-input': {
              padding: '4px 8px'
            },
            marginTop: type === 'number' ? '8px' : '0px'
          }}
        />
      );
  }
};
