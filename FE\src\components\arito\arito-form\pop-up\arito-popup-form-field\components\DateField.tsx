import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Controller } from 'react-hook-form';
import React from 'react';
import dayjs from 'dayjs';
import { fieldStyles } from '../styles/constants';

interface DateFieldProps {
  name: string;
  control: any;
  disabled?: boolean;
  isViewMode?: boolean;
  fieldId: string;
}

export const DateField: React.FC<DateFieldProps> = ({
  name,
  control,
  disabled = false,
  isViewMode = false,
  fieldId
}) => {
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={null}
      render={({ field }) => (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DatePicker
            value={field.value ? dayjs(field.value) : null}
            onChange={(newValue: dayjs.Dayjs | null) => {
              field.onChange(newValue ? newValue.format('YYYY-MM-DD') : null);
            }}
            disabled={disabled || isViewMode}
            format='DD/MM/YYYY'
            localeText={{
              cancelButtonLabel: 'Hủy',
              toolbarTitle: 'Chọn ngày',
              todayButtonLabel: 'Hôm nay',
              previousMonth: 'Tháng trước',
              nextMonth: 'Tháng sau'
            }}
            slotProps={{
              textField: {
                id: fieldId,
                size: 'small',
                fullWidth: true,
                variant: 'standard',
                sx: {
                  '& .MuiInput-root': {
                    fontSize: '13px',
                    '&:before': {
                      borderBottom: '1px solid #e5e7eb'
                    },
                    '&:hover:not(.Mui-disabled):before': {
                      borderBottom: '1px solid #2563EB'
                    },
                    '&.Mui-focused:after': {
                      borderBottom: '1px solid #2563EB'
                    }
                  },
                  '& .MuiInput-input': {
                    padding: '6px 6px'
                  },
                  '& .MuiInputAdornment-root': {
                    marginRight: '0'
                  },
                  '& .MuiIconButton-root': {
                    padding: '2px'
                  },
                  '& .MuiSvgIcon-root': {
                    color: '#2563EB',
                    marginRight: '8px',
                    fontSize: '18px'
                  },
                  position: 'relative',
                  top: '-4px'
                }
              }
            }}
          />
        </LocalizationProvider>
      )}
    />
  );
};
