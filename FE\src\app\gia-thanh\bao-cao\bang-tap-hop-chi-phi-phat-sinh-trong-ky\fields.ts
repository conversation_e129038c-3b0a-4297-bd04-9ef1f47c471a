import { FieldConfig } from '@/components/arito/arito-form-fields';
import { TabConfig } from '@/components/arito/arito-filter-modal';

export const costAccumulationFilterFields: FieldConfig[] = [
  {
    key: 'ky',
    name: 'ky',
    label: 'Kỳ/Năm',
    type: 'text',
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 3
    }
  },
  {
    key: 'nam',
    name: 'nam',
    label: '',
    type: 'text',
    gridPosition: {
      row: 0,
      col: 2,
      colSpan: 4
    }
  }
];

export const costAccumulationDetailFilterFields: FieldConfig[] = [
  {
    key: 'ma_san_pham',
    name: 'ma_san_pham',
    label: 'Mã sản phẩm',
    type: 'search',
    searchModalTitle: 'Danh mục sản phẩm',
    searchColumns: [
      { field: 'ma_vat_tu', headerName: 'Mã vật tư', width: 120 },
      { field: 'ten_vat_tu', headerName: 'Tên vật tư', width: 280 },
      { field: 'don_vi', headerName: 'Đvt lẻ', width: 120 }
    ],
    docType: 'Material',
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 9
    }
  },
  {
    key: 'ma_bo_phan',
    name: 'ma_bo_phan',
    label: 'Mã bộ phận',
    type: 'search',
    searchModalTitle: 'Danh mục công đoạn',
    searchColumns: [
      { field: 'ma_cong_doan', headerName: 'Mã công đoạn', width: 120 },
      { field: 'ten_cong_doan', headerName: 'Tên công đoạn', width: 280 }
    ],
    docType: 'Process',
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 9
    }
  },
  {
    key: 'so_lenh_sx',
    name: 'so_lenh_sx',
    label: 'Số lệnh sx',
    type: 'search',
    searchModalTitle: 'Lệnh sản xuất',
    searchColumns: [
      { field: 'so_lenh', headerName: 'Số lệnh', width: 120 },
      { field: 'dien_giai', headerName: 'Diễn giải', width: 150 }
    ],
    docType: 'ManufacturingOrder',
    gridPosition: {
      row: 2,
      col: 0,
      colSpan: 9
    }
  },
  {
    key: 'don_vi',
    name: 'don_vi',
    label: 'Đơn vị',
    type: 'select',
    options: [
      { value: '0318423416', label: '0318423416 - CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ' }
    ],
    gridPosition: {
      row: 3,
      col: 0,
      colSpan: 9
    }
  },
  {
    key: 'mau_bao_cao',
    name: 'mau_bao_cao',
    label: 'Mẫu báo cáo',
    type: 'select',
    options: [
      { value: 'standard', label: 'Mẫu tiền chuẩn' },
      { value: 'foreign_currency', label: 'Mẫu ngoại tệ' }
    ],
    gridPosition: {
      row: 4,
      col: 0,
      colSpan: 12
    }
  }
];

export const costAccumulationOtherFilterFields: FieldConfig[] = [
  {
    key: 'mau_loc_bao_cao',
    name: 'mau_loc_bao_cao',
    label: 'Mẫu lọc báo cáo',
    type: 'select',
    options: [{ value: 'nguoi_dung_tu_loc', label: 'Người dùng tự lọc' }],
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 9
    }
  },
  {
    key: 'mau_phan_tich_dl',
    name: 'mau_phat_anh_dl',
    label: 'Mẫu phân tích DL',
    type: 'select',
    options: [{ value: 'khong_phan_tich', label: 'Không phân tích' }],
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 9
    }
  }
];

export const costAccumulationFilterTabs: TabConfig[] = [
  {
    key: 'chi_tiet',
    title: 'Chi tiết',
    fields: costAccumulationDetailFilterFields,
    columns: 20
  },
  {
    key: 'khac',
    title: 'Khác',
    fields: costAccumulationOtherFilterFields,
    columns: 20
  }
];
