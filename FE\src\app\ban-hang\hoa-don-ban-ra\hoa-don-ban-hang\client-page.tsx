'use client';

import { FormProvider, useForm } from 'react-hook-form';
import React, { useState, useRef } from 'react';
import { purchaseInvoiceGeneralTabs, purchaseInvoiceDetailedTabs, purchaseInvoiceFooterFields } from './fields';
import { purchaseInvoiceColumns, purchaseInvoiceItemColumns1, purchaseInvoiceItemColumns2 } from './columns';

import { FieldConfig, RenderField } from '@/components/arito/arito-form-fields';
import AritoColoredDot from '@/components/custom/arito/icon/colored-dot';
import { renderFieldsGrid } from '@/components/arito/arito-form-fields';
import { invoiceSchema, purchaseInvoiceInitialValues } from './schemas';

interface SalesInvoicesClientPageProps {
  invoices: any[];
}

export function SalesInvoicesClientPage({ invoices }: SalesInvoicesClientPageProps) {
  const configs: any[] = [
    {
      title: 'Ho<PERSON> đơn bán hàng',
      docType: 'Sales Invoice',
      popupConfig: {
        generalTabs: purchaseInvoiceGeneralTabs,
        detailedTabs: purchaseInvoiceDetailedTabs,
        validationSchema: invoiceSchema,
        initialValues: purchaseInvoiceInitialValues,
        footerFields: purchaseInvoiceFooterFields
      },
      tableConfig: {
        name: 'Tất cả',
        rows: invoices,
        columns: purchaseInvoiceColumns
      },
      subTableField: 'items',
      subTableColumns: purchaseInvoiceItemColumns1
    },
    {
      title: 'Hoá đơn bán hàng',
      docType: 'Sales Invoice',
      popupConfig: {
        generalTabs: purchaseInvoiceGeneralTabs,
        detailedTabs: purchaseInvoiceDetailedTabs,
        validationSchema: invoiceSchema,
        initialValues: purchaseInvoiceInitialValues,
        footerFields: purchaseInvoiceFooterFields
      },
      tableConfig: {
        name: 'Lập chứng từ',
        icon: <AritoColoredDot color='pink' className='m-1.5' />,
        rows: invoices.filter(invoice => invoice.status === 'Chờ duyệt'),
        columns: purchaseInvoiceColumns,
        tabProps: { className: 'whitespace-nowrap' }
      },
      subTableField: 'items',
      subTableColumns: purchaseInvoiceItemColumns1
    },
    {
      title: 'Hoá đơn bán hàng',
      docType: 'Sales Invoice',
      popupConfig: {
        generalTabs: purchaseInvoiceGeneralTabs,
        detailedTabs: purchaseInvoiceDetailedTabs,
        validationSchema: invoiceSchema,
        initialValues: purchaseInvoiceInitialValues,
        footerFields: purchaseInvoiceFooterFields
      },
      tableConfig: {
        name: 'Chờ duyệt',
        icon: <AritoColoredDot color='red' className='m-1.5' />,
        rows: invoices.filter(invoice => invoice.status === 'Chờ duyệt'),
        columns: purchaseInvoiceColumns,
        tabProps: { className: 'whitespace-nowrap' }
      },
      subTableField: 'items',
      subTableColumns: purchaseInvoiceItemColumns1
    },
    {
      title: 'Hoá đơn bán hàng',
      docType: 'Sales Invoice',
      popupConfig: {
        generalTabs: purchaseInvoiceGeneralTabs,
        detailedTabs: purchaseInvoiceDetailedTabs,
        validationSchema: invoiceSchema,
        initialValues: purchaseInvoiceInitialValues,
        footerFields: purchaseInvoiceFooterFields
      },
      tableConfig: {
        name: 'Xuất hoá đơn',
        icon: <AritoColoredDot color='blue' className='m-1.5' />,
        rows: invoices.filter(invoice => invoice.statusHDDT === 'Chờ xuất' || invoice.statusHDDT === 'Chưa xuất'),
        columns: purchaseInvoiceColumns,
        tabProps: { className: 'whitespace-nowrap' }
      },
      subTableField: 'items',
      subTableColumns: purchaseInvoiceItemColumns1
    },
    {
      title: 'Hoá đơn bán hàng',
      docType: 'Sales Invoice',
      popupConfig: {
        generalTabs: purchaseInvoiceGeneralTabs,
        detailedTabs: purchaseInvoiceDetailedTabs,
        validationSchema: invoiceSchema,
        initialValues: purchaseInvoiceInitialValues,
        footerFields: purchaseInvoiceFooterFields
      },
      tableConfig: {
        name: 'Khác',
        icon: <AritoColoredDot color='black' className='m-1.5' />,
        rows: invoices.filter(invoice => invoice.statusHDDT === 'Chờ xuất' || invoice.statusHDDT === 'Chưa xuất'),
        columns: purchaseInvoiceColumns,
        tabProps: { className: 'whitespace-nowrap' }
      },
      subTableField: 'items',
      subTableColumns: purchaseInvoiceItemColumns1
    }
  ];

  return (
    <div className='p-4'>
      <h1 className='mb-4 text-2xl font-bold'>Hoá đơn bán hàng</h1>
      <p>Component needs to be implemented with custom components</p>
    </div>
  );
}
