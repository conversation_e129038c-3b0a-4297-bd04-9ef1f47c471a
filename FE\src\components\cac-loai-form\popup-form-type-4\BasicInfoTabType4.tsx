import React from 'react';
import BasicInfoTabType2 from '../popup-form-type-2/BasicInfoTabType2';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Type2Tabs } from '../popup-form-type-2/Tabs';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTabType4 = ({ formMode }: Props) => {
  return (
    <div className='space-y-2 p-4'>
      <FormField
        className='flex max-w-[400px] items-center justify-between'
        label='Ngoại tệ'
        name='ngoai_te'
        type='text'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='flex max-w-[400px] items-center justify-between'
        label='Tên ngoại tệ'
        name='ten_ngoai_te'
        type='text'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='flex max-w-[400px] items-center justify-between'
        label='Tên khác'
        name='ten_khac'
        type='text'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='flex max-w-[400px] items-center justify-between'
        label='Tk phát sinh cl nợ'
        name='tk_phat_sinh_cl_no'
        type='text'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
        withSearch
        searchEndpoint='mua-hang/bao-cao-mua-hang/bang-ke-phieu-xuat-tra-lai-nha-cung-cap/kho'
        searchColumns={[]}
        actionButtons={['add', 'edit']}
        headerFields={<BasicInfoTabType2 formMode={formMode} />}
        tabs={Type2Tabs({ formMode })}
      />
      <FormField
        className='flex max-w-[400px] items-center justify-between'
        label='Tk phát sinh cl có'
        name='tk_phat_sinh_cl_co'
        type='text'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
        withSearch
        searchEndpoint='mua-hang/bao-cao-mua-hang/bang-ke-phieu-xuat-tra-lai-nha-cung-cap/kho'
        searchColumns={[]}
        actionButtons={['add', 'edit']}
        headerFields={<BasicInfoTabType2 formMode={formMode} />}
        tabs={Type2Tabs({ formMode })}
      />
      <FormField
        className='flex max-w-[400px] items-center justify-between'
        label='Stt'
        name='stt'
        type='number'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='flex max-w-[400px] items-center justify-between'
        label='Đọc lẻ'
        name='doc_le'
        type='number'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
      />
      <div className='flex items-center gap-x-12'>
        <div className='text-[13px]'>Cách đọc</div>
        <div className='grid grid-cols-1 gap-x-4 lg:grid lg:grid-cols-[repeat(5,1fr)]'>
          <FormField
            className='flex max-w-[100px] items-center justify-between'
            label=''
            name='stt'
            type='text'
            labelClassName='min-w-[100px]'
            disabled={formMode === 'view'}
          />
          <FormField
            className='flex max-w-[100px] items-center justify-between'
            label=''
            name='stt'
            type='text'
            labelClassName='min-w-[100px]'
            disabled={formMode === 'view'}
          />
          <FormField
            className='flex max-w-[100px] items-center justify-between'
            label=''
            name='stt'
            type='text'
            labelClassName='min-w-[100px]'
            disabled={formMode === 'view'}
          />
          <FormField
            className='flex max-w-[100px] items-center justify-between'
            label=''
            name='stt'
            type='text'
            labelClassName='min-w-[100px]'
            disabled={formMode === 'view'}
          />
          <FormField
            className='flex max-w-[100px] items-center justify-between'
            label=''
            name='stt'
            type='text'
            labelClassName='min-w-[100px]'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
      <div className='flex items-center gap-x-10'>
        <div className='text-[13px]'>Cách đọc 2</div>
        <div className='grid grid-cols-1 gap-x-4 lg:grid lg:grid-cols-[repeat(5,1fr)]'>
          <FormField
            className='flex max-w-[100px] items-center justify-between'
            label=''
            name='stt'
            type='text'
            labelClassName='min-w-[100px]'
            disabled={formMode === 'view'}
          />
          <FormField
            className='flex max-w-[100px] items-center justify-between'
            label=''
            name='stt'
            type='text'
            labelClassName='min-w-[100px]'
            disabled={formMode === 'view'}
          />
          <FormField
            className='flex max-w-[100px] items-center justify-between'
            label=''
            name='stt'
            type='text'
            labelClassName='min-w-[100px]'
            disabled={formMode === 'view'}
          />
          <FormField
            className='flex max-w-[100px] items-center justify-between'
            label=''
            name='stt'
            type='text'
            labelClassName='min-w-[100px]'
            disabled={formMode === 'view'}
          />
          <FormField
            className='flex max-w-[100px] items-center justify-between'
            label=''
            name='stt'
            type='text'
            labelClassName='min-w-[100px]'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
      <FormField
        className='flex items-center justify-between'
        label='Ghi chú'
        name='ghi_chu'
        type='text'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
      />
    </div>
  );
};

export default BasicInfoTabType4;
