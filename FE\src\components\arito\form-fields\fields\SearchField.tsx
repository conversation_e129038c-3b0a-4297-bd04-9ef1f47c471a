import SearchIcon from '@mui/icons-material/Search';
import { Controller } from 'react-hook-form';
import { IconButton } from '@mui/material';
import React, { useState } from 'react';
import { InputFieldWrapper, ErrorMessage, SearchIconWrapper } from '../components/InputFieldWrapper';
import AritoSearchModal from '@/components/custom/arito/arito-search-modal';
import { StyledInput } from '../inputs/StyledInput';
import { FormRow } from '../components/FormRow';
import { CommonFieldProps } from '../types';

export const SearchField: React.FC<CommonFieldProps> = ({ field, maxLabelWidth, isViewMode }) => {
  const {
    key,
    label,
    placeholder,
    required,
    disabled = isViewMode,
    minWidth,
    valueRender,
    searchModalTitle,
    searchColumns = [],
    docType,
    searchFilters,
    showSearchDisplay
  } = field;

  // State for search modal and selected value
  const [searchModalOpen, setSearchModalOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState<{
    value: any;
    display: string | null;
  } | null>(null);

  // Label style
  const labelStyle = {
    width: `${maxLabelWidth}px`,
    minWidth: `${maxLabelWidth}px`,
    fontSize: '12px',
    fontWeight: 400,
    marginRight: '8px',
    whiteSpace: 'nowrap' as const,
    overflow: 'visible' as const,
    display: 'inline-block'
  };

  // Input wrapper style
  const inputWrapperStyle = {
    flex: '1 1 auto',
    position: 'relative' as const,
    minWidth: minWidth ? (typeof minWidth === 'number' ? `${minWidth}px` : minWidth) : '0',
    display: 'flex',
    flexDirection: 'column' as const
  };

  // Container style
  const containerStyle = {
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    position: 'relative' as const,
    overflow: 'visible'
  };

  // Create a handler for search icon click
  const handleSearchClick = () => {
    setSearchModalOpen(true);
  };

  // Create a handler for search selection
  const handleSearchSelect = (selectedRow: any, setValue: Function) => {
    const firstColumnField = searchColumns[0]?.field || 'name';
    const selectedFieldValue = selectedRow[firstColumnField];

    const secondColumnField = searchColumns[1]?.field;
    const displayValue = secondColumnField ? selectedRow[secondColumnField] : null;

    setValue(key, selectedFieldValue, { shouldValidate: true });

    setSelectedValue({
      value: selectedFieldValue,
      display: displayValue
    });

    setSearchModalOpen(false);
  };

  // Colon element for view mode
  const colonElement = disabled && (
    <span
      style={{
        marginRight: '10px',
        fontSize: '12px',
        fontWeight: 400
      }}
    >
      :
    </span>
  );

  // Label content
  const labelContent = (
    <span style={labelStyle}>
      {label}
      {!disabled && required && <span style={{ color: 'red' }}>*</span>}
    </span>
  );

  return (
    <FormRow>
      {labelContent}
      <InputFieldWrapper style={inputWrapperStyle}>
        <div style={containerStyle}>
          {colonElement}
          <Controller
            name={key}
            render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
              <>
                {valueRender ? (
                  <StyledInput type='text' value={valueRender(value) || ''} disabled={true} />
                ) : (
                  <>
                    <StyledInput
                      type='text'
                      value={value || ''}
                      onChange={e => {
                        onChange(e);
                        if (selectedValue && e.target.value !== selectedValue.value) {
                          setSelectedValue(null);
                        }
                      }}
                      placeholder={!isViewMode ? placeholder : undefined}
                      disabled={disabled}
                      ref={ref}
                      style={{ paddingRight: '20px' }}
                    />
                    {!disabled && (
                      <SearchIconWrapper>
                        <IconButton
                          onClick={handleSearchClick}
                          size='small'
                          sx={{
                            boxSizing: 'border-box',
                            padding: '2px',
                            color: '#0b87c9',
                            border: '1px solid transparent',
                            cursor: 'default',
                            '&:hover': {
                              border: '1px solid rgb(65, 165, 218, 0.5)',
                              backgroundColor: 'rgba(32, 139, 197, 0.08)'
                            }
                          }}
                        >
                          <SearchIcon style={{ fontSize: '16px' }} />
                        </IconButton>
                        {selectedValue?.display && showSearchDisplay !== false && (
                          <span
                            style={{
                              position: 'absolute',
                              left: '28px',
                              color: 'black',
                              fontSize: '12px',
                              whiteSpace: 'nowrap',
                              zIndex: 10
                            }}
                          >
                            {selectedValue.display}
                          </span>
                        )}
                      </SearchIconWrapper>
                    )}
                    {searchModalOpen && (
                      <AritoSearchModal
                        visible={searchModalOpen}
                        onClose={() => setSearchModalOpen(false)}
                        onSelect={row => handleSearchSelect(row, onChange)}
                        title={searchModalTitle || `Tìm kiếm ${label}`}
                        columns={searchColumns}
                        docType={docType}
                        filters={searchFilters}
                      />
                    )}
                  </>
                )}
                {error && <ErrorMessage>{error.message}</ErrorMessage>}
              </>
            )}
          />
        </div>
      </InputFieldWrapper>
    </FormRow>
  );
};

export default SearchField;
