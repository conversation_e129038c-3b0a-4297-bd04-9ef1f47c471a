import { useState, useEffect } from 'react';
import AritoIcon from '../icon';

// Define the tree node structure with generic typing for API compatibility
export interface CheckboxTreeNode {
  id: string;
  label: string;
  children?: CheckboxTreeNode[];
  [key: string]: any; // Allow for additional properties from API data
}

interface CheckboxTreeProps {
  data: CheckboxTreeNode[];
  onSelectionChange?: (selectedIds: string[]) => void;
  initialSelected?: string[];
  className?: string;
  indentSize?: number;
  initialExpandedIds?: string[];
}

const CheckboxTree = ({
  data,
  onSelectionChange,
  initialSelected = [],
  className = '',
  indentSize = 24,
  initialExpandedIds = []
}: CheckboxTreeProps) => {
  const [checkedState, setCheckedState] = useState<Record<string, boolean>>({});
  const [expandedState, setExpandedState] = useState<Record<string, boolean>>({});

  // Initialize with initial selected and expanded values
  useEffect(() => {
    // Set initial checked nodes
    if (initialSelected.length > 0) {
      const newCheckedState: Record<string, boolean> = {};
      initialSelected.forEach(id => {
        newCheckedState[id] = true;
      });
      setCheckedState(newCheckedState);
    }

    // Set initial expanded nodes, default to expanding all parent nodes
    const newExpandedState: Record<string, boolean> = {};

    if (initialExpandedIds.length > 0) {
      initialExpandedIds.forEach(id => {
        newExpandedState[id] = true;
      });
    } else {
      // By default, expand all parent nodes with children
      const expandAllParents = (nodes: CheckboxTreeNode[]) => {
        nodes.forEach(node => {
          if (node.children && node.children.length > 0) {
            newExpandedState[node.id] = true;
            expandAllParents(node.children);
          }
        });
      };
      expandAllParents(data);
    }

    setExpandedState(newExpandedState);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Notify parent component when selection changes
  useEffect(() => {
    if (onSelectionChange) {
      const selectedIds = Object.entries(checkedState)
        .filter(([_, isChecked]) => isChecked)
        .map(([id]) => id);
      onSelectionChange(selectedIds);
    }
  }, [checkedState, onSelectionChange]);

  // Function to get all child IDs of a node
  const getAllChildIds = (node: CheckboxTreeNode): string[] => {
    let ids: string[] = [];
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        ids.push(child.id);
        ids = [...ids, ...getAllChildIds(child)];
      });
    }
    return ids;
  };

  // Function to get all parent IDs of a node
  const getAllParentIds = (nodes: CheckboxTreeNode[], targetId: string, path: string[] = []): string[] => {
    for (const node of nodes) {
      if (node.id === targetId) {
        return path;
      }

      if (node.children && node.children.length > 0) {
        const foundPath = getAllParentIds(node.children, targetId, [...path, node.id]);
        if (foundPath.length > 0) {
          return foundPath;
        }
      }
    }
    return [];
  };

  // Handle checkbox changes
  const handleCheckboxChange = (node: CheckboxTreeNode, checked: boolean) => {
    const newCheckedState = { ...checkedState };

    // Update this node
    newCheckedState[node.id] = checked;

    // Update all children
    if (node.children && node.children.length > 0) {
      const childIds = getAllChildIds(node);
      childIds.forEach(childId => {
        newCheckedState[childId] = checked;
      });
    }

    // Update parents based on siblings' state
    const updateParentState = (nodes: CheckboxTreeNode[], targetId: string) => {
      const parentIds = getAllParentIds(nodes, targetId);

      parentIds.forEach(parentId => {
        const parentNode = findNodeById(nodes, parentId);
        if (parentNode && parentNode.children) {
          const allChildrenChecked = parentNode.children.every(child => newCheckedState[child.id] || false);
          newCheckedState[parentId] = allChildrenChecked;
        }
      });
    };

    updateParentState(data, node.id);

    setCheckedState(newCheckedState);
  };

  // Toggle expand/collapse state
  const toggleExpand = (nodeId: string, e: React.MouseEvent) => {
    // Stop event propagation to prevent triggering checkbox
    e.stopPropagation();

    setExpandedState(prev => ({
      ...prev,
      [nodeId]: !prev[nodeId]
    }));
  };

  // Helper to find a node by ID
  const findNodeById = (nodes: CheckboxTreeNode[], id: string): CheckboxTreeNode | null => {
    for (const node of nodes) {
      if (node.id === id) return node;
      if (node.children && node.children.length > 0) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  // Check if node has children
  const hasChildren = (node: CheckboxTreeNode): boolean => {
    return !!(node.children && node.children.length > 0);
  };

  // Toggle icon component
  const ToggleIcon = ({
    nodeId,
    expanded,
    hasChildren,
    onToggle
  }: {
    nodeId: string;
    expanded: boolean;
    hasChildren: boolean;
    onToggle: (id: string, e: React.MouseEvent) => void;
  }) => {
    if (!hasChildren) return <span className='inline-block h-4 w-4'></span>;

    return (
      <span className='inline-block h-4 w-4 cursor-pointer' onClick={e => onToggle(nodeId, e)}>
        {expanded ? (
          <svg
            className='h-4 w-4 text-gray-500'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path strokeLinecap='round' strokeLinejoin='round' strokeWidth='2' d='M19 9l-7 7-7-7' />
          </svg>
        ) : (
          <svg
            className='h-4 w-4 text-gray-500'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path strokeLinecap='round' strokeLinejoin='round' strokeWidth='2' d='M9 5l7 7-7 7' />
          </svg>
        )}
      </span>
    );
  };

  // Recursive component to render a node and its children
  const renderNode = (node: CheckboxTreeNode, level = 0) => {
    const isChecked = checkedState[node.id] || false;
    const isExpanded = expandedState[node.id] || false;
    const nodeHasChildren = hasChildren(node);

    return (
      <div key={node.id} className='my-1'>
        <div className='flex items-center' style={{ paddingLeft: `${level * indentSize}px` }}>
          <ToggleIcon nodeId={node.id} expanded={isExpanded} hasChildren={nodeHasChildren} onToggle={toggleExpand} />

          <input
            type='checkbox'
            id={`checkbox-${node.id}`}
            checked={isChecked}
            onChange={e => handleCheckboxChange(node, e.target.checked)}
            className='ml-1 h-4 w-4 rounded border-gray-300'
          />
          <label htmlFor={`checkbox-${node.id}`} className='ml-2 cursor-pointer select-none'>
            {node.label}
          </label>
        </div>

        {nodeHasChildren && isExpanded && <div>{node.children!.map(child => renderNode(child, level + 1))}</div>}
      </div>
    );
  };

  return <div className={`checkbox-tree ${className}`}>{data.map(node => renderNode(node))}</div>;
};

export default function CheckBoxTree() {
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // Sample data with three levels of nesting
  const sampleData: CheckboxTreeNode[] = [
    // Thời gian
    {
      id: 'time',
      label: 'Thời gian',
      children: [
        {
          id: 'time-old',
          label: 'Thời gian cũ',
          children: [
            { id: 'month-2019-01', label: 'Th01.2019 (01/01-31/01)' },
            { id: 'month-2019-02', label: 'Th02.2019 (01/02-28/02)' },
            { id: 'month-2019-03', label: 'Th03.2019 (01/03-31/03)' },
            { id: 'month-2019-04', label: 'Th04.2019 (01/04-30/04)' },
            { id: 'month-2019-05', label: 'Th05.2019 (01/05-31/05)' },
            { id: 'month-2019-06', label: 'Th06.2019 (01/06-30/06)' },
            { id: 'month-2019-07', label: 'Th07.2019 (01/07-31/07)' },
            { id: 'month-2019-08', label: 'Th08.2019 (01/08-31/08)' },
            { id: 'month-2019-09', label: 'Th09.2019 (01/09-30/09)' },
            { id: 'month-2019-10', label: 'Th10.2019 (01/10-31/10)' },
            { id: 'month-2019-11', label: 'Th11.2019 (01/11-30/11)' },
            { id: 'month-2019-12', label: 'Th12.2019 (01/12-31/12)' },
            { id: 'month-2020-01', label: 'Th01.2020 (01/01-31/01)' },
            { id: 'month-2020-02', label: 'Th02.2020 (01/02-29/0...)' },
            { id: 'month-2020-03', label: 'Th03.2020 (01/03-31/03)' },
            { id: 'month-2020-04', label: 'Th04.2020 (01/04-30/...)' },
            { id: 'month-2020-05', label: 'Th05.2020 (01/05-31/05)' },
            { id: 'month-2020-06', label: 'Th06.2020 (01/06-30/06)' },
            { id: 'month-2020-07', label: 'Th07.2020 (01/07-31/07)' }
          ]
        }
      ]
    },

    // BỘ phận
    {
      id: 'section',
      label: 'Bộ phận'
    },

    // Đơn từ
    {
      id: 'document',
      label: 'Đơn từ',
      children: [
        { id: 'design-doc', label: 'ĐƠN TỰ THIẾT KẾ' },
        { id: 'leave-request', label: 'Đơn xin nghỉ' },
        { id: 'document', label: 'Tài liệu' },
        { id: 'timekeeping', label: 'Đơn chấm công' },
        { id: 'overtime-request', label: 'Đơn tăng ca' },
        { id: 'out-work-request', label: 'Đơn xin l.việc ngoài' },
        { id: 'work-trip-request', label: 'Đơn công tác' },
        { id: 'shift-change-request', label: 'Đơn đổi ca' },
        { id: 'advance-request', label: 'Đơn nhập tiền' },
        { id: 'overtime-register', label: 'Đăng ký tăng ca' },
        { id: 'resource-register', label: 'Đăng ký tài nguyên' },
        { id: 'recruitment-request', label: 'Yêu cầu tuyển dụng' },
        { id: 'payment-proposal', label: 'Đề nghị thanh toán' },
        { id: 'sales-order', label: 'Đơn hàng bán' },
        { id: 'material-requisition', label: 'Phiếu nhu cầu vật tư' },
        { id: 'supplier-selection', label: 'Chọn nhà cung cấp' },
        { id: 'domestic-purchase-order', label: 'Đơn hàng mua trong nước' },
        { id: 'import-purchase-order', label: 'Đơn hàng mua nhập khẩu' },
        { id: 'warehouse-export-requisition', label: 'Phiếu yêu cầu xuất kho' },
        { id: 'repair-proposal', label: 'Phiếu báo đề nghị sửa chữa...' },
        { id: 'price-comparison', label: 'So sánh giá' }
      ]
    }
  ];

  return (
    <div className='p-4'>
      <h2 className='mb-4 text-lg font-medium'>
        <AritoIcon icon={539} /> Lọc
      </h2>
      <div className='mb-4 max-h-[500px] overflow-auto border-t text-sm'>
        <CheckboxTree data={sampleData} onSelectionChange={setSelectedIds} />
      </div>
    </div>
  );
}
