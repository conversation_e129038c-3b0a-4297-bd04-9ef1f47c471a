import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';

interface DocumentNumberRangeProps {
  fromDocumentNumberName: string;
  toDocumentNumberName: string;
  className?: string;
}

const DocumentNumberRange: React.FC<DocumentNumberRangeProps> = ({
  fromDocumentNumberName,
  toDocumentNumberName,
  className
}) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <FormField name={fromDocumentNumberName} label='' type='text' className='w-32' />
      <FormField name={toDocumentNumberName} label='' type='text' className='w-32' />
    </div>
  );
};

export default DocumentNumberRange;
