import { styled } from '@mui/material/styles';
import { StyledComponentProps } from '../types';

// Styled textarea component
export const StyledTextArea = styled('textarea')<StyledComponentProps>(({ theme, disabled }) => ({
  width: '100%',
  minHeight: '60px',
  resize: disabled ? 'none' : 'vertical',
  border: 'none',
  borderBottom: disabled ? 'none' : '1px solid #ddd',
  fontSize: disabled ? '13px' : '12px',
  fontWeight: disabled ? 600 : 400,
  padding: '2px 4px',
  backgroundColor: 'transparent',
  '&:focus': {
    outline: 'none',
    borderBottomColor: '#0b87c9'
  },
  '&:disabled': {
    backgroundColor: 'transparent',
    cursor: 'default'
  }
}));

export default StyledTextArea;
