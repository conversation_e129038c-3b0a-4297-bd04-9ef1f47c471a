import { FieldConfig } from '@/components/arito/arito-form-fields';
import { TabConfig } from '@/components/arito/arito-filter-modal';

export const updateEndingWIPQuantityFilterFields: FieldConfig[] = [
  {
    key: 'ky',
    name: 'ky',
    label: '<PERSON><PERSON>',
    type: 'text',
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 4
    }
  },
  {
    key: 'nam',
    name: 'nam',
    label: 'Năm',
    type: 'text',
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 4
    }
  },
  {
    key: 'don_vi',
    name: 'don_vi',
    label: 'Đơn vị',
    type: 'select',
    options: [
      { value: '0318423416', label: '0318423416 - CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ' }
    ],
    gridPosition: {
      row: 2,
      col: 0,
      colSpan: 6
    }
  }
];

export const updateEndingWIPQuantityFilterTabs: TabConfig[] = [];

export const updateEndingWIPQuantityFields: FieldConfig[] = [
  {
    key: 'loai_yeu_to',
    name: 'loai_yeu_to',
    label: 'Loại yếu tố',
    type: 'search',
    searchModalTitle: 'Danh mục loại yếu tố',
    searchColumns: [
      { field: 'ma_loai_yeu_to', headerName: 'Mã loại yếu tố', width: 150 },
      { field: 'ten_loai_yeu_to', headerName: 'Tên loại yếu tố', width: 300 }
    ],
    docType: 'FactorType',
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'ma_san_pham',
    name: 'ma_san_pham',
    label: 'Mã sản phẩm',
    type: 'search',
    searchModalTitle: 'Danh mục sản phẩm',
    searchColumns: [
      { field: 'ma_vat_tu', headerName: 'Mã vật tư', width: 150 },
      { field: 'ten_vat_tu', headerName: 'Tên vật tư', width: 300 },
      { field: 'dvt', headerName: 'Đvt lẻ', width: 100 }
    ],
    docType: 'Product',
    gridPosition: {
      row: 2,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'sl_do_dang',
    name: 'sl_do_dang',
    label: 'Số lượng dở dang',
    type: 'number',
    defaultValue: 0,
    gridPosition: {
      row: 3,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'tl_hoan_thanh',
    name: 'tl_hoan_thanh',
    label: 'Tỉ lệ hoàn thành',
    type: 'number',
    defaultValue: 0,
    gridPosition: {
      row: 4,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'sl_quy_doi',
    name: 'sl_quy_doi',
    label: 'Số lượng quy đổi',
    type: 'number',
    defaultValue: 0,
    gridPosition: {
      row: 5,
      col: 0,
      colSpan: 6
    }
  }
];

export const updateEndingWIPQuantityTabs: TabConfig[] = [];
