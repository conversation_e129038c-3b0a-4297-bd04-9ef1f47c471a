import { FormControlLabel, Radio, RadioGroup } from '@mui/material';
import { Controller, useFormContext } from 'react-hook-form';
import React from 'react';
import { InputFieldWrapper, ErrorMessage } from '../components/InputFieldWrapper';
import { FormRow } from '../components/FormRow';
import { CommonFieldProps } from '../types';

export const RadioField: React.FC<CommonFieldProps> = ({ field, maxLabelWidth, isViewMode }) => {
  const { key, name, label, required, disabled = isViewMode, options = [], minWidth, valueRender } = field;

  const { control } = useFormContext();

  // Label style
  const labelStyle = {
    width: `${maxLabelWidth}px`,
    minWidth: `${maxLabelWidth}px`,
    fontSize: '12px',
    fontWeight: 400,
    marginRight: '8px',
    whiteSpace: 'nowrap' as const,
    overflow: 'visible' as const,
    display: 'inline-block'
  };

  // Input wrapper style
  const inputWrapperStyle = {
    flex: '1 1 auto',
    position: 'relative' as const,
    minWidth: minWidth ? (typeof minWidth === 'number' ? `${minWidth}px` : minWidth) : '0',
    display: 'flex',
    flexDirection: 'column' as const
  };

  // Container style
  const containerStyle = {
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    position: 'relative' as const,
    overflow: 'visible'
  };

  // Colon element for view mode
  const colonElement = disabled && (
    <span
      style={{
        marginRight: '10px',
        fontSize: '12px',
        fontWeight: 400
      }}
    >
      :
    </span>
  );

  // Label content
  const labelContent = (
    <span style={labelStyle}>
      {label}
      {!disabled && required && <span style={{ color: 'red' }}>*</span>}
    </span>
  );

  return (
    <FormRow>
      {labelContent}
      <InputFieldWrapper style={inputWrapperStyle}>
        <div style={containerStyle}>
          {colonElement}
          {valueRender ? (
            <span>{valueRender({}) || ''}</span>
          ) : (
            <Controller
              name={name || key}
              control={control}
              render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                <>
                  <RadioGroup
                    row
                    value={value || ''}
                    onChange={onChange}
                    ref={ref}
                    sx={{
                      '& .MuiFormControlLabel-root': {
                        marginRight: '30px'
                      },
                      '& .MuiRadio-root': {
                        padding: '4px'
                      },
                      '& .MuiFormControlLabel-label': {
                        fontSize: '12px'
                      }
                    }}
                  >
                    {options.map(option => (
                      <FormControlLabel
                        key={option.value}
                        value={option.value}
                        control={<Radio size='small' disabled={disabled} />}
                        label={option.label}
                        disabled={disabled}
                      />
                    ))}
                  </RadioGroup>
                  {error && <ErrorMessage>{error.message}</ErrorMessage>}
                </>
              )}
            />
          )}
        </div>
      </InputFieldWrapper>
    </FormRow>
  );
};

export default RadioField;
