import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const HDDTTabType1 = ({ formMode }: Props) => {
  return (
    <div className='space-y-4 p-4'>
      <FormField
        className='flex max-w-[300px] items-center justify-between'
        label='Sự dụng HĐĐT'
        labelClassName='min-w-[100px]'
        type='select'
        name='status'
        disabled={formMode === 'view'}
        options={[
          { label: '1. Có', value: 'not_used' },
          { label: '2. Không', value: 'wait_for_release' }
        ]}
      />
      <FormField
        label='Thư nhận HĐĐT'
        className='flex max-w-[300px] items-center justify-between'
        name='invoiceNumber'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        label='Người đại diện'
        className='flex items-center justify-between'
        name='representative'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />
    </div>
  );
};

export default HDDTTabType1;
