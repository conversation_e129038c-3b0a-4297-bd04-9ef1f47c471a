'use client';

import { useState } from 'react';
import Split from 'react-split';
import { SearchDialog, BottomBar, ActionBar, BasicInfo } from './components';
import { ExchangeRateTab, DetailsTab, OtherTab } from './components/tabs';
import { AritoForm, AritoDataTables } from '@/components/custom/arito';
import { useCalculations, useTableData, useFormState } from './hooks';
import { AccountModel } from '@/types/schemas';

export default function ButToanDieuChinhCongNo() {
  const {
    showForm,
    setShowForm,
    showSearchDialog,
    setShowSearchDialog,
    formMode,
    handleAdd,
    handleEdit,
    handleDelete,
    handleCopy,
    handleSearch,
    handleSearchSubmit,
    handleRowClick
  } = useFormState();

  const { detailRows, tables } = useTableData();

  const { totalAmount } = useCalculations(detailRows);
  const [account, setAccount] = useState<AccountModel | null>(null);
  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog
          open={showSearchDialog}
          onClose={() => setShowSearchDialog(false)}
          onSearch={handleSearchSubmit}
        />
      )}

      {showForm && (
        <AritoForm
          mode={formMode}
          title={formMode === 'add' ? 'Mới' : undefined}
          subTitle='Hóa đơn bán hàng'
          onClose={() => setShowForm(false)}
          headerFields={<BasicInfo formMode={formMode} account={account} setAccount={setAccount} />}
          bottomBar={<BottomBar totalAmount={totalAmount} />}
          tabs={[
            {
              id: '1',
              label: 'Chi tiết',
              component: <DetailsTab formMode={formMode} />
            },
            {
              id: '2',
              label: 'Tỷ giá',
              component: <ExchangeRateTab formMode={formMode} />
            },
            {
              id: '3',
              label: 'Khác',
              component: <OtherTab formMode={formMode} />
            }
          ]}
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAdd={handleAdd}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onCopy={handleCopy}
            onSearch={handleSearch}
          />

          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={4}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
