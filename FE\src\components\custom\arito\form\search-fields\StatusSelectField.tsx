import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface StatusSelectFieldProps {
  name?: string;
  label?: string;
  labelClassName?: string;
  className?: string;
  inputClassName?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
  options?: { value: string; label: string }[];
}

// Default status options
const defaultStatusOptions = [
  { value: 'all', label: 'Tất cả' },
  { value: 'not_posted', label: 'Chưa ghi sổ' },
  { value: 'pending', label: 'Chờ duyệt' },
  { value: 'posted', label: 'Đã ghi sổ' }
];

export const StatusSelectField: React.FC<StatusSelectFieldProps> = ({
  name = 'status',
  label = 'Trạng thái',
  labelClassName = 'text-sm font-normal sm:mb-0 text-left flex items-center mt-3',
  className = 'flex items-center',
  inputClassName = 'w-[114px]',
  disabled = false,
  formMode = 'add',
  options = defaultStatusOptions
}) => {
  return (
    <div className={className}>
      <Label className={cn(labelClassName)}>{label}</Label>
      <FormField
        name={name}
        type='select'
        className={inputClassName}
        label=''
        options={options}
        disabled={disabled || formMode === 'view'}
      />
    </div>
  );
};

export default StatusSelectField;
