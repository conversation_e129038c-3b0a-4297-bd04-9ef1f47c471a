import React, { useState } from 'react';

import { Button } from '@mui/material';
import { GeneralTab, FilterByObjectTab, OtherTab, BasicInfo } from './tabs';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { searchSchema, initialValues } from '../schema';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  // Create a state to store form values
  const [formValues, setFormValues] = useState(initialValues);

  const handleSubmit = (data: any) => {
    console.log('Form submitted with data:', data);
    onSearch(data);
    // Don't close the dialog here - the parent component will handle this
  };

  // Function to directly submit the form values and close the dialog
  const handleDirectSubmit = () => {
    onSearch(formValues);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Bảng kê hoá đơn bán hàng, dịch vụ'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          setFormValues(data);
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto' style={{ width: '800px', minWidth: '800px' }}>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'general',
                  label: 'Thông tin chung',
                  component: <GeneralTab />
                },
                {
                  id: 'filter-by-object',
                  label: 'Lọc theo đối tượng',
                  component: <FilterByObjectTab />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 px-2'
        bottomBar={
          <>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>

            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};
