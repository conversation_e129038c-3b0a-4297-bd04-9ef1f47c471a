import { TextField } from '@mui/material';
import React from 'react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { useSearchResults, useSelection } from './hooks';
import { searchInputFieldStyle } from './styles';
import { SearchIcon } from '@/components/icons';
import { getFormattedColumns } from './columns';

interface DataTableProps<T = any> {
  searchEndpoint?: string;
  searchColumns?: any[];
  columnDisplay?: keyof T;
  rows?: T[];
  onRowSelection?: (row: T) => void;
  onSelectedObjChange?: (obj: T | null) => void;
}

export const DataTable = <T = any,>({
  searchEndpoint,
  searchColumns = [],
  columnDisplay = '' as keyof T,
  rows,
  onRowSelection,
  onSelectedObjChange
}: DataTableProps<T>) => {
  const { searchResults, isLoading } = useSearchResults<T>(searchEndpoint);
  const { selectedObj, selectedRowIndex, handleRowClick } = useSelection<T>();

  // Update parent component when selectedObj changes
  React.useEffect(() => {
    onSelectedObjChange?.(selectedObj);
  }, [selectedObj, onSelectedObjChange]);

  const tableData = [
    {
      name: '',
      rows: rows || searchResults,
      columns: getFormattedColumns(searchColumns)
    }
  ];

  return (
    <>
      <div className='flex items-center border-b border-gray-200 px-4 py-2'>
        <TextField
          variant='outlined'
          placeholder='Tìm kiếm...'
          size='small'
          value=''
          onChange={() => {}}
          sx={searchInputFieldStyle}
          InputProps={{
            startAdornment: <SearchIcon size={14} color='#2563EB' />
          }}
        />
      </div>

      <div className='flex size-full flex-col border border-[#eaeaea] bg-white'>
        {isLoading && (
          <div className='flex h-64 items-center justify-center'>
            <div className='h-8 w-8 animate-spin rounded-full border-2 border-[rgba(15,118,110,0.9)] border-t-transparent'></div>
          </div>
        )}

        {!isLoading && (
          <AritoDataTables
            tables={tableData}
            onRowClick={(params: any) => {
              handleRowClick(params);
              onRowSelection?.(params.row);
            }}
            selectedRowId={selectedRowIndex || undefined}
            getRowId={row => row.uuid || String(row[columnDisplay])}
          />
        )}
      </div>
    </>
  );
};
