import Link from 'next/link';
import React from 'react';
import { DonViTinhColumns, NhomColumns, MaKhoSearchCol, MaViTriSearchCol, MaThueSearchCol } from '../cols-definition';
import BasicInfoTabType16 from '../../popup-form-type-16/BasicInfoTabType16';
import BasicInfoTabType10 from '../../popup-form-type-10/BasicInfoTabType10';
import BasicInfoTabType3 from '../../popup-form-type-3/BasicInfoTabType3';
import { FormField } from '@/components/custom/arito/form/form-field';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const GeneralTab = ({ formMode }: Props) => {
  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-y-4'>
        <FormField
          className='flex items-center'
          inputClassName='w-[19%]'
          label='Đơn vị tính'
          name='don_vi_tinh'
          labelClassName='min-w-[150px] text-sm font-medium'
          type='text'
          disabled={formMode === 'view'}
          withSearch
          searchEndpoint='unit'
          searchColumns={DonViTinhColumns}
          actionButtons={['add', 'edit']}
          headerFields={<BasicInfoTabType16 formMode={formMode} />}
        />

        <div className='grid w-full grid-cols-[1.6fr,1fr,1fr] items-center gap-x-6'>
          <FormField
            className='flex items-center'
            label='Cách tính giá tồn kho'
            name='cach_tinh_gia_ton_kho'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='select'
            disabled={formMode === 'view'}
            options={[
              { value: '0', label: '0. Theo khai báo đơn vị' },
              { value: '1', label: '1. Giá trung bình' },
              { value: '2', label: '2. Nhập trước xuất trước' },
              { value: '3', label: '3. Giá đích danh' },
              { value: '4', label: '4. Giá trung bình di động' }
            ]}
          />
          <Link href='/'>
            <span className='ml-3 text-sm text-blue-500'>(Khai báo trên đơn vị)</span>
          </Link>
        </div>

        <FormField
          className='flex w-[54%] items-center'
          label='Loại vật tư'
          name='loai_vat_tu'
          labelClassName='min-w-[150px] text-sm font-medium'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: '00', label: '00. Dịch vụ' },
            { value: '21', label: '21. Vật tư' },
            { value: '22', label: '22. Phụ tùng' },
            { value: '31', label: '31. CCLD' },
            { value: '41', label: '41. Bán thành phẩm' },
            { value: '51', label: '51. Thành phẩm' },
            { value: '61', label: '61. Hàng hóa' },
            { value: '71', label: '71. Hàng gia công' }
          ]}
        />

        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Nhóm 1'
            name='nhom_1'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='group'
            searchColumns={NhomColumns}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType10 formMode={formMode} />}
          />
        </div>

        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Nhóm 2'
            name='nhom_2'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='group'
            searchColumns={NhomColumns}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType10 formMode={formMode} />}
          />
        </div>

        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Nhóm 3'
            name='nhom_3'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='group'
            searchColumns={NhomColumns}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType10 formMode={formMode} />}
          />
        </div>

        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Mã kho mặc định'
            name='ma_kho_mac_dinh'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='warehouse'
            searchColumns={MaKhoSearchCol}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType3 formMode={formMode} />}
          />
        </div>

        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Vị trí mặc định'
            name='vi_tri_mac_dinh'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='location'
            searchColumns={MaViTriSearchCol}
          />
        </div>

        <div className='flex items-center gap-x-6'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Mã thuế mặc định'
            name='ma_thue_mac_dinh'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='tax'
            searchColumns={MaThueSearchCol}
          />
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label=''
            name='nhap_khau'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='tax'
            placeholder='Nhập khẩu'
            searchColumns={MaThueSearchCol}
          />
        </div>
      </div>
    </div>
  );
};

export default GeneralTab;
