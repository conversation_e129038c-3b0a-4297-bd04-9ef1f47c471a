import { Box, TextField } from '@mui/material';
import { SearchFieldProps } from '../../types';

export const SearchInput = ({
  field,
  fieldId,
  disabled,
  type,
  displayText,
  onSearchClick,
  onInputChange
}: SearchFieldProps) => {
  return (
    <Box sx={{ position: 'relative', width: '100%' }}>
      <TextField
        name={field.name}
        ref={field.ref}
        onBlur={field.onBlur}
        type={type}
        disabled={disabled}
        fullWidth
        size='small'
        variant='standard'
        id={fieldId}
        value={displayText || field.value || ''}
        onChange={e => {
          const newValue = e.target.value;
          onInputChange(newValue);
        }}
        sx={{
          '& .MuiInput-root': {
            fontSize: '14px',
            '&:before': {
              borderBottom: '1px solid #e5e7eb'
            },
            '&:hover:not(.Mui-disabled):before': {
              borderBottom: '1px solid #2563EB'
            },
            '&.Mui-focused:after': {
              borderBottom: '1px solid #2563EB'
            }
          },
          '& .MuiInput-input': {
            padding: '4px 8px',
            paddingRight: '28px',
            textAlign: { xs: 'left', sm: 'left' }
          },
          marginTop: type === 'number' ? '8px' : '0px'
        }}
        InputProps={{
          endAdornment: (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'text.secondary',
                cursor: 'pointer',
                position: 'absolute',
                right: 4,
                '&:hover': {
                  color: '#2563EB'
                }
              }}
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                onSearchClick();
              }}
            >
              <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 20 20' fill='#2563EB'>
                <path
                  fillRule='evenodd'
                  d='M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z'
                  clipRule='evenodd'
                />
              </svg>
            </Box>
          )
        }}
      />
    </Box>
  );
};
