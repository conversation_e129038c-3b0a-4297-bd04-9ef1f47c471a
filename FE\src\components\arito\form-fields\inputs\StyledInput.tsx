import { styled } from '@mui/material/styles';
import { StyledComponentProps } from '../types';

// Base input with only bottom border
export const StyledInput = styled('input')<StyledComponentProps>(({ theme, disabled }) => ({
  border: 'none',
  borderBottom: disabled ? 'none' : '1px solid #ddd',
  padding: '4px 2px',
  fontSize: disabled ? '13px' : '12px',
  fontWeight: disabled ? 600 : 400,
  backgroundColor: 'transparent',
  width: '100%',
  height: '22px',
  overflow: 'visible',
  textOverflow: 'clip',
  whiteSpace: 'normal',
  '&:focus': {
    outline: 'none',
    borderBottomColor: '#0b87c9',
    borderBottomWidth: '1px'
  },
  '&:disabled': {
    backgroundColor: 'transparent',
    cursor: 'default'
  }
}));

// Number input with right alignment (only in edit mode)
export const NumberInput = styled(StyledInput)(({ disabled }) => ({
  textAlign: disabled ? 'left' : 'right',
  paddingRight: disabled ? '2px' : '0px',
  '&::-webkit-inner-spin-button, &::-webkit-outer-spin-button': {
    WebkitAppearance: 'none',
    margin: 0,
    appearance: 'none'
  },
  '&[type=number]': {
    MozAppearance: 'textfield'
  }
}));

export default StyledInput;
