import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface DocumentNumberRangeFieldProps {
  label?: string;
  fromName?: string;
  toName?: string;
  labelClassName?: string;
  className?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
}

export const DocumentNumberRangeField: React.FC<DocumentNumberRangeFieldProps> = ({
  label = 'Số c/từ (từ/đến)',
  fromName = 'voucherNumberStart',
  toName = 'voucherNumberEnd',
  labelClassName = 'text-sm font-normal text-[13px] pr-2 sm:mb-0 text-left flex items-center min-w-[150px]',
  className = 'flex items-center',
  disabled = false,
  formMode = 'add'
}) => {
  return (
    <div className={className}>
      <Label className={labelClassName}>{label}</Label>
      <div className='flex flex-1 gap-2'>
        <FormField name={fromName} type='text' className='w-full' label='' disabled={disabled || formMode === 'view'} />
        <FormField name={toName} type='text' className='w-full' label='' disabled={disabled || formMode === 'view'} />
      </div>
    </div>
  );
};

export default DocumentNumberRangeField;
