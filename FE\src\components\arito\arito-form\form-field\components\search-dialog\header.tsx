import { Box, IconButton, Typography } from '@mui/material';
import { FullscreenIcon, MinimizeIcon } from 'lucide-react';
import AritoIcon from '@/components/custom/arito/icon';

interface DialogHeaderProps {
  title: string;
  isFullScreen: boolean;
  isMobile: boolean;
  onToggleFullScreen: () => void;
}

export const DialogHeader = ({ title, isFullScreen, isMobile, onToggleFullScreen }: DialogHeaderProps) => {
  return (
    <Box
      sx={{
        backgroundColor: '#f2f7fc',
        color: '#000000',
        fontWeight: 'bold',
        fontSize: '18px',
        py: 1,
        px: 2,
        borderBottom: '1px solid #d1d5db',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        minHeight: '40px',
        height: '40px'
      }}
    >
      <Box
        sx={{
          fontSize: '16px',
          color: '#666666',
          display: 'flex',
          alignItems: 'center',
          marginLeft: '-12px'
        }}
      >
        <AritoIcon icon={584} marginX='8px' />
        <Typography component='span'>{title}</Typography>
      </Box>

      {!isMobile && (
        <IconButton
          size='small'
          onClick={onToggleFullScreen}
          title={isFullScreen ? 'Thoát toàn màn hình' : 'Toàn màn hình'}
          sx={{
            marginLeft: 'auto',
            padding: '4px'
          }}
        >
          {isFullScreen ? <MinimizeIcon fontSize='small' /> : <FullscreenIcon fontSize='small' />}
        </IconButton>
      )}
    </Box>
  );
};
