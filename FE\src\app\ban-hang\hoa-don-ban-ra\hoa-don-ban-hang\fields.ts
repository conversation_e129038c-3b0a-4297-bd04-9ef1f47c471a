import {
  purchaseInvoiceItemInputColumns,
  discountColumns,
  discountDetailColumns,
  paymentInfoColumns,
  purchaseInvoiceItemColumns2
} from './columns';
import { FieldConfig } from '@/components/arito/arito-form-fields';

interface TabConfig {
  key: string;
  title: string;
  fields: FieldConfig[];
  columns?: number;
}

// General tabs configuration
export const purchaseInvoiceGeneralTabs: TabConfig[] = [
  {
    key: 'thongTin',
    title: 'Thông tin',
    columns: 10,
    fields: [
      {
        key: 'customerCode',
        name: 'customerCode',
        label: 'Mã khách hàng',
        type: 'search' as const,
        docType: 'Customer',
        searchModalTitle: 'Danh mục khách hàng',
        searchColumns: [
          { field: 'name', headerName: 'Mã khách hàng', width: 110 },
          { field: 'customer_name', headerName: '<PERSON>ên khách hàng', width: 220 },
          { field: '?', headerName: 'Công nợ p/thu', width: 110 },
          { field: '??', headerName: 'Công nợ p/trả', width: 110 },
          { field: 'tax_code', headerName: 'Mã số thuế', width: 110 },
          { field: 'email', headerName: 'Email', width: 110 }
        ],
        required: true,
        minWidth: 120,
        gridPosition: {
          row: 0,
          col: 0,
          colSpan: 1
        }
      },
      {
        key: 'permissionNumber',
        name: 'permissionNumber',
        label: 'Quyền/Số',
        type: 'compound' as const,
        minWidth: 120,
        inputs: [
          {
            type: 'search',
            key: 'Quyen',
            name: 'quyen',
            widthPercentage: 40
          },
          {
            type: 'text',
            key: 'So',
            name: 'so',
            widthPercentage: 60
          }
        ],
        gridPosition: {
          row: 0,
          col: 9,
          colSpan: 1
        }
      },
      {
        key: 'recipient',
        name: 'recipient',
        label: 'Người nhận',
        type: 'text' as const,
        minWidth: 120,
        gridPosition: {
          row: 1,
          col: 0,
          colSpan: 9
        }
      },
      {
        key: 'documentDate',
        name: 'documentDate',
        label: 'Ngày chứng từ',
        type: 'date' as const,
        required: true,
        minWidth: 120,
        gridPosition: {
          row: 1,
          col: 9,
          colSpan: 1
        }
      },
      {
        key: 'debitAccount',
        name: 'debitAccount',
        label: 'Tài khoản nợ',
        type: 'search' as const,
        minWidth: 120,
        gridPosition: {
          row: 2,
          col: 0,
          colSpan: 1
        }
      },
      {
        key: 'symbol',
        name: 'symbol',
        label: 'Ký hiệu',
        type: 'text' as const,
        minWidth: 120,
        gridPosition: {
          row: 2,
          col: 9,
          colSpan: 1
        }
      },
      {
        key: 'paymentCode',
        name: 'paymentCode',
        label: 'Mã thanh toán',
        type: 'search' as const,
        minWidth: 120,
        gridPosition: {
          row: 3,
          col: 0,
          colSpan: 1
        }
      },
      {
        key: 'foreignCurrency',
        name: 'foreignCurrency',
        label: 'Ngoại tệ',
        type: 'compound' as const,
        minWidth: 120,
        inputs: [
          {
            type: 'select',
            key: 'currency',
            name: 'currency',
            widthPercentage: 45,
            options: [
              { value: 'VND', label: 'VND' },
              { value: 'USD', label: 'USD' },
              { value: 'EUR', label: 'EUR' },
              { value: 'JPY', label: 'JPY' },
              { value: 'THB', label: 'THB' }
            ]
          },
          {
            type: 'text',
            key: 'exchangeRate',
            name: 'exchangeRate',
            widthPercentage: 55
          }
        ],
        gridPosition: {
          row: 3,
          col: 9,
          colSpan: 1
        }
      },
      {
        key: 'description',
        name: 'description',
        label: 'Diễn giải',
        type: 'text' as const,
        gridPosition: {
          row: 4,
          col: 0,
          colSpan: 9
        }
      },
      {
        key: 'status',
        name: 'status',
        label: 'Trạng thái',
        type: 'select' as const,
        options: [
          { value: 'export_invoice_2', label: 'Lập chứng từ' },
          { value: 'export_invoice_1', label: 'Chờ duyệt' },
          { value: 'export_invoice', label: 'Xuất hóa đơn' }
        ],
        minWidth: 120,
        gridPosition: {
          row: 4,
          col: 9,
          colSpan: 1
        }
      },
      {
        key: 'discountType',
        name: 'discountType',
        label: 'Loại chiết khấu',
        type: 'select' as const,
        options: [
          { value: 'Tự nhập', label: 'Tự nhập' },
          { value: 'Giảm % theo hoá đơn', label: 'Giảm % theo hoá đơn' },
          { value: 'Giảm tiền trên tổng hoá đơn', label: 'Giảm tiền trên tổng hoá đơn' },
          { value: 'Không sử dụng', label: 'Không sử dụng' }
        ],
        minWidth: '240px',
        gridPosition: {
          row: 5,
          col: 0,
          colSpan: 2
        }
      },
      {
        key: 'transferData',
        name: 'transferData',
        label: 'Chuyển dữ liệu',
        type: 'checkbox' as const,
        labelPosition: 'right' as const,
        minWidth: 120,
        gridPosition: {
          row: 5,
          col: 9,
          colSpan: 1
        }
      },
      {
        key: 'storeCode',
        name: 'storeCode',
        label: 'Mã cửa hàng',
        type: 'search' as const,
        minWidth: 120,
        gridPosition: {
          row: 6,
          col: 0,
          colSpan: 1
        }
      },
      {
        key: 'sourceCode',
        name: 'sourceCode',
        label: 'Mã nguồn đơn',
        type: 'search' as const,
        minWidth: 120,
        gridPosition: {
          row: 7,
          col: 0,
          colSpan: 1
        }
      },
      {
        key: 'invoiceCode',
        name: 'invoiceCode',
        label: 'Mã hóa đơn',
        type: 'text' as const,
        minWidth: '240px',
        gridPosition: {
          row: 8,
          col: 0,
          colSpan: 2
        }
      },
      {
        key: 'totalDiscount',
        name: 'totalDiscount',
        label: 'CK tổng bill',
        type: 'number' as const,
        defaultValue: 0.0,
        minWidth: 120,
        gridPosition: {
          row: 9,
          col: 0,
          colSpan: 1
        }
      },
      {
        key: 'shippingFee',
        name: 'shippingFee',
        label: 'Tiền ship',
        type: 'number' as const,
        defaultValue: 0.0,
        minWidth: 120,
        gridPosition: {
          row: 10,
          col: 0,
          colSpan: 1
        }
      }
    ]
  }
];

// Detailed tabs configuration
export const purchaseInvoiceDetailedTabs: TabConfig[] = [
  {
    key: 'chiTiet',
    title: 'Chi tiết',
    fields: [
      {
        key: 'items',
        name: 'items',
        label: 'Chi tiết hóa đơn',
        type: 'table' as const,
        tableConfig: {
          columns: purchaseInvoiceItemColumns2,
          inputColumns: purchaseInvoiceItemInputColumns
        }
      }
    ]
  },
  {
    key: 'thongTinKhachHang',
    title: 'Thông tin khách hàng',
    columns: 8,
    fields: [
      {
        key: 'customerAddress1',
        name: 'customerAddress1',
        label: 'Địa chỉ',
        type: 'search' as const,
        minWidth: 120,
        gridPosition: {
          row: 0,
          col: 0,
          rowSpan: 1,
          colSpan: 1
        }
      },
      {
        key: 'customerName',
        name: 'customerName',
        label: 'Tên khách hàng',
        type: 'text' as const,
        gridPosition: {
          row: 1,
          col: 0,
          rowSpan: 1,
          colSpan: 8
        }
      },
      {
        key: 'customerAddress2',
        name: 'customerAddress2',
        label: 'Địa chỉ',
        type: 'text' as const,
        gridPosition: {
          row: 2,
          col: 0,
          rowSpan: 1,
          colSpan: 8
        }
      },
      {
        key: 'taxCode',
        name: 'taxCode',
        label: 'Mã số thuế',
        type: 'text' as const,
        gridPosition: {
          row: 3,
          col: 0,
          rowSpan: 1,
          colSpan: 8
        }
      },
      {
        key: 'email',
        name: 'email',
        label: 'Email',
        type: 'text' as const,
        gridPosition: {
          row: 4,
          col: 0,
          rowSpan: 1,
          colSpan: 8
        }
      },
      {
        key: 'productGroup',
        name: 'productGroup',
        label: 'Nhóm sản phẩm',
        type: 'text' as const,
        gridPosition: {
          row: 5,
          col: 0,
          rowSpan: 1,
          colSpan: 8
        }
      },
      {
        key: 'taxDepartment',
        name: 'taxDepartment',
        label: 'Cục thuế',
        type: 'search' as const,
        minWidth: 120,
        gridPosition: {
          row: 6,
          col: 0,
          rowSpan: 1,
          colSpan: 1
        }
      }
    ]
  },
  {
    key: 'hddt',
    title: 'HĐĐT',
    columns: 8,
    fields: [
      {
        key: 'trangThaiHDDT',
        name: 'trangThaiHDDT',
        label: 'Trạng thái HĐĐT',
        type: 'select' as const,
        minWidth: 120,
        options: [
          { value: 'unused', label: 'Không sử dụng' },
          { value: 'pending', label: 'Chờ xuất' },
          { value: 'exported', label: 'Đã xuất' }
        ],
        gridPosition: {
          row: 0,
          col: 0,
          rowSpan: 1,
          colSpan: 1
        }
      },
      {
        key: 'soChungTu',
        name: 'soChungTu',
        label: 'Số chứng từ',
        type: 'text' as const,
        minWidth: 120,
        gridPosition: {
          row: 1,
          col: 0,
          rowSpan: 1,
          colSpan: 1
        }
      },
      {
        key: 'kyHieu',
        name: 'kyHieu',
        label: 'Ký hiệu',
        type: 'text' as const,
        minWidth: 120,
        gridPosition: {
          row: 2,
          col: 0,
          rowSpan: 1,
          colSpan: 1
        }
      },
      {
        key: 'mauHoaDon',
        name: 'mauHoaDon',
        label: 'Mẫu hóa đơn',
        type: 'text' as const,
        minWidth: 120,
        gridPosition: {
          row: 3,
          col: 0,
          rowSpan: 1,
          colSpan: 1
        }
      }
    ]
  },
  {
    key: 'khac',
    title: 'Khác',
    columns: 20,
    fields: [
      {
        key: 'employeeCode',
        name: 'employeeCode',
        label: 'Mã nhân viên',
        type: 'search' as const,
        minWidth: 120,
        gridPosition: {
          row: 0,
          col: 0,
          rowSpan: 1,
          colSpan: 3
        }
      },
      {
        key: 'transportMethod',
        name: 'transportMethod',
        label: 'Phương tiện v/c',
        type: 'search' as const,
        minWidth: 120,
        gridPosition: {
          row: 1,
          col: 0,
          rowSpan: 1,
          colSpan: 3
        }
      },
      {
        key: 'paymentMethod',
        name: 'paymentMethod',
        label: 'PT/th thanh toán',
        type: 'search' as const,
        minWidth: 120,
        gridPosition: {
          row: 2,
          col: 0,
          rowSpan: 1,
          colSpan: 3
        }
      },
      {
        key: 'deliveryMethod',
        name: 'deliveryMethod',
        label: 'PT/th giao hàng',
        type: 'search' as const,
        minWidth: 120,
        gridPosition: {
          row: 3,
          col: 0,
          rowSpan: 1,
          colSpan: 3
        }
      },
      {
        key: 'cancelReason',
        name: 'cancelReason',
        label: 'Lý do hủy',
        type: 'text' as const,
        placeholder: 'Nhập lý do hủy hóa',
        minWidth: 120,
        gridPosition: {
          row: 4,
          col: 0,
          rowSpan: 1,
          colSpan: 3
        }
      },
      {
        key: 'adjustmentReason',
        name: 'adjustmentReason',
        label: 'Lý do điều chỉnh',
        type: 'text' as const,
        placeholder: 'Nhập lý do thay thế hoặc điều chỉnh hóa đơn điện tử',
        gridPosition: {
          row: 5,
          col: 0,
          rowSpan: 1,
          colSpan: 20
        }
      },
      {
        key: 'transactionType',
        name: 'transactionType',
        label: 'Giao dịch',
        type: 'select' as const,
        minWidth: 180,
        options: [
          { value: 'BH', label: 'BH. Bán hàng ngoài' },
          { value: 'NB', label: 'NB. Bán nội bộ' },
          { value: 'XK', label: 'XK. Khác' },
          { value: 'PO', label: 'PO. Bán hàng POS' }
        ],
        gridPosition: {
          row: 6,
          col: 0,
          rowSpan: 1,
          colSpan: 4
        }
      },
      {
        key: 'statementNumber',
        name: 'statementNumber',
        label: 'Số bảng kê',
        type: 'text' as const,
        minWidth: 120,
        gridPosition: {
          row: 7,
          col: 0,
          rowSpan: 1,
          colSpan: 3
        }
      },
      {
        key: 'statementDate',
        name: 'statementDate',
        label: 'Ngày bảng kê',
        type: 'date' as const,
        minWidth: 120,
        gridPosition: {
          row: 7,
          col: 6,
          rowSpan: 1,
          colSpan: 3
        }
      },
      {
        key: 'notes',
        name: 'notes',
        label: 'Ghi chú',
        type: 'textarea' as const,
        placeholder: 'Nhập ghi chú',
        gridPosition: {
          row: 8,
          col: 0,
          rowSpan: 1,
          colSpan: 20
        }
      }
    ]
  },
  {
    key: 'tongCong',
    title: 'Tổng cộng',
    columns: 8,
    fields: [
      {
        key: 'totalCapital',
        name: 'totalCapital',
        label: 'Tổng tiền vốn',
        type: 'number' as const,
        minWidth: 120,
        defaultValue: 0,
        readOnly: true,
        gridPosition: {
          row: 0,
          col: 0,
          rowSpan: 1,
          colSpan: 1
        }
      },
      {
        key: 'totalDiscount2',
        name: 'totalDiscount2',
        label: 'Tổng giảm giá',
        type: 'number' as const,
        minWidth: 120,
        defaultValue: 0,
        readOnly: true,
        gridPosition: {
          row: 1,
          col: 0,
          rowSpan: 1,
          colSpan: 1
        }
      },
      {
        key: 'totalAmount',
        name: 'totalAmount',
        label: 'Tổng tiền hàng',
        type: 'number' as const,
        minWidth: 120,
        defaultValue: 0,
        readOnly: true,
        gridPosition: {
          row: 2,
          col: 0,
          rowSpan: 1,
          colSpan: 1
        }
      },
      {
        key: 'totalVAT',
        name: 'totalVAT',
        label: 'Tổng tiền KM',
        type: 'number' as const,
        minWidth: 120,
        defaultValue: 0,
        readOnly: true,
        gridPosition: {
          row: 3,
          col: 0,
          rowSpan: 1,
          colSpan: 1
        }
      },
      {
        key: 'totalTaxVAT',
        name: 'totalTaxVAT',
        label: 'Tổng thuế KM',
        type: 'number' as const,
        minWidth: 120,
        defaultValue: 0,
        readOnly: true,
        gridPosition: {
          row: 4,
          col: 0,
          rowSpan: 1,
          colSpan: 1
        }
      }
    ]
  },
  {
    key: 'files',
    title: 'Files',
    fields: []
  },
  {
    key: 'chietKhau',
    title: 'Chiết khấu',
    fields: [
      {
        key: 'discounts',
        name: 'discounts',
        label: 'Chi tiết chiết khấu',
        type: 'table' as const,
        tableConfig: {
          columns: discountColumns
        }
      }
    ]
  },
  {
    key: 'chiTietChietKhau',
    title: 'Chi tiết chiết khấu',
    fields: [
      {
        key: 'discountDetails',
        name: 'discountDetails',
        label: 'Chi tiết chiết khấu',
        type: 'table' as const,
        tableConfig: {
          columns: discountDetailColumns
        }
      }
    ]
  },
  {
    key: 'thongTinThanhToan',
    title: 'Thông tin thanh toán',
    fields: [
      {
        key: 'paymentInfo',
        name: 'paymentInfo',
        label: 'Thông tin thanh toán',
        type: 'table' as const,
        tableConfig: {
          columns: paymentInfoColumns
        }
      }
    ]
  }
];

export const purchaseInvoiceFooterFields: FieldConfig[] = [
  {
    key: 'totalQuantityDisplay',
    label: 'Tổng số lượng',
    type: 'text',
    disabled: true,
    valueRender: formValues => {
      return formValues.items.length;
    },
    gridPosition: { row: 0, col: 0, colSpan: 2 }
  },
  {
    key: 'totalAmountDisplay',
    label: 'Tổng tiền',
    type: 'number',
    disabled: true,
    gridPosition: { row: 0, col: 2, colSpan: 2 }
  },
  {
    key: 'totalTaxDisplay',
    label: 'Tổng thuế',
    type: 'number',
    disabled: true,
    gridPosition: { row: 1, col: 2, colSpan: 2 }
  },
  {
    key: 'totalPaymentDisplay',
    label: 'Tổng thanh toán',
    type: 'number',
    disabled: true,
    gridPosition: { row: 2, col: 2, colSpan: 2 }
  }
];
