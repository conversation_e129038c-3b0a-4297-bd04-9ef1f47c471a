import { Copy, Edit2, Plus, TextSearch, Trash2 } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button/index';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onAddClick?: () => void;
  onRefreshClick?: () => void;
  onPintClick?: () => void;
  onEditClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  onViewClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  className?: string;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onAddClick,
  onRefreshClick,
  onPintClick,
  onEditClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  onViewClick,
  onDeleteClick,
  onCopyClick,
  className
}) => {
  return (
    <AritoActionBar
      className={className}
      titleComponent={<h1 className='text-xl font-bold'>Cập nhật điều kiện thanh toán</h1>}
    >
      {onAddClick && <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />}
      {onEditClick && <AritoActionButton title='Sửa' icon={Edit2} onClick={onEditClick} variant='secondary' />}
      <AritoActionButton title='Xóa' icon={Trash2} onClick={onDeleteClick} variant='secondary' />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} variant='secondary' />
      <AritoActionButton title='Xem' icon={TextSearch} onClick={onViewClick} variant='secondary' />

      <AritoMenuButton
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: () => onRefreshClick,
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            group: 0,
            onClick: () => onPintClick
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={18} />,
            group: 1,
            onClick: () => onExportDataClick
          }
        ]}
      />
    </AritoActionBar>
  );
};
