'use client';

import { Menu, MenuItem } from '@mui/material';

// Profile dropdown menu component
export function ProfileMenu({
  anchorEl,
  isOpen,
  onClose,
  onLogout
}: {
  anchorEl: HTMLElement | null;
  isOpen: boolean;
  onClose: () => void;
  onLogout: () => void;
}) {
  return (
    <Menu
      anchorEl={anchorEl}
      open={isOpen}
      onClose={onClose}
      MenuListProps={{
        'aria-labelledby': 'profile-button'
      }}
    >
      <MenuItem onClick={onClose}>Thông tin tài khoản</MenuItem>
      <MenuItem onClick={onClose}>Cài đặt</MenuItem>
      <MenuItem
        onClick={() => {
          onClose();
          onLogout();
        }}
      >
        Đăng xuất
      </MenuItem>
    </Menu>
  );
}
