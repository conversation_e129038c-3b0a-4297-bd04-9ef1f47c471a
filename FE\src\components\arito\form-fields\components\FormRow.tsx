import { styled } from '@mui/material/styles';

// Form row with label and input side by side
export const FormRow = styled('div')({
  display: 'flex',
  alignItems: 'center',
  marginBottom: '4px',
  width: '100%',
  minHeight: '28px',
  flexWrap: 'nowrap'
});

// Special form row for textarea with top alignment
export const TextareaFormRow = styled(FormRow)({
  alignItems: 'flex-start',
  paddingTop: '4px'
});

export default FormRow;
