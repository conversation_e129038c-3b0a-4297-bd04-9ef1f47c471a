import { GridColDef } from '@mui/x-data-grid';
import { AritoCheckboxCellRenderer } from '@/components/arito/cell-renderers/arito-checkbox-cell-renderer';
import { AritoSearchCellRenderer } from '@/components/arito/cell-renderers/arito-search-cell-renderer';
// Invoice header table configuration
export const purchaseInvoiceColumns: GridColDef[] = [
  { field: 'status', headerName: 'Trạng thái', width: 100 },
  { field: 'statusHDDT', headerName: 'Trạng thái HĐĐT', width: 130 },
  { field: 'name', headerName: 'Số c/từ', width: 100 },
  { field: 'date', headerName: 'Ngày c/từ', width: 100 },
  { field: 'customerCode', headerName: 'Mã khách hàng', width: 120 },
  { field: 'customerName', headerName: 'Tên khách hàng', width: 200 },
  { field: 'description', headerName: '<PERSON><PERSON><PERSON> gi<PERSON>', width: 200 },
  { field: 'debitAccount', headerName: 'TK nợ', width: 90 },
  { field: 'totalAmount', headerName: 'Tổng tiền', type: 'number', width: 120 },
  { field: 'foreignCurrency', headerName: 'Ngoại tệ', width: 100 },
  { field: 'storeCode', headerName: 'Mã cửa hàng', width: 120 },
  { field: 'sourceCode', headerName: 'Mã nguồn đơn', width: 120 },
  { field: 'invoiceId', headerName: 'Mã hoá đơn', width: 200 }
];

export const purchaseInvoiceItemColumns1: GridColDef[] = [
  { field: 'item_code', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'item_name', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'stock_uom', headerName: 'Đvt', width: 80 },
  { field: 'warehouse', headerName: 'Mã kho', width: 100 },
  { field: 'stock_qty', headerName: 'Tồn', type: 'number', width: 100 },
  { field: 'productType', headerName: 'Loại hàng', width: 120 },
  { field: 'qty', headerName: 'Số lượng', type: 'number', width: 100 },
  { field: 'base_rate', headerName: 'Giá chuẩn %s', type: 'number', width: 130 },
  { field: 'rate', headerName: 'Giá bán %s', type: 'number', width: 120 },
  { field: 'incoming_rate', headerName: 'Doanh số %s', type: 'number', width: 130 },
  { field: 'discount_percentage', headerName: 'Tl ck(%)', type: 'number', width: 100 },
  { field: 'discount_ammount', headerName: 'Giảm giá %s', type: 'number', width: 130 },
  { field: 'promotion', headerName: 'Ch.khấu %s', type: 'number', width: 130 },
  { field: 'namedPromotion', headerName: 'Đích danh', width: 100 },
  { field: 'stock_uom_rate', headerName: 'Giá tồn %s', type: 'number', width: 120 },
  { field: 'amount', headerName: 'Tiền %s', type: 'number', width: 120 },
  { field: 'taxCode', headerName: 'Mã thuế', width: 100 },
  { field: 'taxAccount', headerName: 'Tk thuế có', width: 120 },
  { field: 'taxRate', headerName: 'Thuế suất(%)', type: 'number', width: 120 },
  { field: 'taxAmount', headerName: 'Thuế %s', type: 'number', width: 120 },
  { field: 'income_account', headerName: 'Tk doanh thu', width: 120 },
  { field: 'expense_account', headerName: 'Tk giá vốn', width: 120 },
  { field: 'warehouseAccount', headerName: 'Tk kho', width: 100 },
  { field: 'discountAccount', headerName: 'Tk giảm giá', width: 120 },
  { field: 'promotionAccount', headerName: 'Tk chiết khấu', width: 120 },
  { field: 'marketingAccount', headerName: 'Tk khuyến mãi', width: 120 },
  { field: 'description', headerName: 'Ghi chú tên vật tư', width: 150 },
  { field: 'department', headerName: 'Bộ phận', width: 100 },
  { field: 'paymentMethod', headerName: 'Đợt thanh toán', width: 130 },
  { field: 'fee', headerName: 'Phí', width: 80 },
  { field: 'product', headerName: 'Sản phẩm', width: 100 },
  { field: 'inventoryPrice', headerName: 'Giá tồn', type: 'number', width: 120 },
  { field: 'total', headerName: 'Tiền', type: 'number', width: 120 },
  { field: 'standardPrice', headerName: 'Giá chuẩn', type: 'number', width: 130 },
  { field: 'sellingPrice', headerName: 'Giá bán', type: 'number', width: 120 },
  { field: 'revenue', headerName: 'Doanh số', type: 'number', width: 130 },
  { field: 'discount', headerName: 'Giảm giá', type: 'number', width: 130 },
  { field: 'promotion', headerName: 'Chiết khấu', type: 'number', width: 130 },
  { field: 'taxAmount', headerName: 'Thuế', type: 'number', width: 120 },
  { field: 'quantityExported', headerName: 'Sl đã xuất', type: 'number', width: 100 },
  { field: 'exportNumber', headerName: 'Số PX', width: 100 },
  { field: 'exportLine', headerName: 'Dòng px', width: 100 },
  { field: 'orderNumber', headerName: 'Số đơn hàng', width: 120 },
  { field: 'orderLine', headerName: 'Dòng ĐH', width: 100 },
  { field: 'itemCode', headerName: 'Mã hàng', width: 100 },
  { field: 'posPrice', headerName: 'Giá từ POS', type: 'number', width: 120 },
  { field: 'posDiscount', headerName: 'CK POS', type: 'number', width: 100 },
  { field: 'posDiscountAmount', headerName: 'Giảm giá POS', type: 'number', width: 130 }
];

export const purchaseInvoiceItemColumns2: GridColDef[] = [
  { field: 'item_code', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'item_name', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'stock_uom', headerName: 'Đvt', width: 80 },
  { field: 'warehouseCode', headerName: 'Mã kho', width: 100 },
  { field: 'stock_qty', headerName: 'Tồn', type: 'number', width: 100 },
  { field: 'productType', headerName: 'Loại hàng', width: 120 },
  { field: 'quantity', headerName: 'Số lượng', type: 'number', width: 100 },
  { field: 'base_rate', headerName: 'Giá chuẩn %s', type: 'number', width: 130 },
  { field: 'rate', headerName: 'Giá bán %s', type: 'number', width: 120 },
  { field: 'incoming_rate', headerName: 'Doanh số %s', type: 'number', width: 130 },
  { field: 'discount_percentage', headerName: 'Tl ck(%)', type: 'number', width: 100 },
  { field: 'discount_ammount', headerName: 'Giảm giá %s', type: 'number', width: 130 },
  { field: 'promotion', headerName: 'Ch.khấu %s', type: 'number', width: 130 },
  { field: 'namedPromotion', headerName: 'Đích danh', width: 100 },
  { field: 'stock_uom_rate', headerName: 'Giá tồn %s', type: 'number', width: 120 },
  { field: 'amount', headerName: 'Tiền %s', type: 'number', width: 120 },
  { field: 'taxCode', headerName: 'Mã thuế', width: 100 },
  { field: 'taxAccount', headerName: 'Tk thuế có', width: 120 },
  { field: 'taxRate', headerName: 'Thuế suất(%)', type: 'number', width: 120 },
  { field: 'taxAmount', headerName: 'Thuế %s', type: 'number', width: 120 },
  { field: 'income_account', headerName: 'Tk doanh thu', width: 120 },
  { field: 'expense_account', headerName: 'Tk giá vốn', width: 120 },
  { field: 'warehouseAccount', headerName: 'Tk kho', width: 100 },
  { field: 'discountAccount', headerName: 'Tk giảm giá', width: 120 },
  { field: 'promotionAccount', headerName: 'Tk chiết khấu', width: 120 },
  { field: 'marketingAccount', headerName: 'Tk khuyến mãi', width: 120 },
  { field: 'description', headerName: 'Ghi chú tên vật tư', width: 150 },
  { field: 'department', headerName: 'Bộ phận', width: 100 },
  { field: 'paymentMethod', headerName: 'Đợt thanh toán', width: 130 },
  { field: 'fee', headerName: 'Phí', width: 80 },
  { field: 'product', headerName: 'Sản phẩm', width: 100 },
  { field: 'inventoryPrice', headerName: 'Giá tồn', type: 'number', width: 120 },
  { field: 'total', headerName: 'Tiền', type: 'number', width: 120 },
  { field: 'standardPrice', headerName: 'Giá chuẩn', type: 'number', width: 130 },
  { field: 'sellingPrice', headerName: 'Giá bán', type: 'number', width: 120 },
  { field: 'revenue', headerName: 'Doanh số', type: 'number', width: 130 },
  { field: 'discount', headerName: 'Giảm giá', type: 'number', width: 130 },
  { field: 'promotion', headerName: 'Chiết khấu', type: 'number', width: 130 },
  { field: 'taxAmount', headerName: 'Thuế', type: 'number', width: 120 },
  { field: 'quantityExported', headerName: 'Sl đã xuất', type: 'number', width: 100 },
  { field: 'exportNumber', headerName: 'Số PX', width: 100 },
  { field: 'exportLine', headerName: 'Dòng px', width: 100 },
  { field: 'orderNumber', headerName: 'Số đơn hàng', width: 120 },
  { field: 'orderLine', headerName: 'Dòng ĐH', width: 100 },
  { field: 'itemCode', headerName: 'Mã hàng', width: 100 },
  { field: 'posPrice', headerName: 'Giá từ POS', type: 'number', width: 120 },
  { field: 'posDiscount', headerName: 'CK POS', type: 'number', width: 100 },
  { field: 'posDiscountAmount', headerName: 'Giảm giá POS', type: 'number', width: 130 }
];

// Invoice detail/line items table configuration
export const purchaseInvoiceItemInputColumns: GridColDef[] = [
  {
    field: 'item_code',
    headerName: 'Mã sản phẩm',
    width: 120,
    renderCell: params => {
      return (
        <AritoSearchCellRenderer
          params={params}
          docType='Item'
          columns={[
            { field: 'item_code', headerName: 'Mã vật tư', width: 120 },
            { field: 'item_name', headerName: 'Tên vật tư', width: 200 },
            { field: 'stock_uom', headerName: 'Đvt', width: 80 },
            { field: 'warehouseCode', headerName: 'Nhom 1', width: 100 },
            { field: 'theodoilop', headerName: 'Theo dõi lô', width: 100, renderCell: AritoCheckboxCellRenderer },
            { field: 'productType', headerName: 'Hình ảnh', width: 100 }
          ]}
          onSelect={(selectedRow, params) => {
            // Get current row model
            const newRow = { ...params.row };

            // Update values
            newRow.item_code = selectedRow.item_code;
            newRow.item_name = selectedRow.item_name;
            newRow.stock_uom = selectedRow.stock_uom;
            // Update the entire row
            params.api.updateRows([{ id: params.id, ...newRow }]);
          }}
        />
      );
    }
  },
  { field: 'item_name', headerName: 'Tên sản phẩm', width: 200, editable: false },
  { field: 'stock_uom', headerName: 'Đvt', width: 80, editable: false },
  { field: 'warehouseCode', headerName: 'Mã kho', width: 100 },
  { field: 'inventory', headerName: 'Tồn', type: 'number', width: 100 },
  {
    field: 'productType',
    headerName: 'Loại hàng',
    width: 120,
    renderCell: params => {
      const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const newValue = event.target.value;
        // Update the row data using updateRows
        params.api.updateRows([{ ...params.row, productType: newValue }]);
      };

      return (
        <select className='border-none bg-inherit' value={params.value} onChange={handleChange}>
          <option value='0'>Hàng bán</option>
          <option value='1'>Hàng KM</option>
        </select>
      );
    }
  },
  { field: 'quantity', headerName: 'Số lượng', type: 'number', width: 100 },
  { field: 'standardPrice', headerName: 'Giá chuẩn VND', type: 'number', width: 130 },
  { field: 'sellingPrice', headerName: 'Giá bán VND', type: 'number', width: 120 },
  { field: 'revenue', headerName: 'Doanh số VND', type: 'number', width: 130 },
  { field: 'discountRate', headerName: 'Tl ck(%)', type: 'number', width: 100 },
  { field: 'discount', headerName: 'Giảm giá VND', type: 'number', width: 130 },
  { field: 'promotion', headerName: 'Ch.khấu VND', type: 'number', width: 130 },
  { field: 'namedPromotion', headerName: 'Đích danh', width: 100 },
  { field: 'inventoryPrice', headerName: 'Giá tồn VND', type: 'number', width: 120 },
  { field: 'total', headerName: 'Tiền VND', type: 'number', width: 120 },
  { field: 'taxCode', headerName: 'Mã thuế', width: 100 },
  { field: 'taxAccount', headerName: 'Tk thuế có', width: 120 },
  { field: 'taxRate', headerName: 'Thuế suất(%)', type: 'number', width: 120 },
  { field: 'taxAmount', headerName: 'Thuế VND', type: 'number', width: 120 },
  { field: 'revenueAccount', headerName: 'Tk doanh thu', width: 120 },
  { field: 'costAccount', headerName: 'Tk giá vốn', width: 120 },
  { field: 'warehouseAccount', headerName: 'Tk kho', width: 100 },
  { field: 'discountAccount', headerName: 'Tk giảm giá', width: 120 },
  { field: 'promotionAccount', headerName: 'Tk chiết khấu', width: 120 },
  { field: 'marketingAccount', headerName: 'Tk khuyến mãi', width: 120 },
  { field: 'productNote', headerName: 'Ghi chú tên vật tư', width: 150 },
  { field: 'department', headerName: 'Bộ phận', width: 100 },
  { field: 'paymentMethod', headerName: 'Đợt thanh toán', width: 130 },
  { field: 'fee', headerName: 'Phí', width: 80 },
  { field: 'product', headerName: 'Sản phẩm', width: 100 },
  { field: 'orderNumber', headerName: 'Số đơn hàng', width: 120 },
  { field: 'orderLine', headerName: 'Dòng ĐH', width: 100 },
  { field: 'itemCode', headerName: 'Mã hàng', width: 100 },
  { field: 'posPrice', headerName: 'Giá từ POS', type: 'number', width: 120 },
  { field: 'posDiscount', headerName: 'CK POS', type: 'number', width: 100 },
  { field: 'posDiscountAmount', headerName: 'Giảm giá POS', type: 'number', width: 130 }
];

// Discount table configuration
export const discountColumns: GridColDef[] = [
  { field: 'discountCode', headerName: 'Mã chiết khấu', width: 120 },
  { field: 'discountName', headerName: 'Tên chiết khấu', width: 200 },
  { field: 'discountType', headerName: 'Loại chiết khấu', width: 120 },
  { field: 'discountId', headerName: 'ID ck', width: 100 },
  { field: 'discountLine', headerName: 'Line ck', width: 100 },
  { field: 'discountRate', headerName: 'Tỷ lệ ck', type: 'number', width: 100 },
  { field: 'discountRateAmount', headerName: 'Tiền ck tl', type: 'number', width: 120 },
  { field: 'discountAmount', headerName: 'Tiền ck', type: 'number', width: 120 },
  { field: 'giftItem', headerName: 'Tặng hàng', width: 100 },
  { field: 'giftItemCode', headerName: 'Mã hàng tặng', width: 120 },
  { field: 'giftItemName', headerName: 'Tên hàng tặng', width: 200 },
  { field: 'giftItemUnit', headerName: 'Đvt', width: 80 },
  { field: 'warehouseCode', headerName: 'Mã kho', width: 100 },
  { field: 'giftQuantity', headerName: 'Sl tặng', type: 'number', width: 100 },
  { field: 'basedOnAmount', headerName: 'Tính trên tiền hàng', width: 150 }
];

// Discount detail table configuration
export const discountDetailColumns: GridColDef[] = [
  { field: 'discountCode', headerName: 'Mã chiết khấu', width: 120 },
  { field: 'discountName', headerName: 'Tên chiết khấu', width: 200 },
  { field: 'discountType', headerName: 'Loại chiết khấu', width: 120 },
  { field: 'discountId', headerName: 'ID ck', width: 100 },
  { field: 'discountLine', headerName: 'Line ck', width: 100 },
  { field: 'discountRate', headerName: 'Tỷ lệ ck', type: 'number', width: 100 },
  { field: 'discountRateAmount', headerName: 'Tiền ck tl', type: 'number', width: 120 },
  { field: 'discountAmount', headerName: 'Tiền ck', type: 'number', width: 120 },
  { field: 'giftItem', headerName: 'Tặng hàng', width: 100 },
  { field: 'productCode', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'productName', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'unit', headerName: 'Đvt', width: 80 },
  { field: 'quantity', headerName: 'Số lượng', type: 'number', width: 100 },
  { field: 'sellingPrice', headerName: 'Giá bán', type: 'number', width: 120 },
  { field: 'basedOnAmount', headerName: 'Tính trên tiền hàng', width: 150 },
  { field: 'detailLine', headerName: 'Line ct', width: 100 }
];

// Payment information table configuration
export const paymentInfoColumns: GridColDef[] = [
  { field: 'paymentMethodCode', headerName: 'Mã hình thức', width: 120 },
  { field: 'paymentMethodName', headerName: 'Tên hình thức', width: 200 },
  { field: 'paymentAccount', headerName: 'Tk thanh toán', width: 120 },
  { field: 'documentNumber', headerName: 'Chứng từ', width: 120 },
  { field: 'documentDate', headerName: 'Ngày chứng từ', width: 120 },
  { field: 'documentBook', headerName: 'Quyển chứng từ', width: 120 },
  { field: 'paymentAmount', headerName: 'Số tiền thanh toán', type: 'number', width: 150 }
];
