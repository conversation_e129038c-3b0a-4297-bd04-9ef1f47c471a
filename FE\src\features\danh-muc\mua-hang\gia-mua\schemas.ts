import { z } from 'zod';

export const formSchema = z.object({
  uuid: z.string().optional(),
  ngay_hieu_luc: z.string(),
  so_luong_tu: z.union([z.number(), z.string()]).optional(),
  gia_mua: z.union([z.number(), z.string()]).optional(),
  trang_thai: z.number().default(1),

  // Các trường dữ liệu đối tượ<PERSON> (cho edit mode)
  ma_vat_tu_data: z.any().optional(),
  don_vi_tinh_data: z.any().optional(),
  nha_cung_cap_data: z.any().optional(),
  ngoai_te_data: z.any().optional()
});

export type FormValues = z.infer<typeof formSchema>;

export const initialValues: FormValues = {
  uuid: undefined,
  ngay_hieu_luc: '',
  so_luong_tu: undefined,
  gia_mua: undefined,
  trang_thai: 1,

  // <PERSON><PERSON>c trường dữ liệu đối tượng
  ma_vat_tu_data: null,
  don_vi_tinh_data: null,
  nha_cung_cap_data: null,
  ngoai_te_data: null
};
