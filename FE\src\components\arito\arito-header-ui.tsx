'use client';

import Link from 'next/link';
import React from 'react';
import { useAuth } from '@/contexts/auth-context';

// Logo component with responsive sizing
export function Logo({ isMobile = false }: { isMobile?: boolean }) {
  return (
    <Link href='/trang-chu' className={`flex cursor-pointer flex-row items-center ${isMobile ? '-ml-1' : 'sm:-ml-2'}`}>
      <svg
        width='100'
        height='50'
        viewBox={isMobile ? '10 0 80 50' : '0 0 100 50'}
        xmlns='http://www.w3.org/2000/svg'
        fill='none'
      >
        <text x='10' y='35' fontFamily='Arial, sans-serif' fontSize='25' fontWeight='bold' fill='white'>
          T
        </text>
        <text x='25' y='35' fontFamily='Arial, sans-serif' fontSize='25' fontWeight='bold' fill='white'>
          T
        </text>
        <text x='40' y='35' fontFamily='Arial, sans-serif' fontSize='25' fontWeight='bold' fill='white'>
          M
        </text>
        <text x='62' y='35' fontFamily='Arial, sans-serif' fontSize='25' fontWeight='bold' fill='white'>
          I
        </text>
        <text x='75' y='35' fontFamily='Arial, sans-serif' fontSize='12' fontWeight='bold' fill='white'>
          ERP
        </text>
      </svg>
    </Link>
  );
}

// Profile button with consistent styling
export function ProfileButton({
  isMobile = false,
  onClick
}: {
  isMobile?: boolean;
  onClick: (e: React.MouseEvent<HTMLElement>) => void;
}) {
  return (
    <div className='flex h-7 w-7 cursor-pointer items-center justify-center rounded-full bg-white' onClick={onClick}>
      <span className='text-sm font-semibold text-teal-600'>A</span>
    </div>
  );
}

// Company selector with user and entity info
export function CompanySelector({ isMobile = false }: { isMobile?: boolean }) {
  const { profile, entity, loading } = useAuth();

  return (
    <div className='flex cursor-pointer items-center gap-1 rounded bg-teal-700 px-2 py-1'>
      <span className='text-xs text-white'>{profile?.nickname}</span>
      <span className='text-sm text-white'>{entity?.name}</span>
    </div>
  );
}

// Header icons (search and notifications)
export function HeaderIcons({ isMobile = false }: { isMobile?: boolean }) {
  return (
    <>
      <div className='cursor-pointer'>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-white`}
          fill='none'
          viewBox='0 0 24 24'
          stroke='currentColor'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
          />
        </svg>
      </div>

      <div className='cursor-pointer'>
        <svg
          xmlns='http://www.w3.org/2000/svg'
          className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-white`}
          fill='none'
          viewBox='0 0 24 24'
          stroke='currentColor'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={2}
            d='M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9'
          />
        </svg>
      </div>
    </>
  );
}
