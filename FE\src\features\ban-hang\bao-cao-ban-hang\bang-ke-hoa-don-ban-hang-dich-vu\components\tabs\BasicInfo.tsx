import React from 'react';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { RadioButton } from '@/components/custom/arito/form/radio-button';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const BasicInfo: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-2'>
        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Ngày xuất từ/đến:</Label>
            <AritoFormDateRangeDropdown fromDateName='fromDate' toDateName='toDate' />
          </div>
        </div>

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='mr-4 whitespace-nowrap'>Số c từ đến(từ/đến):</Label>
            <div className='grid flex-1 grid-cols-1 gap-4 md:grid-cols-2'>
              <FormField name='fromDocumentNumber' label='' type='text' />
              <FormField name='toDocumentNumber' label='' type='text' />
            </div>
          </div>
        </div>

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='mr-4 whitespace-nowrap'>Loại dữ liệu:</Label>
            <RadioButton
              name='dataType'
              options={[
                { value: 'goods', label: 'Hàng hoá' },
                { value: 'services', label: 'Dịch vụ' },
                { value: 'both', label: 'Hàng hoá và dịch vụ' }
              ]}
              defaultValue='both'
              orientation='horizontal'
              onChange={() => {}}
            />
            <div className='ml-6'>
              <FormField name='includeReturns' type='checkbox' label='Bao gồm trả lại' />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
