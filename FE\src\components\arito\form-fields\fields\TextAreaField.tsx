import { Controller } from 'react-hook-form';
import React from 'react';
import { InputFieldWrapper, ErrorMessage } from '../components/InputFieldWrapper';
import { StyledTextArea } from '../inputs/StyledTextArea';
import { TextareaFormRow } from '../components/FormRow';
import { StyledInput } from '../inputs/StyledInput';
import { CommonFieldProps } from '../types';

export const TextAreaField: React.FC<CommonFieldProps> = ({ field, maxLabelWidth, isViewMode }) => {
  const { key, label, placeholder, required, disabled = isViewMode, minWidth, valueRender } = field;

  // Label style
  const labelStyle = {
    width: `${maxLabelWidth}px`,
    minWidth: `${maxLabelWidth}px`,
    fontSize: '12px',
    fontWeight: 400,
    marginRight: '8px',
    whiteSpace: 'nowrap' as const,
    overflow: 'visible' as const,
    display: 'inline-block'
  };

  // Input wrapper style
  const inputWrapperStyle = {
    flex: '1 1 auto',
    position: 'relative' as const,
    minWidth: minWidth ? (typeof minWidth === 'number' ? `${minWidth}px` : minWidth) : '0',
    display: 'flex',
    flexDirection: 'column' as const
  };

  // Container style
  const containerStyle = {
    display: 'flex',
    alignItems: 'flex-start',
    width: '100%',
    position: 'relative' as const,
    overflow: 'visible'
  };

  // Colon element for view mode
  const colonElement = disabled && (
    <span
      style={{
        marginRight: '10px',
        fontSize: '12px',
        fontWeight: 400,
        alignSelf: 'flex-start',
        paddingTop: '4px'
      }}
    >
      :
    </span>
  );

  // Label content
  const labelContent = (
    <span style={labelStyle}>
      {label}
      {!disabled && required && <span style={{ color: 'red' }}>*</span>}
    </span>
  );

  return (
    <TextareaFormRow>
      {labelContent}
      <InputFieldWrapper style={inputWrapperStyle}>
        <div style={containerStyle}>
          {colonElement}
          <Controller
            name={key}
            render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
              <>
                {valueRender ? (
                  <StyledInput type='text' value={valueRender(value) || ''} disabled={true} />
                ) : (
                  <StyledTextArea
                    value={value || ''}
                    onChange={onChange}
                    placeholder={!isViewMode ? placeholder : undefined}
                    disabled={disabled}
                    ref={ref}
                    rows={3}
                  />
                )}
                {error && <ErrorMessage>{error.message}</ErrorMessage>}
              </>
            )}
          />
        </div>
      </InputFieldWrapper>
    </TextareaFormRow>
  );
};

export default TextAreaField;
