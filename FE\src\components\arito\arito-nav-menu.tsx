'use client';

import {
  <PERSON>u,
  <PERSON>u<PERSON><PERSON>,
  <PERSON><PERSON>,
  ListItemIcon,
  ListItemText,
  Divider,
  Popper,
  Paper,
  ClickAwayListener,
  Grow,
  MenuList
} from '@mui/material';
import React, { ReactNode, useState, useRef, useEffect } from 'react';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import { useRouter } from 'next/navigation';

export interface MenuItem {
  title: string;
  icon?: ReactNode;
  href?: string;
  items?: MenuItem[];
  group?: number;
}

export interface NavMenu {
  title: string;
  items: MenuItem[];
}

const RecursiveMenuItem = ({
  item,
  onItemClick,
  depth = 0
}: {
  item: MenuItem;
  onItemClick: () => void;
  depth?: number;
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [open, setOpen] = useState(false);
  const menuItemRef = useRef<HTMLLIElement>(null);
  const router = useRouter();
  const navigatingRef = useRef(false);

  // Handle navigation without page reload
  const navigate = (href: string) => {
    // Set flag to prevent menu closing from canceling navigation
    navigatingRef.current = true;

    // Use Next.js router for client-side navigation
    router.push(href);

    // Short delay before closing menu to ensure navigation starts first
    setTimeout(() => {
      onItemClick();
      navigatingRef.current = false;
    }, 50);
  };

  // Close submenu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (menuItemRef.current && !menuItemRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [open]);

  // Create a universal handler for all events
  const handleAllEvents = (event: React.SyntheticEvent) => {
    event.preventDefault();
    event.stopPropagation();

    if (item.items) {
      setAnchorEl(event.currentTarget as HTMLElement);
      setOpen(true);
    } else if (item.href) {
      navigate(item.href);
    } else {
      onItemClick();
    }
  };

  const menuItem = (
    <MenuItem
      ref={menuItemRef}
      onClick={handleAllEvents}
      onMouseUp={item.href ? handleAllEvents : undefined}
      onTouchEnd={item.href ? handleAllEvents : undefined}
      onTouchStart={item.href ? e => e.stopPropagation() : undefined}
      onMouseDown={item.href ? e => e.stopPropagation() : undefined}
      style={{
        margin: '1px 3px',
        padding: '0px 8px',
        backgroundColor: open ? 'rgba(25, 118, 210, 0.15)' : 'transparent',
        height: 28,
        cursor: 'default',
        WebkitTapHighlightColor: 'transparent', // Remove mobile tap highlight
        boxSizing: 'border-box', // Add box-sizing to the inline style
        outline: open ? '1px solid rgb(171, 211, 239)' : 'none' // Add outline when open to match hover
      }}
      sx={{
        boxSizing: 'border-box', // Keep this in sx as well
        '&:hover': {
          backgroundColor: 'rgba(25, 118, 210, 0.15) !important',
          outline: '1px solid rgb(171, 211, 239) !important', // Use outline instead of border
          cursor: 'default',
          '& .MuiListItemText-primary': {
            color: '#007acc !important'
          },
          '& .MuiListItemIcon-root': {
            color: '#007acc !important'
          },
          '& .MuiSvgIcon-root': {
            color: '#007acc !important'
          }
        }
      }}
    >
      <ListItemIcon
        style={{
          minWidth: 24,
          color: open ? '#007acc' : 'black',
          marginLeft: -3,
          height: 16,
          display: 'flex',
          alignItems: 'center'
        }}
      >
        {item.icon}
      </ListItemIcon>
      <ListItemText
        slotProps={{
          primary: {
            style: {
              fontSize: '0.8rem',
              margin: '0px',
              color: open ? '#007acc' : 'black',
              fontWeight: 400
            }
          }
        }}
      >
        {item.title}
      </ListItemText>
      {item.items && (
        <ArrowRightIcon
          style={{
            fontSize: 16,
            color: open ? '#007acc' : 'black',
            marginRight: -6
          }}
        />
      )}
    </MenuItem>
  );

  return (
    <>
      {menuItem}

      {item.items && (
        <Popper open={open} anchorEl={anchorEl} placement='right-start' transition style={{ zIndex: 1301 }}>
          {({ TransitionProps }) => (
            <Grow {...TransitionProps} style={{ transformOrigin: 'left top' }}>
              <Paper
                style={{
                  borderRadius: 0,
                  minWidth: 200,
                  padding: 0,
                  marginLeft: 1,
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                  border: '1px solid #e0e0e0'
                }}
              >
                <ClickAwayListener onClickAway={handleClose}>
                  <MenuList style={{ padding: 0 }}>
                    {item.items?.map((subItem, index) => (
                      <RecursiveMenuItem key={index} item={subItem} onItemClick={onItemClick} depth={depth + 1} />
                    ))}
                  </MenuList>
                </ClickAwayListener>
              </Paper>
            </Grow>
          )}
        </Popper>
      )}
    </>
  );

  function handleClose() {
    // Don't close if we're in the middle of navigating
    if (navigatingRef.current) return;
    setOpen(false);
    setAnchorEl(null);
  }
};

export function AritoNavMenu({ title, items }: NavMenu) {
  const [mainMenuEl, setMainMenuEl] = useState<HTMLElement | null>(null);
  const [open, setOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const navigatingRef = useRef(false);

  // Handling all events for the main menu button
  const handleMainMenuToggle = (event: React.SyntheticEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setMainMenuEl(event.currentTarget as HTMLElement);
    setOpen(true);
  };

  const handleMainMenuClose = (): void => {
    // Don't close if we're in the middle of navigating
    if (navigatingRef.current) return;
    setMainMenuEl(null);
    setOpen(false);
  };

  // Group items by their group property
  const groupedItems: { [key: number]: MenuItem[] } = {};
  items.forEach(item => {
    const group = item.group || 0;
    if (!groupedItems[group]) {
      groupedItems[group] = [];
    }
    groupedItems[group].push(item);
  });

  // Get sorted group keys
  const groupKeys = Object.keys(groupedItems).map(Number).sort();

  return (
    <>
      <Button
        ref={buttonRef}
        style={{
          gap: 0,
          borderRadius: 0,
          color: 'white',
          textTransform: 'none',
          fontSize: '12px',
          padding: '4px 10px',
          backgroundColor: open ? 'rgba(0, 0, 0, 0.2)' : 'transparent',
          WebkitTapHighlightColor: 'transparent' // Remove mobile tap highlight
        }}
        sx={{
          border: '1px solid transparent',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.2) !important',
            border: '1px solid rgba(171, 211, 239, 0.5) !important'
          },
          '& .MuiButton-endIcon': {
            marginLeft: '2px'
          },
          '&:active': {
            backgroundColor: 'rgba(0, 0, 0, 0.3) !important'
          }
        }}
        onClick={handleMainMenuToggle}
        onTouchEnd={handleMainMenuToggle}
        onMouseUp={handleMainMenuToggle}
        endIcon={<ArrowDropDownIcon style={{ fontSize: 16 }} />}
      >
        {title}
      </Button>

      <Popper open={open} anchorEl={mainMenuEl} placement='bottom-start' transition style={{ zIndex: 1300 }}>
        {({ TransitionProps }) => (
          <Grow {...TransitionProps} style={{ transformOrigin: 'top left' }}>
            <Paper
              style={{
                borderRadius: 0,
                minWidth: 200,
                padding: '3px 0px',
                marginTop: 0,
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                border: '1px solid #e0e0e0'
              }}
            >
              <ClickAwayListener onClickAway={handleMainMenuClose}>
                <MenuList style={{ padding: 0 }}>
                  {groupKeys.map((group, groupIndex) => (
                    <React.Fragment key={group}>
                      {groupIndex > 0 && <Divider style={{ margin: '3px 0px' }} />}
                      {groupedItems[group].map((item, itemIndex) => (
                        <RecursiveMenuItem
                          key={`${group}-${itemIndex}`}
                          item={item}
                          onItemClick={handleMainMenuClose}
                          depth={0}
                        />
                      ))}
                    </React.Fragment>
                  ))}
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </>
  );
}
