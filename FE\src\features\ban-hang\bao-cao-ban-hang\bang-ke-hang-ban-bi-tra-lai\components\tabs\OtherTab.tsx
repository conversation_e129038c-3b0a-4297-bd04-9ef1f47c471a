import { useFormContext } from 'react-hook-form';
import React, { useState } from 'react';
import { transactionColumns, batchColumns, locationColumns, accountColumns } from '../../cols-definition';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

const OtherTab: React.FC = () => {
  const { setValue } = useFormContext();
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);
  const [saveAnalysisTemplateDialogOpen, setSaveAnalysisTemplateDialogOpen] = useState(false);

  const handleSaveFilterTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving filter template:', data);
    setSaveFilterTemplateDialogOpen(false);
  };

  const handleSaveAnalysisTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving analysis template:', data);
    setSaveAnalysisTemplateDialogOpen(false);
  };

  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-2'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã giao dịch:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}/`}
            searchColumns={transactionColumns}
            dialogTitle='Danh mục giao dịch'
            columnDisplay='transactionCode'
            displayRelatedField='transactionName'
            onValueChange={value => {
              setValue('transactionCode', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('transactionCode', row.transactionCode);
                setValue('transactionName', row.transactionName);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản vật tư:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='accountCode'
            displayRelatedField='accountName'
            onValueChange={value => {
              setValue('itemAccount', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('itemAccount', row.accountCode);
                setValue('itemAccountName', row.accountName);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản doanh thu:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='accountCode'
            displayRelatedField='accountName'
            onValueChange={value => {
              setValue('revenueAccount', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('revenueAccount', row.accountCode);
                setValue('revenueAccountName', row.accountName);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản giá vốn:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='accountCode'
            displayRelatedField='accountName'
            onValueChange={value => {
              setValue('costAccount', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('costAccount', row.accountCode);
                setValue('costAccountName', row.accountName);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã lô:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.LO}/`}
            searchColumns={batchColumns}
            dialogTitle='Danh mục lô hàng'
            columnDisplay='batchCode'
            displayRelatedField='batchName'
            onValueChange={value => {
              setValue('batchCode', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('batchCode', row.batchCode);
                setValue('batchName', row.batchName);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vị trí:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.VI_TRI}/`}
            searchColumns={locationColumns}
            dialogTitle='Danh mục vị trí'
            columnDisplay='locationCode'
            displayRelatedField='locationName'
            onValueChange={value => {
              setValue('locationCode', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('locationCode', row.locationCode);
                setValue('locationName', row.locationName);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Diễn giải:</Label>
          <FormField name='description' label='' type='text' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu lọc báo cáo:</Label>
          <div className='flex flex-1 items-center gap-1'>
            <div className='flex-1'>
              <FormField
                name='reportFilterTemplate'
                label=''
                type='select'
                options={[{ value: 'user_filter', label: 'Người dùng tự lọc' }]}
              />
            </div>
            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => setSaveFilterTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu phân tích DL:</Label>
          <div className='flex flex-1 items-center gap-1'>
            <div className='flex-1'>
              <FormField
                name='dataAnalysisTemplate'
                label=''
                type='select'
                options={[{ value: 'no_analysis', label: 'Không phân tích' }]}
              />
            </div>
            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={873}
                items={[
                  {
                    value: 'save_new',
                    label: 'Tạo mới mẫu phân tích',
                    icon: 7,
                    onClick: () => setSaveAnalysisTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />

      {/* Save Analysis Template Dialog */}
      <SaveTemplateDialog
        open={saveAnalysisTemplateDialogOpen}
        onClose={() => setSaveAnalysisTemplateDialogOpen(false)}
        onSave={handleSaveAnalysisTemplate}
        templateType='analysis'
      />
    </div>
  );
};

export default OtherTab;
