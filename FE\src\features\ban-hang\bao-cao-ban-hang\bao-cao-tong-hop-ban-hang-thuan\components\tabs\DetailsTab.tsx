import { CustomerSearchField } from '@/components/custom/arito/form/search-fields/CustomerSearchField';
import { customerGroupColumns, regionColumns, itemColumns, productGroupColumns } from '../../columns';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const DetailsTab = () => {
  return (
    <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        {/* Mã khách hàng */}
        <CustomerSearchField labelClassName='w-40' />
        <div className='flex items-center gap-1'>
          <Label className='mt-1.5 w-40'>Nhóm khách hàng</Label>
          <div className='grid grid-cols-3 gap-2'>
            <FormField
              type='text'
              label=''
              name='nhom_khach_hang_1'
              withSearch={true}
              searchEndpoint='CustomerGroup'
              searchColumns={customerGroupColumns}
            />
            <FormField
              type='text'
              label=''
              name='nhom_khach_hang_2'
              withSearch={true}
              searchEndpoint='CustomerGroup'
              searchColumns={customerGroupColumns}
            />
            <FormField
              type='text'
              label=''
              name='nhom_khach_hang_3'
              withSearch={true}
              searchEndpoint='CustomerGroup'
              searchColumns={customerGroupColumns}
            />
          </div>
        </div>
        <div className='flex items-center gap-1'>
          <FormField
            name='region'
            label='Khu vực'
            labelClassName='w-40'
            type='text'
            searchEndpoint='/api/regions'
            searchResultLabelKey='regionName'
            searchResultValueKey='regionCode'
            searchColumns={regionColumns}
          />
        </div>
        <div className='flex items-center gap-1 gap-x-5'>
          <FormField
            name='itemCode'
            label='Mã vật tư'
            labelClassName='w-40'
            type='text'
            searchEndpoint='/api/items'
            searchResultLabelKey='itemName'
            searchResultValueKey='itemCode'
            searchColumns={itemColumns}
          />
          <div className='whitespace-nowrap'>
            <FormField name='trackInventory' type='checkbox' label='Chỉ xem vật tư có theo dõi tồn kho' />
          </div>
        </div>

        <div className='flex items-center gap-1'>
          <Label className='mt-1.5 w-40'>Nhóm vật tư</Label>
          <div className='grid grid-cols-3 gap-2'>
            <FormField
              name='productGroup1'
              label=''
              type='text'
              searchEndpoint='/api/product-groups'
              searchResultLabelKey='productGroupName'
              searchResultValueKey='productGroupCode'
              searchColumns={productGroupColumns}
            />
            <FormField
              name='productGroup2'
              label=''
              type='text'
              searchEndpoint='/api/product-groups'
              searchResultLabelKey='productGroupName'
              searchResultValueKey='productGroupCode'
              searchColumns={productGroupColumns}
            />
            <FormField
              name='productGroup3'
              label=''
              type='text'
              searchEndpoint='/api/product-groups'
              searchResultLabelKey='productGroupName'
              searchResultValueKey='productGroupCode'
              searchColumns={productGroupColumns}
            />
          </div>
        </div>
        <div className='flex items-center gap-1'>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='balance'
                label='Số dư'
                labelClassName='w-40'
                type='select'
                options={[
                  { value: '0', label: '0. Tất cả' },
                  { value: '1', label: '1. Chỉ có hóa đơn số dư lớn hơn 0' },
                  { value: '2', label: '2. Chỉ những hóa đơn đã tất toán' }
                ]}
                className='w-[400px]'
                defaultValue={'0'}
              />
            </div>
          </div>
        </div>
        <div className='flex items-center gap-1'>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='details_collection'
                label='Chi tiết thu tiền'
                labelClassName='w-40'
                type='select'
                options={[
                  { value: '0', label: '0. Không' },
                  { value: '1', label: '1. Có' }
                ]}
                className='w-[400px]'
                defaultValue={'1'}
              />
            </div>
          </div>
        </div>
        <div className='flex items-center gap-1'>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='reportTemplate'
                label='Mẫu báo cáo'
                labelClassName='w-40'
                type='select'
                options={[
                  { value: 'TC', label: 'Mẫu tiền chuẩn' },
                  { value: 'NT', label: 'Mẫu ngoại tệ' }
                ]}
                className='w-[400px]'
                defaultValue={'TC'}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
