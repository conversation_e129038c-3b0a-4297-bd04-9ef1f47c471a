import { styled } from '@mui/material/styles';
import { StyledComponentProps } from '../types';

// Styled file input component
export const StyledFileInput = styled('input')<StyledComponentProps>(({ theme, disabled }) => ({
  fontSize: disabled ? '13px' : '12px',
  fontWeight: disabled ? 600 : 400,
  padding: '4px 2px',
  border: 'none',
  backgroundColor: 'transparent',
  width: '100%',
  height: '22px',
  '&::-webkit-file-upload-button': {
    display: 'none'
  },
  '&::file-selector-button': {
    display: 'none'
  }
}));

// Styled file container component
export const FileInputContainer = styled('div')({
  display: 'flex',
  flexDirection: 'column',
  width: '100%',
  position: 'relative',
  border: 'none',
  borderBottom: '1px solid #ddd'
});

// Styled file list component
export const FileList = styled('div')({
  position: 'absolute',
  top: '22px',
  left: '0',
  width: '100%',
  zIndex: 1,
  backgroundColor: '#fff'
});

// Styled file item component
export const FileItem = styled('div')({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '4px 0',
  fontSize: '12px',
  borderBottom: '1px solid #eee',
  '&:last-child': {
    borderBottom: 'none'
  }
});

export default StyledFileInput;
