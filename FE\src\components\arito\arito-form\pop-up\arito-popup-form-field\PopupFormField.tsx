import { useFormContext, Control } from 'react-hook-form';
import { useMediaQuery, useTheme } from '@mui/material';
import { Box, Typography } from '@mui/material';
import React, { useContext } from 'react';
import { MultiSelectField } from './components/MultiSelectField';
import { useSearchResults } from './hooks/useSearchResults';
import { TextFieldComponent } from './components/TextField';
import { AritoPopupFormContext } from '../arito-popup-form';
import { CheckboxField } from './components/CheckboxField';
import { SearchDialog } from './components/SearchDialog';
import { SelectField } from './components/SelectField';
import { TableField } from './components/TableField';
import { DateField } from './components/DateField';
import { useDebounce } from './hooks/useDebounce';
import { fieldStyles } from './styles/constants';

interface PopupFormFieldProps {
  control?: Control;
  label: string;
  name: string;
  type?: 'text' | 'number' | 'select' | 'checkbox' | 'date' | 'table' | 'multiselect';
  disabled?: boolean;
  options?: { value: string | number; label: string }[];
  error?: string;
  withSearch?: boolean;
  withMultiSearch?: boolean;
  withDate?: boolean;
  className?: string;
  columns?: any[];
  searchEndpoint?: string;
  searchResultLabelKey?: string;
  searchResultValueKey?: string;
  searchColumns?: any[];
  defaultSearchColumn?: string;
  displayRelatedField?: string;
  labelWidth?: string;
  inputWidth?: string;
}

export const PopupFormField: React.FC<PopupFormFieldProps> = ({
  control,
  label,
  name,
  type = 'text',
  disabled = false,
  options,
  error,
  withSearch,
  withMultiSearch,
  withDate = false,
  className,
  columns,
  searchEndpoint,
  searchResultLabelKey,
  searchResultValueKey,
  searchColumns,
  defaultSearchColumn,
  displayRelatedField,
  labelWidth = '140px',
  inputWidth = '1fr'
}) => {
  const formContext = useContext(AritoPopupFormContext);
  const formHookContext = useFormContext();

  // Use control from props if provided, otherwise try form contexts
  const finalControl = control || formContext?.control || formHookContext?.control;
  const { errors, isViewMode } = formContext || {};
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const fieldError = error || (errors && errors[name]?.message);
  const fieldId = name;

  // Search functionality states
  const [searchDialogOpen, setSearchDialogOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedSearchResult, setSelectedSearchResult] = React.useState<any>(null);
  const [selectedSearchResults, setSelectedSearchResults] = React.useState<any[]>([]);
  const [displayText, setDisplayText] = React.useState<string>('');
  const [selectedItems, setSelectedItems] = React.useState<any[]>([]);
  const [relatedFieldValue, setRelatedFieldValue] = React.useState<string>('');
  const [isFullScreen, setIsFullScreen] = React.useState<boolean>(false);

  // Debounce search query
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Use custom hook for search functionality
  const { searchResults, isLoading, fetchSearchResults } = useSearchResults({
    searchEndpoint: searchEndpoint || '',
    defaultSearchColumn,
    searchColumns,
    searchResultLabelKey
  });

  // Handle search dialog
  const handleSearchClick = () => {
    if (isMobile) setIsFullScreen(true);
    setSearchDialogOpen(true);
    if (searchEndpoint) fetchSearchResults('');
  };

  const handleSearchQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle search result selection
  const handleSelectSearchResult = (result: any | any[], onChange: (value: any) => void) => {
    const valueKey = searchResultValueKey || defaultSearchColumn || 'name';
    const labelKey = searchResultLabelKey || defaultSearchColumn || 'name';

    if (withMultiSearch && Array.isArray(result)) {
      setSelectedItems(result);
      const values = result.map(item => item[valueKey]);
      onChange(values);
    } else {
      const actualValue = result[valueKey];
      onChange(actualValue);
      setDisplayText(result[labelKey] || String(actualValue));

      if (displayRelatedField && result[displayRelatedField]) {
        setRelatedFieldValue(result[displayRelatedField]);
      } else {
        setRelatedFieldValue('');
      }
    }

    setSearchDialogOpen(false);
  };

  // Handle removing selected items in multi-search mode
  const handleRemoveItem = (itemToRemove: any, field: any) => {
    const valueKey = searchResultValueKey || defaultSearchColumn || 'name';
    const updatedItems = selectedItems.filter(item => item[valueKey] !== itemToRemove[valueKey]);
    setSelectedItems(updatedItems);
    const updatedValues = updatedItems.map(item => item[valueKey]);
    field.onChange(updatedValues);
  };

  // Fetch search results when query changes
  React.useEffect(() => {
    if (searchDialogOpen && searchEndpoint) {
      fetchSearchResults(debouncedSearchQuery);
    }
  }, [debouncedSearchQuery, searchDialogOpen]);

  // Render appropriate field based on type
  const renderField = () => {
    if (type === 'checkbox') {
      return (
        <CheckboxField
          name={name}
          label={label}
          control={finalControl}
          disabled={disabled}
          isViewMode={isViewMode}
          fieldId={fieldId}
          error={fieldError}
          className={className}
          inputWidth={inputWidth}
        />
      );
    }

    return (
      <Box
        sx={{
          width: '100%',
          '& .form-group': {
            display: 'grid',
            gridTemplateColumns: {
              xs: '1fr',
              sm: type === 'table' ? '1fr' : label === '' ? `0px ${inputWidth}` : `${labelWidth} ${inputWidth}`
            },
            alignItems: {
              xs: 'flex-start',
              sm: 'center'
            },
            marginBottom: '12px',
            gap: '8px'
          }
        }}
      >
        <div className={`form-group ${className || ''}`}>
          <Typography
            variant='body2'
            component='label'
            sx={{
              ...fieldStyles.label,
              display: type === 'table' || label === '' ? 'none' : 'flex'
            }}
          >
            {label}
          </Typography>

          <Box
            sx={{
              width: '100%',
              gridColumn: type === 'table' || label === '' ? '1 / -1' : 'auto'
            }}
          >
            {type === 'table' ? (
              <TableField
                name={name}
                control={finalControl}
                disabled={disabled}
                isViewMode={isViewMode}
                columns={columns}
              />
            ) : type === 'select' ? (
              <SelectField
                name={name}
                control={finalControl}
                disabled={disabled}
                isViewMode={isViewMode}
                fieldId={fieldId}
                options={options}
              />
            ) : type === 'date' ? (
              <DateField
                name={name}
                control={finalControl}
                disabled={disabled}
                isViewMode={isViewMode}
                fieldId={fieldId}
              />
            ) : type === 'multiselect' ? (
              <MultiSelectField
                name={name}
                control={finalControl}
                disabled={disabled}
                isViewMode={isViewMode}
                fieldId={fieldId}
                options={options}
              />
            ) : withSearch || withMultiSearch ? (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: withMultiSearch ? 'flex-start' : 'center',
                  width: '100%',
                  flexDirection: { xs: 'column', sm: 'row' },
                  position: 'relative' // Add this to make the icon position relative to this container
                }}
              >
                {withMultiSearch ? (
                  <MultiSelectField
                    name={name}
                    control={finalControl}
                    disabled={disabled}
                    isViewMode={isViewMode}
                    fieldId={fieldId}
                    options={selectedItems.map(item => ({
                      value: item[searchResultValueKey || defaultSearchColumn || 'name'],
                      label: item[searchResultLabelKey || defaultSearchColumn || 'name']
                    }))}
                  />
                ) : (
                  <TextFieldComponent
                    name={name}
                    control={finalControl}
                    type={type}
                    disabled={disabled}
                    isViewMode={isViewMode}
                    fieldId={fieldId}
                    displayText={displayText}
                    onDisplayTextChange={setDisplayText}
                  />
                )}

                {!(disabled || isViewMode) && (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'text.secondary',
                      cursor: 'pointer',
                      position: 'absolute',
                      top: '50%',
                      transform: 'translateY(-50%)', // Center vertically
                      right: 10, // Adjust as needed
                      '&:hover': {
                        color: '#2563EB'
                      },
                      zIndex: 2 // Ensure it's above the input field
                    }}
                    onClick={handleSearchClick}
                  >
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      width='14'
                      height='14'
                      viewBox='0 0 20 20'
                      fill='currentColor'
                    >
                      <path
                        fillRule='evenodd'
                        d='M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z'
                        clipRule='evenodd'
                      />
                    </svg>
                  </Box>
                )}
              </Box>
            ) : (
              <TextFieldComponent
                name={name}
                control={finalControl}
                type={type}
                disabled={disabled}
                isViewMode={isViewMode}
                fieldId={fieldId}
              />
            )}
          </Box>
        </div>

        {fieldError && (
          <Typography variant='body2' sx={fieldStyles.error}>
            {typeof fieldError === 'string' ? fieldError : 'Invalid input'}
          </Typography>
        )}
      </Box>
    );
  };

  return (
    <>
      {renderField()}

      {(withSearch || withMultiSearch) && searchEndpoint && (
        <SearchDialog
          open={searchDialogOpen}
          onClose={() => setSearchDialogOpen(false)}
          title={withMultiSearch ? `Chọn ${String(label).toLowerCase()}` : `Danh mục ${String(label).toLowerCase()}`}
          searchQuery={searchQuery}
          onSearchQueryChange={handleSearchQueryChange}
          isLoading={isLoading}
          searchResults={searchResults}
          searchColumns={searchColumns}
          searchResultLabelKey={searchResultLabelKey}
          searchResultValueKey={searchResultValueKey}
          selectedResult={selectedSearchResult}
          selectedResults={selectedItems}
          multiSelect={withMultiSearch}
          onResultClick={
            withMultiSearch
              ? (results: any[]) => setSelectedItems(results)
              : (result: any) => setSelectedSearchResult(result)
          }
          onResultDoubleClick={(result: any) => {
            if (!withMultiSearch) {
              handleSelectSearchResult(result, field => {
                const fieldsArray = name.split('.');
                let obj = finalControl._formValues;
                for (let i = 0; i < fieldsArray.length - 1; i++) {
                  obj = obj[fieldsArray[i]] = obj[fieldsArray[i]] || {};
                }
                obj[fieldsArray[fieldsArray.length - 1]] =
                  result[searchResultValueKey || defaultSearchColumn || 'name'];
              });
            }
          }}
          onConfirm={results =>
            handleSelectSearchResult(results, field => {
              const fieldsArray = name.split('.');
              let obj = finalControl._formValues;
              for (let i = 0; i < fieldsArray.length - 1; i++) {
                obj = obj[fieldsArray[i]] = obj[fieldsArray[i]] || {};
              }
              if (withMultiSearch) {
                obj[fieldsArray[fieldsArray.length - 1]] = results.map(
                  (result: any) => result[searchResultValueKey || defaultSearchColumn || 'name']
                );
              } else {
                obj[fieldsArray[fieldsArray.length - 1]] =
                  results[searchResultValueKey || defaultSearchColumn || 'name'];
              }
            })
          }
          isMobile={isMobile}
        />
      )}
    </>
  );
};
