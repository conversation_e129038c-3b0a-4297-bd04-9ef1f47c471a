import { FieldConfig } from '@/components/arito/arito-form-fields';
import { TabConfig } from '@/components/arito/arito-filter-modal';

export const invoiceAccountReceivableReportFilterFields: FieldConfig[] = [
  {
    key: 'ngay_h_toan_tu_den',
    name: 'ngay_h_toan_tu_den',
    label: '<PERSON><PERSON>y h.toán từ/đến',
    type: 'daterange',
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 9
    }
  },
  {
    key: 'duoc_thanh_toan_de',
    name: 'duoc_thanh_toan_de',
    label: 'Được thanh toán đễ',
    type: 'date',
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'ngoai_te',
    name: 'ngoai_te',
    label: 'Ngoại tệ',
    type: 'select',
    options: [
      { value: 'USD', label: 'USD' },
      { value: 'EUR', label: 'EUR' },
      { value: 'JPY', label: 'JPY' }
    ],
    gridPosition: {
      row: 2,
      col: 0,
      colSpan: 5
    }
  },
  {
    key: 'tai_khoan',
    name: 'tai_khoan',
    label: 'Tài khoản',
    type: 'search',
    searchModalTitle: 'Danh mục tài khoản',
    searchColumns: [
      { field: 'ma_tai_khoan', headerName: 'Mã tài khoản', width: 100 },
      { field: 'ten_tai_khoan', headerName: 'Tên tài khoản', width: 300 },
      { field: 'tai_khoan_me', headerName: 'Tài khoản mẹ', width: 100 },
      { field: 'tk_so_cai', headerName: 'Tk số cái', width: 100 },
      { field: 'tk_chi_tiet', headerName: 'Tk chi tiết', width: 100 },
      { field: 'bac_tk', headerName: 'Bậc tk', width: 100 }
    ],
    docType: 'Account',
    gridPosition: {
      row: 3,
      col: 0,
      colSpan: 6
    }
  }
];

export const invoiceAccountReceivableReportDetailFilterFields: FieldConfig[] = [
  {
    key: 'ma_khach_hang',
    name: 'ma_khach_hang',
    label: 'Mã khách hàng',
    type: 'search',
    searchModalTitle: 'Danh mục khách hàng',
    searchColumns: [
      { field: 'ma_doi_tuong', headerName: 'Mã đối tượng', width: 100 },
      { field: 'ten_doi_tuong', headerName: 'Tên đối tượng', width: 280 },
      { field: 'cong_no_p_thu', headerName: 'Công nợ p/thu', width: 120 },
      { field: 'ma_so_thue', headerName: 'Mã số thuế', width: 120 },
      { field: 'email', headerName: 'Email', width: 160 },
      { field: 'so_dien_thoai', headerName: 'Số điện thoại', width: 160 }
    ],
    docType: 'Customer',
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'nhom_khach_hang',
    name: 'nhom_khach_hang',
    label: 'Nhóm khách hàng',
    type: 'compound',
    inputs: [
      {
        key: 'nhom_khach_hang_1',
        name: 'nhom_khach_hang_1',
        type: 'search',
        searchModalTitle: 'Danh mục nhóm khách hàng',
        searchColumns: [
          { field: 'ma_nhom', headerName: 'Mã nhóm', width: 100 },
          { field: 'ten_nhom', headerName: 'Tên nhóm', width: 280 }
        ],
        docType: 'CustomerGroup',
        widthPercentage: 33.7
      },
      {
        key: 'nhom_khach_hang_2',
        name: 'nhom_khach_hang_2',
        type: 'search',
        searchModalTitle: 'Danh mục nhóm khách hàng',
        searchColumns: [
          { field: 'ma_nhom', headerName: 'Mã nhóm', width: 100 },
          { field: 'ten_nhom', headerName: 'Tên nhóm', width: 280 }
        ],
        docType: 'CustomerGroup',
        widthPercentage: 34.7
      },
      {
        key: 'nhom_khach_hang_3',
        name: 'nhom_khach_hang_3',
        type: 'search',
        searchModalTitle: 'Danh mục nhóm khách hàng',
        searchColumns: [
          { field: 'ma_nhom', headerName: 'Mã nhóm', width: 100 },
          { field: 'ten_nhom', headerName: 'Tên nhóm', width: 280 }
        ],
        docType: 'CustomerGroup',
        widthPercentage: 32
      }
    ],
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 12
    }
  },
  {
    key: 'khu_vuc',
    name: 'khu_vuc',
    label: 'Khu vực',
    type: 'search',
    searchModalTitle: 'Danh mục khu vực',
    searchColumns: [
      { field: 'ma_khu_vuc', headerName: 'Mã khu vực', width: 100 },
      { field: 'ten_khu_vuc', headerName: 'Tên khu vực', width: 280 }
    ],
    docType: 'Territory',
    gridPosition: {
      row: 2,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'so_du',
    name: 'so_du',
    label: 'Số dư',
    type: 'select',
    options: [
      { value: '0', label: '0. Tất cả' },
      { value: '1', label: '1. Chỉ có hóa đơn số dư lớn hơn 0' }
    ],
    gridPosition: {
      row: 3,
      col: 0,
      colSpan: 9
    }
  },
  {
    key: 'chi_tiet_thu_tien',
    name: 'chi_tiet_thu_tien',
    label: 'Chi tiết thu tiền',
    type: 'select',
    options: [
      { value: '0', label: '0. Không' },
      { value: '1', label: '1. Có' }
    ],
    gridPosition: {
      row: 4,
      col: 0,
      colSpan: 9
    }
  },
  {
    key: 'mau_bao_cao',
    name: 'mau_bao_cao',
    label: 'Mẫu báo cáo',
    type: 'select',
    options: [
      { value: '0', label: 'Mẫu tiền chuẩn' },
      { value: '1', label: 'Mẫu ngoại tệ' }
    ],
    gridPosition: {
      row: 5,
      col: 0,
      colSpan: 12
    }
  }
];

export const invoiceAccountReceivableReportOtherFilterFields: FieldConfig[] = [
  {
    key: 'so_c_tu_tu_den',
    name: 'so_c_tu_tu_den',
    label: 'Số c/từ(từ/đến)',
    type: 'compound',
    inputs: [
      {
        key: 'so_c_tu_tu',
        name: 'so_c_tu_tu',
        type: 'text'
      },
      {
        key: 'so_c_tu_den',
        name: 'so_c_tu_den',
        type: 'text'
      }
    ],
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 9
    }
  },
  {
    key: 'mau_loc_bao_cao',
    name: 'mau_loc_bao_cao',
    label: 'Mẫu lọc báo cáo',
    type: 'select',
    options: [{ value: '0', label: 'Người dùng tự lọc' }],
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 12
    }
  },
  {
    key: 'mau_phan_tich_dl',
    name: 'mau_phan_tich_dl',
    label: 'Mẫu phân tích DL',
    type: 'select',
    options: [{ value: '0', label: 'Không phân tích' }],
    gridPosition: {
      row: 2,
      col: 0,
      colSpan: 12
    }
  }
];

export const invoiceAccountReceivableReportFilterTabs: TabConfig[] = [
  {
    key: 'chi_tiet',
    title: 'Chi tiết',
    fields: invoiceAccountReceivableReportDetailFilterFields,
    columns: 20
  },
  {
    key: 'khac',
    title: 'Khác',
    fields: invoiceAccountReceivableReportOtherFilterFields,
    columns: 20
  }
];
