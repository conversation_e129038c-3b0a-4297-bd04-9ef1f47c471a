import { useFormContext } from 'react-hook-form';
import { Calendar } from 'lucide-react';
import React, { useState } from 'react';
import dayjs from 'dayjs';
import { FormField } from '@/components/custom/arito/form/form-field';

interface AritoDateRangeWithDropdownProps {
  fromDateName: string;
  toDateName: string;
}

const AritoDateRangeWithDropdown: React.FC<AritoDateRangeWithDropdownProps> = ({ fromDateName, toDateName }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { setValue } = useFormContext();

  const handleMouseLeave = () => {
    // Add a small delay before closing the dropdown
    setTimeout(() => {
      setIsOpen(false);
    }, 300);
  };

  const handleSelectOption = (option: string) => {
    const today = dayjs();

    switch (option) {
      case 'today':
        setValue(fromDateName, today.format('YYYY-MM-DD'));
        setValue(toDateName, today.format('YYYY-MM-DD'));
        break;
      case 'yesterday':
        setValue(fromDateName, today.subtract(1, 'day').format('YYYY-MM-DD'));
        setValue(toDateName, today.subtract(1, 'day').format('YYYY-MM-DD'));
        break;
      case 'thisWeek':
        setValue(fromDateName, today.startOf('week').format('YYYY-MM-DD'));
        setValue(toDateName, today.format('YYYY-MM-DD'));
        break;
      case 'lastWeek':
        setValue(fromDateName, today.subtract(1, 'week').startOf('week').format('YYYY-MM-DD'));
        setValue(toDateName, today.subtract(1, 'week').endOf('week').format('YYYY-MM-DD'));
        break;
      case 'thisMonth':
        setValue(fromDateName, today.startOf('month').format('YYYY-MM-DD'));
        setValue(toDateName, today.format('YYYY-MM-DD'));
        break;
      case 'lastMonth':
        setValue(fromDateName, today.subtract(1, 'month').startOf('month').format('YYYY-MM-DD'));
        setValue(toDateName, today.subtract(1, 'month').endOf('month').format('YYYY-MM-DD'));
        break;
      case 'thisQuarter':
        // Calculate current quarter start (months 0-2 = Q1, 3-5 = Q2, 6-8 = Q3, 9-11 = Q4)
        const currentMonth = today.month();
        const quarterStartMonth = Math.floor(currentMonth / 3) * 3;
        setValue(fromDateName, today.month(quarterStartMonth).startOf('month').format('YYYY-MM-DD'));
        setValue(toDateName, today.format('YYYY-MM-DD'));
        break;
      case 'lastQuarter':
        // Calculate last quarter
        const lastQuarterMonth = today.month();
        const lastQuarterStartMonth = Math.floor(lastQuarterMonth / 3) * 3 - 3;
        const lastQuarterEndMonth = lastQuarterStartMonth + 2;
        setValue(fromDateName, today.month(lastQuarterStartMonth).startOf('month').format('YYYY-MM-DD'));
        setValue(toDateName, today.month(lastQuarterEndMonth).endOf('month').format('YYYY-MM-DD'));
        break;
      case 'thisYear':
        setValue(fromDateName, today.startOf('year').format('YYYY-MM-DD'));
        setValue(toDateName, today.format('YYYY-MM-DD'));
        break;
      case 'lastYear':
        setValue(fromDateName, today.subtract(1, 'year').startOf('year').format('YYYY-MM-DD'));
        setValue(toDateName, today.subtract(1, 'year').endOf('year').format('YYYY-MM-DD'));
        break;
    }

    setIsOpen(false);
  };

  return (
    <div className='flex items-center gap-2'>
      <div className='grid flex-1 grid-cols-1 gap-4 md:grid-cols-2'>
        <FormField name={fromDateName} label='' type='date' />
        <FormField name={toDateName} label='' type='date' />
      </div>
      <div className='relative'>
        <button
          type='button'
          className='flex h-9 w-9 items-center justify-center rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground'
          onMouseEnter={() => setIsOpen(true)}
        >
          <Calendar className='h-4 w-4' />
        </button>
        {isOpen && (
          <div
            className='absolute right-0 z-10 mt-1 w-56 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5'
            onMouseEnter={() => setIsOpen(true)}
            onMouseLeave={handleMouseLeave}
          >
            <div className='py-1'>
              <button
                type='button'
                className='w-full px-4 py-2 text-left hover:bg-gray-100'
                onClick={() => handleSelectOption('today')}
              >
                Hôm nay
              </button>
              <button
                type='button'
                className='w-full px-4 py-2 text-left hover:bg-gray-100'
                onClick={() => handleSelectOption('yesterday')}
              >
                Hôm qua
              </button>
              <button
                type='button'
                className='w-full px-4 py-2 text-left hover:bg-gray-100'
                onClick={() => handleSelectOption('thisWeek')}
              >
                Tuần này
              </button>
              <button
                type='button'
                className='w-full px-4 py-2 text-left hover:bg-gray-100'
                onClick={() => handleSelectOption('lastWeek')}
              >
                Tuần trước
              </button>
              <button
                type='button'
                className='w-full px-4 py-2 text-left hover:bg-gray-100'
                onClick={() => handleSelectOption('thisMonth')}
              >
                Tháng này
              </button>
              <button
                type='button'
                className='w-full px-4 py-2 text-left hover:bg-gray-100'
                onClick={() => handleSelectOption('lastMonth')}
              >
                Tháng trước
              </button>
              <button
                type='button'
                className='w-full px-4 py-2 text-left hover:bg-gray-100'
                onClick={() => handleSelectOption('thisQuarter')}
              >
                Quý này
              </button>
              <button
                type='button'
                className='w-full px-4 py-2 text-left hover:bg-gray-100'
                onClick={() => handleSelectOption('lastQuarter')}
              >
                Quý trước
              </button>
              <button
                type='button'
                className='w-full px-4 py-2 text-left hover:bg-gray-100'
                onClick={() => handleSelectOption('thisYear')}
              >
                Năm nay
              </button>
              <button
                type='button'
                className='w-full px-4 py-2 text-left hover:bg-gray-100'
                onClick={() => handleSelectOption('lastYear')}
              >
                Năm trước
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AritoDateRangeWithDropdown;
