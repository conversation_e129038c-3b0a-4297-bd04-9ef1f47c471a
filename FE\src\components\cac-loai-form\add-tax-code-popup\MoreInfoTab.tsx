import { Modal, Box } from '@mui/material';
import React, { useState } from 'react';
import BasicInfoTab from '../popup-form-type-1/khai-bao-thong-tin-khach-hang/BasicInfoTab';
import DetailTab from '../popup-form-type-1/khai-bao-thong-tin-khach-hang/DetailTab';
import { FormField } from '@/components/custom/arito/form/form-field';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';
interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const MoreInfoTab = ({ formMode }: Props) => {
  const [showCustomerInfoForm, setShowCustomerInfoForm] = useState(false);
  const [customerInfoData, setCustomerInfoData] = useState<any[]>([]);

  const handleShowForm = () => {
    setShowCustomerInfoForm(true);
  };

  const handleCloseForm = () => {
    setShowCustomerInfoForm(false);
  };

  const handleSubmitForm = (data: any) => {
    console.log('Form data:', data);
    setShowCustomerInfoForm(false);
  };

  const handleTableDataChange = (newData: any[]) => {
    setCustomerInfoData(newData);
  };
  return (
    <div className='grid grid-cols-1 gap-x-8 space-y-2 p-4 lg:grid-cols-1 lg:space-y-0'>
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Mô tả'
        name='description'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Địa chỉ giao hàng'
        name='shippingAddress'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Lĩnh vực hoạt động'
        name='businessField'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <button
        className='mt-6 flex w-[300px] items-center justify-center gap-x-2 rounded-md border border-gray-300 p-2 text-xs font-semibold hover:bg-gray-100'
        onClick={handleShowForm}
      >
        Khai báo thêm thông tin khách hàng
      </button>

      <Modal
        open={showCustomerInfoForm}
        onClose={handleCloseForm}
        aria-labelledby='customer-info-form-modal'
        BackdropProps={{
          style: { backgroundColor: 'rgba(0, 0, 0, 0.5)' }
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '80%',
            maxWidth: '1000px',
            maxHeight: '90vh',
            bgcolor: 'background.paper',
            boxShadow: 24,
            borderRadius: '4px',
            overflow: 'hidden'
          }}
        >
          <AritoForm
            mode={formMode}
            title='Khai báo thông tin khách hàng'
            onSubmit={handleSubmitForm}
            onClose={handleCloseForm}
            initialData={{}}
            headerFields={<BasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'chi-tiet',
                label: 'Chi tiết',
                component: <DetailTab value={customerInfoData} onChange={handleTableDataChange} formMode={formMode} />
              }
            ]}
            className='relative z-50'
          />
        </Box>
      </Modal>
    </div>
  );
};

export default MoreInfoTab;
