import { styled } from '@mui/material/styles';
import { StyledComponentProps } from '../types';

// Simpler approach for date input - use native date input
export const StyledDateInput = styled('input')<StyledComponentProps>(({ theme, disabled }) => ({
  width: '100%',
  height: '22px',
  border: 'none',
  borderBottom: disabled ? 'none' : '1px solid #ddd',
  padding: '0 4px 0 2px',
  fontSize: disabled ? '13px' : '12px',
  fontWeight: disabled ? 600 : 400,
  backgroundColor: 'transparent',
  overflow: 'visible',
  textOverflow: 'clip',
  whiteSpace: 'normal',
  '&:focus': {
    outline: 'none',
    borderBottomColor: '#0b87c9',
    borderBottomWidth: '1px'
  },
  '&:disabled': {
    backgroundColor: 'transparent',
    cursor: 'default'
  },
  '&::-webkit-calendar-picker-indicator': {
    cursor: 'default',
    width: '22px',
    height: '22px',
    padding: '3px',
    marginRight: '-3px',
    borderRadius: '50%',
    boxSizing: 'border-box',
    border: '1px solid transparent',
    filter: 'invert(43%) sepia(97%) saturate(1752%) hue-rotate(177deg) brightness(97%) contrast(87%)',
    '&:hover': {
      opacity: 1,
      backgroundColor: 'rgba(32, 139, 197, 0.08)',
      border: '1px solid rgb(65, 165, 218, 0.5)'
    }
  }
}));

export default StyledDateInput;
