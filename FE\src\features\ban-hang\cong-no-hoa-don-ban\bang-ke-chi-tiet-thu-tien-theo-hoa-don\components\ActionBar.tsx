import { <PERSON>n, Printer, Refresh<PERSON>w, Search, Sheet } from 'lucide-react';
import { vi } from 'date-fns/locale';
import { format } from 'date-fns';
import { AritoActionButton } from '@/components/custom/arito/action-button/index';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';
import { SearchFormValues } from '../types';

interface ActionBarProps {
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onPrintClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  searchParams?: SearchFormValues;
  className?: string;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onPrintClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  searchParams,
  className
}) => {
  const formatDateRange = () => {
    if (!searchParams?.ngay_h_toan_tu && !searchParams?.ngay_h_toan_den) {
      return { fromDate: '01/04/2025', toDate: '03/04/2025' }; // Default fallback
    }

    const fromDate = searchParams?.ngay_h_toan_tu;
    const toDate = searchParams?.ngay_h_toan_den;

    try {
      const formattedFromDate = fromDate ? format(new Date(fromDate), 'dd/MM/yyyy', { locale: vi }) : '';
      const formattedToDate = toDate ? format(new Date(toDate), 'dd/MM/yyyy', { locale: vi }) : '';
      return { fromDate: formattedFromDate, toDate: formattedToDate };
    } catch (error) {
      return { fromDate: fromDate || '', toDate: toDate || '' };
    }
  };

  const { fromDate, toDate } = formatDateRange();

  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Bảng kê chi tiết thu tiền theo hóa đơn</h1>
          <div className='text-[10px] text-gray-500'>
            <p>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              <span className='font-semibold'>Từ ngày {fromDate}</span>
              <span> đến ngày </span>
              <span className='font-semibold'>{toDate}</span>
            </p>
          </div>
        </div>
      }
    >
      {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />}
      <AritoMenuButton
        title='In ấn'
        icon={<Printer />}
        items={[
          {
            title: 'Mẫu chuẩn',
            icon: <AritoIcon icon={20} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          },
          {
            title: 'Mẫu ngoại tệ',
            icon: <AritoIcon icon={20} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          },
          {
            title: 'Mẫu chuẩn - Song ngữ',
            icon: <AritoIcon icon={20} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          },
          {
            title: 'Mẫu ngoại tệ - Song ngữ',
            icon: <AritoIcon icon={20} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
      {/* No print button */}
      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='destructive' />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
      )}
      {onExportDataClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
