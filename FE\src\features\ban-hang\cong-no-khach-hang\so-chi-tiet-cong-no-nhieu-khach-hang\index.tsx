'use client';

import { useState, useEffect } from 'react';
import { EditPrintTemplateDialog, InitialSearchDialog, ActionBar } from './components';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { useDialogState, useTableData, useActionHandlers } from './hooks';
import { useSoChiTietCongNoNhieuKhachHang } from '@/hooks';
import { SearchFormValues } from './schema';

export default function SoChiTietCongNoNhieuKhachHangPage() {
  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate
  } = useDialogState();

  const [searchParams, setSearchParams] = useState<SearchFormValues>({});
  const [pendingSearch, setPendingSearch] = useState(false);
  const { data, isLoading, fetchData, refreshData } = useSoChiTietCongNoNhieuKhachHang(searchParams);
  const { tables, handleRowClick } = useTableData(data);
  const { handleRefreshClick, handleFixedColumnsClick, handleExportDataClick } = useActionHandlers();

  // Auto show table when data is loaded after search
  useEffect(() => {
    if (pendingSearch && data.length > 0 && !showTable) {
      handleInitialSearch({});
      setPendingSearch(false);
    }
  }, [data.length, pendingSearch, showTable, handleInitialSearch]);

  const handleSearchWithData = async (values: SearchFormValues) => {
    setSearchParams(values);
    setPendingSearch(true);

    try {
      await fetchData(values);
      handleInitialSearchClose();
    } catch (error) {
      console.error('Error fetching data:', error);
      setPendingSearch(false);
      handleInitialSearchClose();
    }
  };

  const handleRefreshWithData = async () => {
    await refreshData();
    handleRefreshClick();
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleSearchWithData} // Use new handler
      />

      <EditPrintTemplateDialog
        open={editPrintTemplateDialogOpen}
        onClose={handleClosePrintTemplateDialog}
        onSave={handleSavePrintTemplate}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshWithData}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            searchParams={searchParams}
            className='border-b border-gray-200'
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}
    </div>
  );
}
