import React, { useState } from 'react';

import { DetailsTab, FilterByObjectTab, OtherTab, BasicInfo } from './tabs';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { searchSchema, initialValues } from '../schema';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';
import { BottomBar } from '@/components/custom/arito';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [formValues, setFormValues] = useState(initialValues);

  const handleSubmit = (data: any) => {
    onSearch(data);
  };

  const handleDirectSubmit = () => {
    onSearch(formValues);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo bán hàng nhiều kỳ'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          setFormValues(data);
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab />
                },
                {
                  id: 'filter-by-object',
                  label: 'Lọc theo đối tượng',
                  component: <FilterByObjectTab />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar mode='add' onSubmit={handleDirectSubmit} onClose={onClose} />}
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
