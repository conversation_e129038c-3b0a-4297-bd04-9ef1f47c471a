import SearchIcon from '@mui/icons-material/Search';
import { Icon<PERSON>utt<PERSON>, Box } from '@mui/material';
import { Controller } from 'react-hook-form';
import React, { useState } from 'react';
import { InputFieldWrapper, ErrorMessage, SearchIconWrapper } from '../components/InputFieldWrapper';
import AritoSearchModal from '@/components/custom/arito/arito-search-modal';
import { StyledInput } from '../inputs/StyledInput';
import { FormRow } from '../components/FormRow';
import { CommonFieldProps } from '../types';

export const MultiSearchField: React.FC<CommonFieldProps> = ({ field, maxLabelWidth, isViewMode }) => {
  const {
    key,
    label,
    required,
    disabled = isViewMode,
    minWidth,
    valueRender,
    searchModalTitle,
    searchColumns = [],
    docType,
    searchFilters,
    showSearchDisplay,
    count = 3 // Default number of search fields to display
  } = field;

  // State for search modal and selected values
  const [searchModalOpenIndex, setSearchModalOpenIndex] = useState<number | null>(null);
  const [selectedValues, setSelectedValues] = useState<
    Array<{
      value: any;
      display: string | null;
    } | null>
  >(Array(count).fill(null));

  // Label style
  const labelStyle = {
    width: `${maxLabelWidth}px`,
    minWidth: `${maxLabelWidth}px`,
    fontSize: '12px',
    fontWeight: 400,
    marginRight: '8px',
    whiteSpace: 'nowrap' as const,
    overflow: 'visible' as const,
    display: 'inline-block'
  };

  // Input wrapper style
  const inputWrapperStyle = {
    flex: '1 1 auto',
    position: 'relative' as const,
    minWidth: minWidth ? (typeof minWidth === 'number' ? `${minWidth}px` : minWidth) : '0',
    display: 'flex',
    flexDirection: 'column' as const
  };

  // Container style
  const containerStyle = {
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    position: 'relative' as const,
    overflow: 'visible'
  };

  // Label content
  const labelContent = (
    <span style={labelStyle}>
      {label}
      {!disabled && required && <span style={{ color: 'red' }}>*</span>}
    </span>
  );

  // Colon element for view mode
  const colonElement = disabled && (
    <span
      style={{
        marginRight: '10px',
        fontSize: '12px',
        fontWeight: 400
      }}
    >
      :
    </span>
  );

  return (
    <FormRow>
      {labelContent}
      <InputFieldWrapper style={inputWrapperStyle}>
        <div style={containerStyle}>
          {colonElement}
          <Controller
            name={key}
            render={({ field: { onChange, value = Array(count).fill(''), ref }, fieldState: { error } }) => {
              // Ensure the value is an array with the correct length
              if (!Array.isArray(value) || value.length !== count) {
                value = Array(count).fill('');
              }

              // Helper functions
              const handleSearchClick = (index: number) => {
                setSearchModalOpenIndex(index);
              };

              const handleSearchSelect = (selectedRow: any, index: number) => {
                const firstColumnField = searchColumns[0]?.field || 'name';
                const selectedFieldValue = selectedRow[firstColumnField];

                const secondColumnField = searchColumns[1]?.field;
                const displayValue = secondColumnField ? selectedRow[secondColumnField] : null;

                const newValues = [...value];
                newValues[index] = selectedFieldValue;
                onChange(newValues);

                const newSelectedValues = [...selectedValues];
                newSelectedValues[index] = {
                  value: selectedFieldValue,
                  display: displayValue
                };
                setSelectedValues(newSelectedValues);

                setSearchModalOpenIndex(null);
              };

              const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
                const newValues = [...value];
                newValues[index] = e.target.value;
                onChange(newValues);

                // Clear the selectedValue for this index if the input changes
                if (selectedValues[index] && e.target.value !== selectedValues[index]?.value) {
                  const newSelectedValues = [...selectedValues];
                  newSelectedValues[index] = null;
                  setSelectedValues(newSelectedValues);
                }
              };

              return (
                <>
                  <Box
                    sx={{
                      width: '100%',
                      display: 'flex',
                      flexDirection: 'row',
                      gap: '1.2rem'
                    }}
                  >
                    {Array.from({ length: count }).map((_, index) => (
                      <Box
                        key={index}
                        sx={{
                          flex: 1,
                          position: 'relative',
                          minWidth: '80px'
                        }}
                      >
                        <StyledInput
                          type='text'
                          value={value[index] || ''}
                          onChange={e => handleInputChange(e, index)}
                          disabled={disabled}
                          style={{ paddingRight: '30px' }}
                        />
                        {!disabled && (
                          <SearchIconWrapper>
                            <IconButton
                              onClick={() => handleSearchClick(index)}
                              size='small'
                              sx={{
                                boxSizing: 'border-box',
                                padding: '3px',
                                color: '#0b87c9',
                                border: '1px solid transparent',
                                cursor: 'default',
                                '&:hover': {
                                  border: '1px solid rgb(65, 165, 218, 0.5)',
                                  backgroundColor: 'rgba(32, 139, 197, 0.08)'
                                }
                              }}
                            >
                              <SearchIcon style={{ fontSize: '16px' }} />
                            </IconButton>
                            {selectedValues[index]?.display && showSearchDisplay !== false && (
                              <span
                                style={{
                                  position: 'absolute',
                                  left: '28px',
                                  color: 'black',
                                  fontSize: '12px',
                                  whiteSpace: 'nowrap',
                                  zIndex: 10
                                }}
                              >
                                {selectedValues[index]?.display}
                              </span>
                            )}
                          </SearchIconWrapper>
                        )}
                        {searchModalOpenIndex === index && (
                          <AritoSearchModal
                            visible={true}
                            onClose={() => setSearchModalOpenIndex(null)}
                            onSelect={row => handleSearchSelect(row, index)}
                            title={searchModalTitle || `Tìm kiếm ${label} ${index + 1}`}
                            columns={searchColumns}
                            docType={docType}
                            filters={searchFilters}
                          />
                        )}
                      </Box>
                    ))}
                  </Box>
                  {error && <ErrorMessage>{error.message}</ErrorMessage>}
                </>
              );
            }}
          />
        </div>
      </InputFieldWrapper>
    </FormRow>
  );
};

export default MultiSearchField;
