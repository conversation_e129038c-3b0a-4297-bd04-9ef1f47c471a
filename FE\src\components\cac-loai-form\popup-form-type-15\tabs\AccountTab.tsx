import Link from 'next/link';
import React from 'react';
import BasicInfoTabType2 from '../../popup-form-type-2/BasicInfoTabType2';
import { FormField } from '@/components/custom/arito/form/form-field';
import { TKSearchColBasicInfo } from '../cols-definition';
import { Type2Tabs } from '../../popup-form-type-2/Tabs';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const AccountTab = ({ formMode }: Props) => {
  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-y-4'>
        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Tk kho/ chi phí'
            name='tk_kho_chi_phi'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='account'
            searchColumns={TKSearchColBasicInfo}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType2 formMode={formMode} />}
            tabs={Type2Tabs({ formMode })}
          />
        </div>

        <div className='mt-2'>
          <div className='ml-[160px] flex'>
            <FormField
              className='mr-2 flex items-center'
              label=''
              name='cho_phep_sua_tk'
              type='checkbox'
              disabled={formMode === 'view'}
            />
            <span className='text-sm'>Cho phép sửa tài khoản vật tư khi nhập liệu trên chứng từ</span>
          </div>
        </div>

        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Tk doanh thu'
            name='tk_doanh_thu'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='account'
            searchColumns={TKSearchColBasicInfo}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType2 formMode={formMode} />}
            tabs={Type2Tabs({ formMode })}
          />
        </div>

        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Tk giá vốn'
            name='tk_gia_von'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='account'
            searchColumns={TKSearchColBasicInfo}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType2 formMode={formMode} />}
            tabs={Type2Tabs({ formMode })}
          />
        </div>

        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Tk chiết khấu'
            name='tk_chiet_khau'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='account'
            searchColumns={TKSearchColBasicInfo}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType2 formMode={formMode} />}
            tabs={Type2Tabs({ formMode })}
          />
        </div>

        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Tk khuyến mãi'
            name='tk_khuyen_mai'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='account'
            searchColumns={TKSearchColBasicInfo}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType2 formMode={formMode} />}
            tabs={Type2Tabs({ formMode })}
          />
        </div>

        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Tk trả lại'
            name='tk_tra_lai'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='account'
            searchColumns={TKSearchColBasicInfo}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType2 formMode={formMode} />}
            tabs={Type2Tabs({ formMode })}
          />
        </div>

        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Tk s/p dở dang'
            name='tk_sp_do_dang'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='account'
            searchColumns={TKSearchColBasicInfo}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType2 formMode={formMode} />}
            tabs={Type2Tabs({ formMode })}
          />
        </div>

        <div className='flex items-center'>
          <FormField
            className='flex items-center'
            inputClassName='w-full'
            label='Tk chi phí NVL'
            name='tk_chi_phi_nvl'
            labelClassName='min-w-[150px] text-sm font-medium'
            type='text'
            disabled={formMode === 'view'}
            withSearch
            searchEndpoint='account'
            searchColumns={TKSearchColBasicInfo}
            actionButtons={['add', 'edit']}
            headerFields={<BasicInfoTabType2 formMode={formMode} />}
            tabs={Type2Tabs({ formMode })}
          />
        </div>

        <div className='mt-4'>
          <Link href='#' className='text-blue-500 underline'>
            * Danh mục tài khoản
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AccountTab;
