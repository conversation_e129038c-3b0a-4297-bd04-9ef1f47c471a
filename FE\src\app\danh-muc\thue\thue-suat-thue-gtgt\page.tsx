'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import { BasicInformationTab } from '@/features/danh-muc/thue/thue-suat-thue-gtgt/components/AddingTab';
import { ActionBar } from '@/features/danh-muc/thue/thue-suat-thue-gtgt/components/ActionBar';
import { taxRateColumns } from '@/features/danh-muc/thue/thue-suat-thue-gtgt/cols-definition';
import { AritoPopupForm } from '@/components/arito/arito-form/pop-up/arito-popup-form';
import { taxRateSchema } from '@/features/danh-muc/thue/thue-suat-thue-gtgt/schemas';
import AritoDataTables from '@/components/custom/arito/data-tables';

const initialTaxRateRows: any[] = [
  {
    id: '1',
    taxCode: 'T001',
    taxName: 'Thuế GTGT',
    taxRate: 10,
    outputTaxAccount: 'TK001',
    outputTaxReductionAccount: 'TK002',
    inputTaxAccount: 'TK003',
    inputTaxReductionAccount: 'TK004',
    taxGroup: 'Nhóm 1'
  }
  // ...other initial rows...
];

export default function Page() {
  // Form state
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  //Initial
  const [taxRateRows, setTaxRateRows] = useState<any[]>(initialTaxRateRows);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const order = params.row as any;
    setSelectedObj(order);
  };

  const handleFormSubmit = async (data: any) => {
    console.log(data);
    setShowForm(false);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: taxRateRows,
      columns: taxRateColumns
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      <AritoPopupForm<any>
        open={showForm}
        onClose={handleCloseForm}
        mode={formMode}
        title='Mới'
        initialData={currentObj || undefined}
        onSubmit={handleFormSubmit}
        schema={taxRateSchema}
        headerFields={<BasicInformationTab formMode={formMode} />}
        maxWidth='md'
      />

      <ActionBar
        onAddClick={handleOpenAddForm}
        onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
        onViewClick={() => selectedObj && handleOpenViewForm(selectedObj)}
        isEditDisabled={!selectedObj}
        isViewDisabled={!selectedObj}
      />
      <div className='w-full overflow-hidden'>
        <AritoDataTables tables={tables} onRowClick={handleRowClick} />
      </div>
    </div>
  );
}
