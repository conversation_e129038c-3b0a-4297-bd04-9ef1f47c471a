import { Icon<PERSON>utton, ListItemText, MenuItem, Paper, Popper } from '@mui/material';
import { Controller, useFormContext } from 'react-hook-form';
import React, { useRef, useState } from 'react';
import { dateRangeOptions, calculateDateRange, formatDate } from '../utils/dateRangeUtils';
import { InputFieldWrapper, ErrorMessage } from '../components/InputFieldWrapper';
import { StyledDateInput } from '../inputs/StyledDateInput';
import AritoIcon from '@/components/custom/arito/icon';
import { StyledInput } from '../inputs/StyledInput';
import { FormRow } from '../components/FormRow';
import { CommonFieldProps } from '../types';

// Menu item style
const menuItemStyle = {
  fontSize: '0.8rem',
  fontWeight: 600,
  margin: '1px 3px',
  padding: '0px 8px',
  height: 28,
  cursor: 'default',
  boxSizing: 'border-box',
  '&:hover': {
    backgroundColor: 'rgba(25, 118, 210, 0.15) !important',
    outline: '1px solid rgb(171, 211, 239) !important',
    cursor: 'default',
    '& .MuiListItemText-primary': {
      color: '#007acc !important'
    }
  }
};

export const DateRangeField: React.FC<CommonFieldProps> = ({ field, maxLabelWidth, isViewMode }) => {
  const { key, label, required, disabled = isViewMode, minWidth, valueRender, showDaterangeOptions = true } = field;

  const { setValue: setFormValue } = useFormContext();

  // State for menu handling
  const [isHovering, setIsHovering] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Label style
  const labelStyle = {
    width: `${maxLabelWidth}px`,
    minWidth: `${maxLabelWidth}px`,
    fontSize: '12px',
    fontWeight: 400,
    marginRight: '8px',
    whiteSpace: 'nowrap' as const,
    overflow: 'visible' as const,
    display: 'inline-block'
  };

  // Input wrapper style
  const inputWrapperStyle = {
    flex: '1 1 auto',
    position: 'relative' as const,
    minWidth: minWidth ? (typeof minWidth === 'number' ? `${minWidth}px` : minWidth) : '0',
    display: 'flex',
    flexDirection: 'column' as const
  };

  // Container style
  const containerStyle = {
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    position: 'relative' as const,
    overflow: 'visible'
  };

  // Handle menu hover
  const handleMenuHover = (event: React.MouseEvent<HTMLElement>) => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
    setIsHovering(true);
    setMenuAnchor(event.currentTarget);
  };

  // Handle menu leave
  const handleMenuLeave = () => {
    closeTimeoutRef.current = setTimeout(() => {
      setIsHovering(false);
      setMenuAnchor(null);
    }, 1000);
  };

  // Clean up timeout on unmount
  React.useEffect(() => {
    return () => {
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, []);

  // Handle date range preset selection
  const handleDateRangePreset = (preset: string) => {
    const [startDate, endDate] = calculateDateRange(preset);
    setFormValue(`${key}_from`, formatDate(startDate));
    setFormValue(`${key}_to`, formatDate(endDate));

    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
    }

    setIsHovering(false);
    setMenuAnchor(null);
  };

  // Colon element for view mode
  const colonElement = disabled && (
    <span
      style={{
        marginRight: '10px',
        fontSize: '12px',
        fontWeight: 400
      }}
    >
      :
    </span>
  );

  // Label content
  const labelContent = (
    <span style={labelStyle}>
      {label}
      {!disabled && required && <span style={{ color: 'red' }}>*</span>}
    </span>
  );

  return (
    <FormRow>
      {labelContent}
      <InputFieldWrapper style={inputWrapperStyle}>
        <div style={containerStyle}>
          {colonElement}
          {valueRender ? (
            <StyledInput type='text' value={valueRender({}) || ''} disabled={true} />
          ) : (
            <div style={{ display: 'flex', width: '100%', alignItems: 'center' }}>
              <Controller
                name={`${key}_from`}
                render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                  <>
                    <StyledDateInput
                      type='date'
                      value={value || ''}
                      onChange={onChange}
                      disabled={disabled}
                      ref={ref}
                      style={{ width: '100%' }}
                    />
                    {error && <ErrorMessage>{error.message}</ErrorMessage>}
                  </>
                )}
              />
              <div style={{ margin: '0 4px' }}></div>
              <Controller
                name={`${key}_to`}
                render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                  <>
                    <StyledDateInput
                      type='date'
                      value={value || ''}
                      onChange={onChange}
                      disabled={disabled}
                      ref={ref}
                      style={{ width: '100%' }}
                    />
                    {showDaterangeOptions && !disabled && (
                      <div style={{ marginLeft: '4px' }} onMouseEnter={handleMenuHover} onMouseLeave={handleMenuLeave}>
                        <IconButton
                          size='small'
                          sx={{
                            marginBottom: '2px',
                            padding: '4px 8px',
                            borderRadius: '0px',
                            color: '#0b87c9',
                            border: '1px solid transparent',
                            cursor: 'default',
                            '&:hover': {
                              border: '1px solid rgb(65, 165, 218, 0.5)',
                              backgroundColor: 'rgba(32, 139, 197, 0.08)'
                            }
                          }}
                        >
                          <AritoIcon icon={284} />
                        </IconButton>
                        <Popper
                          open={isHovering}
                          anchorEl={menuAnchor}
                          placement='bottom-start'
                          style={{ zIndex: 1300 }}
                        >
                          <Paper
                            onMouseEnter={() => setIsHovering(true)}
                            onMouseLeave={() => setIsHovering(false)}
                            style={{
                              borderRadius: 0,
                              minWidth: 200,
                              padding: 0,
                              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                              border: '1px solid #e0e0e0',
                              maxHeight: 300,
                              overflow: 'auto'
                            }}
                          >
                            {dateRangeOptions.map(option => (
                              <MenuItem
                                key={option.value}
                                sx={menuItemStyle}
                                onClick={() => handleDateRangePreset(option.value)}
                              >
                                <ListItemText
                                  slotProps={{
                                    primary: {
                                      style: {
                                        fontSize: '0.8rem',
                                        margin: '0px',
                                        fontWeight: 400
                                      }
                                    }
                                  }}
                                >
                                  {option.label}
                                </ListItemText>
                              </MenuItem>
                            ))}
                          </Paper>
                        </Popper>
                      </div>
                    )}
                    {error && <ErrorMessage>{error.message}</ErrorMessage>}
                  </>
                )}
              />
            </div>
          )}
        </div>
      </InputFieldWrapper>
    </FormRow>
  );
};

export default DateRangeField;
