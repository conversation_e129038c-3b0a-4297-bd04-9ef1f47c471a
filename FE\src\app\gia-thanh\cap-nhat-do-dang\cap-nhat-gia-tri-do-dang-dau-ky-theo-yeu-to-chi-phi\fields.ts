import { FieldConfig } from '@/components/arito/arito-form-fields';
import { TabConfig } from '@/components/arito/arito-filter-modal';

export const wipBeginningByCostFilterFields: FieldConfig[] = [
  {
    key: 'ky',
    name: 'ky',
    label: '<PERSON><PERSON>',
    type: 'text',
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 3
    }
  },
  {
    key: 'nam',
    name: 'nam',
    label: 'Năm',
    type: 'text',
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 3
    }
  },
  {
    key: 'don_vi',
    name: 'don_vi',
    label: 'Đơn vị',
    type: 'select',
    options: [{ value: '1', label: '0318423416 - CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ' }],
    gridPosition: {
      row: 2,
      col: 0,
      colSpan: 6
    }
  }
];

export const wipBeginningByCostFilterTabs: TabConfig[] = [];

export const wipBeginningByCostFields: FieldConfig[] = [
  {
    key: 'ma_ytcp',
    name: 'ma_ytcp',
    label: 'Mã ytcp',
    type: 'search',
    searchModalTitle: 'Danh mục yếu tố chi phí',
    searchColumns: [
      { field: 'ma_ytcp', headerName: 'Mã yếu tố', width: 150 },
      { field: 'ten_ytcp', headerName: 'Tên yếu tố', width: 300 }
    ],
    docType: 'CostElement',
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'ma_san_pham',
    name: 'ma_san_pham',
    label: 'Mã sản phẩm',
    type: 'search',
    searchModalTitle: 'Danh mục sản phẩm',
    searchColumns: [
      { field: 'ma_san_pham', headerName: 'Mã vật tư', width: 150 },
      { field: 'ten_san_pham', headerName: 'Tên vật tư', width: 300 },
      { field: 'ma_san_pham', headerName: 'Đvt lẻ', width: 150 }
    ],
    docType: 'Product',
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'tien_nt',
    name: 'tien_nt',
    label: 'Tiền nt',
    type: 'number',
    defaultValue: 0.0,
    gridPosition: {
      row: 2,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'tien',
    name: 'tien',
    label: 'Tiền',
    type: 'number',
    defaultValue: 0,
    gridPosition: {
      row: 3,
      col: 0,
      colSpan: 6
    }
  }
];

export const wipBeginningByCostTabs: TabConfig[] = [];
