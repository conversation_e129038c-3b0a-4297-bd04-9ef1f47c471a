import { useFormContext } from 'react-hook-form';
import { customerGroupSearchColumns, customerSearchColumns, regionSearchColumns } from './cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

const DetailsTab: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* 1. Mã khách hàng */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã khách hàng:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
              searchColumns={customerSearchColumns}
              dialogTitle='Danh mục khách hàng'
              columnDisplay='customer_code'
              displayRelatedField='customer_name'
              onValueChange={value => {
                setValue('customerCode', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('customerCode', row.customer_code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* 2. Nhóm khách hàng */}
        <div className='flex w-[76%] items-center'>
          <Label className='w-40 min-w-40 text-left'>Nhóm khách hàng:</Label>
          <div className='flex-1'>
            <div className='grid grid-cols-3'>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}`}
                  searchColumns={customerGroupSearchColumns}
                  dialogTitle='Nhóm khách hàng'
                  columnDisplay='customerGroupCode'
                  displayRelatedField='customerGroupName'
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}`}
                  searchColumns={customerGroupSearchColumns}
                  dialogTitle='Nhóm khách hàng'
                  columnDisplay='customerGroupCode'
                  displayRelatedField='customerGroupName'
                  className='w-full'
                />
              </div>
              <div className='w-full'>
                <SearchField
                  type='text'
                  searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}`}
                  searchColumns={customerGroupSearchColumns}
                  dialogTitle='Nhóm khách hàng'
                  columnDisplay='customerGroupCode'
                  displayRelatedField='customerGroupName'
                  className='w-full'
                />
              </div>
            </div>
          </div>
        </div>

        {/* 3. Khu vực */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Khu vực:</Label>
          <div className='w-[57.5%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHU_VUC}`}
              searchColumns={regionSearchColumns}
              dialogTitle='Khu vực'
              columnDisplay='rg_code'
              displayRelatedField='rgname'
              onValueChange={value => {
                setValue('region', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('region', row.rg_code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* 4. Chi tiết */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Chi tiết:</Label>
          <div className='w-48'>
            <FormField
              name='detail'
              label=''
              type='select'
              options={[
                { value: 'invoice', label: 'Theo hóa đơn' },
                {
                  value: 'customer',
                  label: 'Theo khách hàng'
                }
              ]}
            />
          </div>
        </div>

        {/* 5. Số dư */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số dư:</Label>
          <div className='w-48'>
            <FormField
              name='balance'
              label=''
              type='select'
              options={[
                { value: 'all', label: 'Tất cả' },
                {
                  value: 'more_than_0',
                  label: 'Chỉ có hóa đơn số dư lớn hơn 0'
                }
              ]}
            />
          </div>
        </div>

        {/* 6. Số ngày hạn tt */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số ngày hạn tt:</Label>
          <div className='w-48'>
            <FormField name='numberOfTtDays' label='' type='number' defaultValue={30} />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số ngày cảnh báo:</Label>
          <div className='w-48'>
            <FormField name='numberOfWarningDays' label='' type='number' defaultValue={0} />
          </div>
        </div>

        {/* 7. Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo:</Label>
          <div className='w-3/4 flex-1'>
            <FormField
              name='reportTemplate'
              label=''
              type='select'
              options={[
                { value: 'TC', label: 'Mẫu tiền chuẩn' },
                { value: 'NT', label: 'Mẫu ngoại tệ' }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
