import { styled } from '@mui/material/styles';

// Input field wrapper - updated to accommodate label
export const InputFieldWrapper = styled('div')({
  flex: '1 1 auto',
  position: 'relative',
  minWidth: '0',
  display: 'flex',
  flexDirection: 'column'
});

// Error message component
export const ErrorMessage = styled('div')({
  color: 'red',
  fontSize: '11px',
  marginTop: '2px',
  width: '100%',
  position: 'relative'
});

// Update the SearchIconWrapper to allow for proper positioning
export const SearchIconWrapper = styled('div')({
  position: 'absolute',
  right: '0',
  top: '0',
  display: 'flex',
  alignItems: 'center'
});

// Checkbox wrapper
export const CheckboxContainer = styled('div')({
  marginBottom: '4px',
  width: '100%',
  display: 'flex',
  alignItems: 'center'
});

export default InputFieldWrapper;
