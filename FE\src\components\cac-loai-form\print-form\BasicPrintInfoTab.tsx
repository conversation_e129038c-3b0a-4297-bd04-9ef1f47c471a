import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}
const BasicPrintInfoTab = ({ formMode }: Props) => {
  const deleteSample = () => {};
  const copySample = () => {};
  return (
    <div className='grid grid-cols-1 gap-x-8 space-y-2 p-4 lg:grid-cols-5 lg:space-y-0'>
      <div className='grid gap-x-8 space-y-2 lg:col-span-3 lg:space-y-0'>
        <FormField
          className='flex items-center justify-between gap-x-4'
          labelClassName='min-w-[120px]'
          label='Loại giấy'
          name='paperType'
          type='select'
          disabled={formMode === 'view'}
          options={[{ label: 'Thêm mới mẫu', value: '0' }]}
        />
        <FormField
          className='flex items-center justify-between gap-x-4'
          label='Nhập tên mẫu mới'
          labelClassName='min-w-[120px]'
          name='newTemplateName'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          className='flex items-center justify-between gap-x-4'
          label='Tên khác'
          labelClassName='min-w-[120px]'
          name='otherName'
          type='text'
          disabled={formMode === 'view'}
        />
        <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[2fr,1fr]'>
          <FormField
            className='flex items-center justify-between gap-x-4'
            label='Mẫu'
            labelClassName='min-w-[120px]'
            name='template'
            type='select'
            disabled={formMode === 'view'}
            options={[
              { label: 'Mẫu Kế toán trưởng ký', value: '1' },
              { label: 'Mẫu Kế toán trưởng ký', value: '2' },
              { label: 'Mẫu Kế toán trưởng ký', value: '3' }
            ]}
          />
          <FormField
            className='flex items-center justify-between gap-x-4'
            label=''
            name=''
            labelClassName='min-w-[120px]'
            type='select'
            disabled={formMode === 'view'}
            options={[
              { label: 'Giấy in ngang', value: '1' },
              { label: 'Giấy in đứng', value: '2' }
            ]}
          />
        </div>
        <FormField
          className='flex items-center gap-y-1'
          label='Tiêu đề in'
          labelClassName='min-w-[130px]'
          name='printTitle'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          className='flex items-center gap-y-1'
          label='Tiêu đề 2'
          labelClassName='min-w-[130px]'
          name='printTitle2'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          className='flex items-center gap-y-1'
          label='Tiêu đề phụ'
          labelClassName='min-w-[130px]'
          name='subTitle'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          className='flex items-center gap-y-1'
          label='Tiêu đề phụ 2'
          labelClassName='min-w-[130px]'
          name='subTitle2'
          type='text'
          disabled={formMode === 'view'}
        />
        <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[1fr,1fr]'>
          <FormField
            className='flex items-center gap-y-1'
            label='Mẫu song ngữ'
            name='bilingualTemplate'
            labelClassName='min-w-[130px]'
            type='select'
            disabled={formMode === 'view'}
            options={[
              { label: 'Không', value: '1' },
              { label: 'Có', value: '2' }
            ]}
          />
          <FormField
            className='flex items-center gap-y-1'
            label='In các dòng tổng'
            name=' totalLine'
            labelClassName='min-w-[120px]'
            type='select'
            disabled={formMode === 'view'}
            options={[
              { label: 'Không in', value: '1' },
              { label: 'Có in', value: '2' }
            ]}
          />
        </div>
        <FormField
          className='flex items-center gap-y-1'
          label='Loại mẫu'
          name='printType'
          labelClassName='min-w-[130px]'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { label: 'Mẫu cho người dùng hiện tại', value: '1' },
            { label: 'Mẫu cho tất cả người dùng', value: '2' }
          ]}
        />
        <FormField
          className='flex items-center gap-y-1'
          label='Tên file được tạo'
          name='fileName'
          labelClassName='min-w-[130px]'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>
      <div className='flex justify-start gap-1 lg:col-span-2'>
        <button
          className='h-10 w-24 rounded border px-4 py-1.5 text-[11px] hover:bg-slate-100'
          onClick={() => deleteSample()}
        >
          Xóa mẫu
        </button>
        <button
          className='h-10 w-24 rounded border px-4 py-1.5 text-[11px] hover:bg-slate-100'
          onClick={() => copySample()}
        >
          Sao chép mẫu
        </button>
      </div>
    </div>
  );
};

export default BasicPrintInfoTab;
