import { GridRenderCellParams } from '@mui/x-data-grid';
import React from 'react';

export function AritoCheckboxCellRenderer(params: GridRenderCellParams) {
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%'
      }}
    >
      <input type='checkbox' checked={params.value} style={{ width: '14px', height: '14px' }} disabled />
    </div>
  );
}
