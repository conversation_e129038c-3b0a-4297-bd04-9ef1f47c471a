# ERP Frontend Development Guide

This guide provides an overview of the frontend architecture, patterns, and best practices for developing features in the ERP system.

## Table of Contents

1. [TypeScript Types and Interfaces](#typescript-types-and-interfaces)
2. [API Integration](#api-integration)
3. [Query Hooks](#query-hooks)
4. [Components](#components)
5. [Forms](#forms)
6. [Data Tables](#data-tables)
7. [Best Practices](#best-practices)

## TypeScript Types and Interfaces

### Creating Model Interfaces

1. Create interfaces in the `types/schemas` directory using kebab-case with `.type.ts` extension
2. Match field names exactly with backend model
3. Use `ApiResponse` generic type for API response types
4. For models with related entities, include nested data objects with `_data` suffix
5. Document fields with <PERSON>SDoc comments

Example:

```typescript
// types/schemas/khach-hang.type.ts
import { ApiResponse } from '../api.type';

/**
 * Interface for KhachHang (Customer) model
 */
export interface KhachHang {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Customer code
   */
  customer_code: string;

  /**
   * Customer name
   */
  customer_name: string;

  /**
   * Reference to the region
   */
  region?: string | null;

  /**
   * Region data (populated from region)
   */
  region_data?: {
    uuid: string;
    name: string;
  } | null;
}

/**
 * Type for KhachHang API response
 */
export type KhachHangResponse = ApiResponse<KhachHang>;

/**
 * Type for creating or updating a KhachHang
 */
export interface KhachHangInput {
  customer_code: string;
  customer_name: string;
  region?: string | null;
}
```

## API Integration

### API Client

Use the `api` client from `@/lib/api` for making API requests:

```typescript
import api from '@/lib/api';

// GET request
const response = await api.get<KhachHangResponse>('/entities/${entity.slug}/erp/customers/');

// POST request
const response = await api.post<KhachHang>('/entities/${entity.slug}/erp/customers/', newCustomer);

// PUT request
const response = await api.put<KhachHang>(`/entities/${entity.slug}/erp/customers/${uuid}/`, updatedCustomer);

// DELETE request
await api.delete(`/entities/${entity.slug}/erp/customers/${uuid}/`);
```

## Query Hooks

### Creating Query Hooks

1. Create hooks in the `hooks/queries` directory
2. Follow the pattern of existing hooks
3. Export hooks from the `hooks/queries/index.ts` file
4. Use Vietnamese naming for state variables and functions

Example:

```typescript
// hooks/queries/useKhachHang.ts
import { useState, useEffect } from 'react';
import { KhachHang, KhachHangInput, KhachHangResponse } from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

export const useKhachHang = () => {
  const [khachHangs, setKhachHangs] = useState<KhachHang[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { entity } = useAuth();

  const fetchKhachHangs = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<KhachHangResponse>(`/entities/${entity.slug}/erp/customers/`);
      setKhachHangs(response.data.results);
    } catch (error) {
      console.error('Error fetching customers:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addKhachHang = async (newKhachHang: KhachHangInput) => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.post<KhachHang>(`/entities/${entity.slug}/erp/customers/`, newKhachHang);
      setKhachHangs(prev => [...prev, response.data]);
    } catch (error) {
      console.error('Error adding customer:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Add other CRUD operations...

  useEffect(() => {
    fetchKhachHangs();
  }, [entity?.slug]);

  return {
    khachHangs,
    isLoading,
    addKhachHang,
    // Other functions...
    refreshKhachHangs: fetchKhachHangs
  };
};
```

### Using Query Hooks

Import hooks from the main hooks directory:

```typescript
import { useKhachHang } from '@/hooks/queries';

function MyComponent() {
  const { khachHangs, isLoading, addKhachHang } = useKhachHang();

  // Use the hook data and functions
}
```

## Components

### Custom Arito Components

Always use custom Arito components from `@/components/custom/arito/` directory:

```typescript
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Button } from '@/components/custom/arito/button';
```

> **Important**: Never import components directly from `@/components/arito/`. Always use the custom versions from `@/components/custom/arito/`. For input tables, always use `InputTable` from `@/components/custom/arito/custom-input-table` instead of from `@/components/custom/arito/input-table`.

### Form Components

Always use custom Arito form components:

```typescript
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Form } from '@/components/custom/arito/form/form';
```

> **Important**: Always use `SearchField` from `@/components/custom/arito/form/custom-search-field` instead of `FormField` with search props. When you find `FormField` with search and searchEndpoint props, replace them with `SearchField`.

### Custom Search Fields

Always use the `SearchField` component for search functionality:

```typescript
import { SearchField } from '@/components/custom/arito/form/custom-search-field';

// In your form component
<SearchField
  control={control}
  name="customer"
  label="Khách hàng"
  searchEndpoint="/entities/${entity.slug}/erp/customers/"
  displayField="customer_name"
  valueField="uuid"
  required
/>
```

When implementing `SearchField`, move search column definitions to a separate cols.tsx file:

```typescript
// search-cols.tsx
export const getCustomerSearchColumns = () => [
  {
    field: 'customer_code',
    headerName: 'Mã khách hàng',
    width: 150
  },
  {
    field: 'customer_name',
    headerName: 'Tên khách hàng',
    width: 250
  }
];

// In your form component
import { getCustomerSearchColumns } from './search-cols';

<SearchField
  control={control}
  name="customer"
  label="Khách hàng"
  searchEndpoint="/entities/${entity.slug}/erp/customers/"
  displayField="customer_name"
  valueField="uuid"
  searchColumns={getCustomerSearchColumns()}
  required
/>
```

### Action Components

Use `AritoActionBar` with `AritoActionButton` components for action buttons:

```typescript
import { AritoActionBar, AritoActionButton } from '@/components/custom/arito/action-bar';

<AritoActionBar>
  <AritoActionButton
    onClick={handleAddClick}
    disabled={isLoading}
    icon="add"
    color="primary"
  />
  <AritoActionButton
    onClick={handleEditClick}
    disabled={!selectedRow || isLoading}
    icon="edit"
    color="primary"
  />
</AritoActionBar>
```

## Forms

### Form Validation with Zod

Use Zod schemas for form validation:

```typescript
// schemas.ts
import { z } from 'zod';

export const formSchema = z.object({
  customer_code: z.string().nonempty('Mã khách hàng là bắt buộc'),
  customer_name: z.string().nonempty('Tên khách hàng là bắt buộc'),
  region: z.string().optional(),
  status: z.string().default('1'),

  // For numeric fields, accept string inputs but transform to numbers
  quantity: z.union([z.string().transform(val => parseFloat(val) || 0), z.number()]).default(0),

  // For boolean fields
  is_active: z.boolean().default(true)
});
```

### Form Implementation

Use the Form component with Zod schema:

```typescript
import { Form } from '@/components/custom/arito/form/form';
import { FormField } from '@/components/custom/arito/form/form-field';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { formSchema } from './schemas';

function CustomerForm({ onSubmit }) {
  return (
    <Form
      schema={formSchema}
      onSubmit={onSubmit}
      defaultValues={{
        customer_code: '',
        customer_name: '',
        region: '',
        status: '1'
      }}
    >
      {({ control, formState }) => (
        <>
          <FormField
            control={control}
            name="customer_code"
            label="Mã khách hàng"
            required
          />
          <FormField
            control={control}
            name="customer_name"
            label="Tên khách hàng"
            required
          />
          <SearchField
            control={control}
            name="region"
            label="Khu vực"
            searchEndpoint="/entities/${entity.slug}/erp/regions/"
            displayField="name"
            valueField="uuid"
          />
        </>
      )}
    </Form>
  );
}
```

## Data Tables

### Input Tables

Always use custom input tables for editable data grids:

```typescript
import { InputTable } from '@/components/custom/arito/custom-input-table'; // Always use custom-input-table, not input-table
import { getInputTableColumns } from './cols-definition';

function ProductInputTable({ rows, setRows, selectedRow, setSelectedRow }) {
  const columns = getInputTableColumns();
  const selectedRowId = selectedRow?.uuid;

  const handleAddRow = () => {
    const newRow = {
      uuid: crypto.randomUUID(),
      product_code: '',
      product_name: '',
      quantity: 0,
      price: 0
    };

    const newRows = [...rows, newRow];
    setRows(newRows);

    // Select the newly added row
    setSelectedRow(newRow);
  };

  const handleDeleteRow = (uuid) => {
    const newRows = rows.filter(row => row.uuid !== uuid);
    setRows(newRows);

    // Select the last row after deletion
    if (newRows.length > 0) {
      setSelectedRow(newRows[newRows.length - 1]);
    } else {
      setSelectedRow(null);
    }
  };

  // Create action buttons component
  const actionButtons = (
    <InputTableActionBar
      mode="add"
      handleAddRow={handleAddRow}
      handleDeleteRow={selectedRow ? () => handleDeleteRow(selectedRow.uuid) : undefined}
    />
  );

  return (
    <InputTable
      rows={rows}
      columns={columns}
      getRowId={(row) => row.uuid}
      selectedRowId={selectedRowId}
      onRowClick={(params) => setSelectedRow(params.row)}
      actionButtons={actionButtons}
      mode="add"
    />
  );
}
```

> **Important**: When implementing input tables, always use the `InputTable` from `@/components/custom/arito/custom-input-table` (not from `input-table`). Always enable row selection using `selectedRowId` and ensure that after adding or deleting rows, the `selectedRow` selects the last row. Use the `actionButtons` prop to provide custom action buttons.

### InputTableActionBar

When using the `InputTable` component, you should create an action bar for table operations. You can create a custom action bar component or use the one from the custom-input-table package:

```typescript
import { buttonConfigs, buttonSx } from '@/components/custom/arito/custom-input-table/components/action-button-set';
import { ButtonType } from '@/components/custom/arito/custom-input-table/types';
import AritoIcon from '@/components/custom/arito/icon';
import { FormMode } from '@/types/form';

interface InputTableActionBarProps {
  mode: FormMode;
  handleAddRow?: () => void;
  handleDeleteRow?: () => void;
  handleCopyRow?: () => void;
  handlePasteRow?: () => void;
  handleMoveRow?: (direction: 'up' | 'down') => void;
}

export default function InputTableActionBar({
  mode,
  handleAddRow,
  handleDeleteRow,
  handleCopyRow,
  handlePasteRow,
  handleMoveRow
}: InputTableActionBarProps) {
  const buttons: ButtonConfig[] = [
    {
      type: 'add',
      onClick: handleAddRow || (() => {}),
      disabled: mode === 'view',
      title: 'Thêm dòng'
    },
    {
      type: 'delete',
      onClick: handleDeleteRow || (() => {}),
      disabled: mode === 'view',
      title: 'Xóa dòng'
    }
  ];

  return (
    <div className="flex items-center space-x-1 pb-2">
      {buttons.map(button => {
        const config = buttonConfigs[button.type];
        return (
          <Tooltip key={button.type} title={button.title || config.title}>
            <span>
              <IconButton
                onClick={button.onClick}
                disabled={button.disabled}
                size="small"
                sx={buttonSx}
              >
                <AritoIcon icon={button.icon || config.icon} />
              </IconButton>
            </span>
          </Tooltip>
        );
      })}
    </div>
  );
}
```

### Column Definitions

Define table columns in a separate file:

```typescript
// cols-definition.tsx
import { GridColDef } from '@mui/x-data-grid';

export const getDataTableColumns = (): GridColDef[] => [
  {
    field: 'customer_code',
    headerName: 'Mã khách hàng',
    width: 150
  },
  {
    field: 'customer_name',
    headerName: 'Tên khách hàng',
    width: 250
  },
  {
    field: 'region_data',
    headerName: 'Khu vực',
    width: 150,
    valueGetter: params => params.row.region_data?.name || '',
    renderCell: params => params.value || ''
  }
];
```

### Table Implementation

Use the DataGrid component:

```typescript
import { DataGrid } from '@mui/x-data-grid';
import { getDataTableColumns } from './cols-definition';

function CustomerTable({ customers, onRowClick }) {
  const columns = getDataTableColumns();

  return (
    <DataGrid
      rows={customers}
      columns={columns}
      getRowId={(row) => row.uuid}
      onRowClick={(params) => onRowClick(params.row)}
      autoHeight
      disableColumnMenu
      disableSelectionOnClick
    />
  );
}
```

### CellField Component

The `CellField` component is used for editable cells in the `InputTable`. It provides a consistent way to edit cell values:

```typescript
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { GridColDef } from '@mui/x-data-grid';

// In your column definitions
export const getInputTableColumns = (): GridColDef[] => [
  {
    field: 'product_code',
    headerName: 'Mã sản phẩm',
    width: 150,
    renderCell: (params) => (
      <CellField
        value={params.value}
        onChange={(newValue) => {
          // Handle value change
          const updatedRow = { ...params.row, product_code: newValue };
          // Update your rows state
        }}
      />
    )
  },
  {
    field: 'quantity',
    headerName: 'Số lượng',
    width: 120,
    renderCell: (params) => (
      <CellField
        value={params.value}
        type="number"
        onChange={(newValue) => {
          const updatedRow = { ...params.row, quantity: Number(newValue) };
          // Update your rows state
        }}
      />
    )
  }
];
```

## Best Practices

### TypeScript Generics

Always use TypeScript generics for type safety in hooks and components:

```typescript
// Good - Using generics for type safety
function useFormState<T>(initialState: T) {
  const [state, setState] = useState<T>(initialState);
  return { state, setState };
}

// Bad - Not using generics
function useFormState(initialState: any) {
  const [state, setState] = useState(initialState);
  return { state, setState };
}
```

### Other Best Practices

1. **Optional Chaining**: Use optional chaining for function calls (`fn?.()`) instead of explicit if-checks
2. **Meaningful Names**: Use meaningful variable and function names instead of comments
3. **No Console Logs**: Remove console.log statements from production code
4. **Query Keys**: Use existing constants file for query keys
5. **Index Files**: Create index files for component and hooks folders
6. **Tailwind CSS**: Always use Tailwind CSS classes instead of Material UI's sx prop or inline styles
7. **Form Inputs**: When implementing forms with numeric fields, accept string inputs but convert to numbers when sending to the API
8. **Error Handling**: Always include proper error handling in API calls
9. **Refresh Data**: When implementing API requests that create new records, refresh the data table automatically after successful creation
10. **Input Tables**: Always use `InputTable` from `@/components/custom/arito/custom-input-table` for editable data grids, not from `input-table`
11. **Cell Editing**: Use the `CellField` component from `@/components/custom/arito/custom-input-table/components` for editable cells

## Conclusion

Following these patterns and best practices will ensure consistency across the codebase and make it easier to maintain and extend the application. For more specific examples, refer to existing feature implementations in the codebase.
