import { Button } from '@mui/material';
import React from 'react';
import { EditPrintTemplateBasicInfo, EditPrintTemplatePrintColumns } from './tabs';
import { editPrintTemplateSchema, initialValues } from './schema';
import { AritoDialog } from '@/components/custom/arito/dialog';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';

interface EditPrintTemplateDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
}

export const EditPrintTemplateDialog: React.FC<EditPrintTemplateDialogProps> = ({ open, onClose, onSave }) => {
  const handleSubmit = (data: any) => {
    onSave(data);
    onClose();
  };

  const handleSaveClick = () => {
    const form = document.querySelector('form');
    if (form) {
      form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title=''
      maxWidth='md'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button variant='contained' onClick={handleSaveClick}>
            <AritoIcon icon={884} />
            <span className='ml-1'>Đồng ý</span>
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} />
            <span className='ml-1'>Huỷ</span>
          </Button>
        </>
      }
    >
      <div className='w-[800px] min-w-[800px]'>
        <AritoForm
          mode='add'
          hasAritoActionBar={false}
          schema={editPrintTemplateSchema}
          initialData={initialValues}
          onSubmit={handleSubmit}
          className='w-full'
          headerFields={<EditPrintTemplateBasicInfo />}
        />
      </div>
    </AritoDialog>
  );
};

export default EditPrintTemplateDialog;
