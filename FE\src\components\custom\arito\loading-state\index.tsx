'use client';

import { CircularProgress } from '@mui/material';

export interface LoadingStateProps {
  isLoading: boolean;
  loadingText?: string;
  children: React.ReactNode;
}

export const LoadingState = ({ isLoading, loadingText = 'Loading...', children }: LoadingStateProps) => {
  return (
    <>
      {isLoading && (
        <div className='absolute left-0 top-0 z-10 flex h-full w-full flex-col items-center justify-center bg-white/80'>
          <CircularProgress size={40} />
          <p className='mt-2 text-sm text-gray-600'>{loadingText}</p>
        </div>
      )}
      {children}
    </>
  );
};

export default LoadingState;
