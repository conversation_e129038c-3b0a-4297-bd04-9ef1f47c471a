import { ContextMenuItem } from './types';
import AritoIcon from '../icon';

// Helper function to create context menu items for column operations
export function createColumnMenuItems(
  columnName: string,
  onSort?: (direction: 'asc' | 'desc' | null) => void,
  onHide?: () => void,
  onCustomize?: () => void,
  onClearFilter?: () => void,
  onSearch?: (value: string) => void,
  currentSortDirection?: 'asc' | 'desc' | null
): ContextMenuItem[] {
  return [
    {
      id: 'sort-asc',
      label: 'Sắp xếp tăng dần',
      icon: <AritoIcon icon={13} />,
      type: 'radio',
      group: 'sort',
      selected: currentSortDirection === 'asc',
      onClick: () => onSort && onSort('asc')
    },
    {
      id: 'sort-desc',
      label: 'Sắp xếp giảm dần',
      icon: <AritoIcon icon={24} />,
      type: 'radio',
      group: 'sort',
      selected: currentSortDirection === 'desc',
      onClick: () => onSort && onSort('desc')
    },
    {
      id: 'sort-none',
      label: 'Không sắp xếp',
      icon: <AritoIcon icon={25} />,
      type: 'radio',
      group: 'sort',
      selected: currentSortDirection === null,
      onClick: () => onSort && onSort(null)
    },
    {
      id: 'divider-1',
      label: '',
      type: 'divider'
    },
    {
      id: 'hide-column',
      label: 'Ẩn cột',
      icon: <AritoIcon icon={886} />,
      onClick: onHide
    },
    {
      id: 'customize-column',
      label: 'Tuy chỉnh các cột',
      icon: <AritoIcon icon={887} />,
      onClick: onCustomize
    },
    {
      id: 'divider-2',
      label: '',
      type: 'divider'
    },
    {
      id: 'search',
      label: 'Tìm kiếm',
      type: 'search',
      shortcut: 'Ctrl+L'
    },
    {
      id: 'clear-filter',
      label: `Bỏ lọc cột [${columnName}]`,
      icon: <AritoIcon icon={861} />,
      onClick: onClearFilter
    }
  ];
}
