import React, { useRef, useState, useEffect } from 'react';
import * as Popover from '@radix-ui/react-popover';
import AritoIcon from '@/components/custom/arito/icon';
import { cn } from '@/lib/utils';

interface DropdownItem {
  label: string;
  value: string | number;
  icon?: number | React.ReactNode;
  onClick?: () => void;
}

interface RadixHoverDropdownProps {
  items: DropdownItem[];
  iconNumber?: number | React.ReactNode;
  className?: string;
  buttonClassName?: string;
}

const RadixHoverDropdown: React.FC<RadixHoverDropdownProps> = ({
  items,
  iconNumber = 284,
  className = '',
  buttonClassName = ''
}) => {
  const [open, setOpen] = useState(false);
  const [isHoveringTrigger, setIsHoveringTrigger] = useState(false);
  const [isHoveringContent, setIsHoveringContent] = useState(false);
  const closeTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Effect to manage open state based on hover states
  useEffect(() => {
    if (isHoveringTrigger || isHoveringContent) {
      setOpen(true);
      // Clear any pending close timer
      if (closeTimerRef.current) {
        clearTimeout(closeTimerRef.current);
        closeTimerRef.current = null;
      }
    } else {
      // Set a timer to close the dropdown
      closeTimerRef.current = setTimeout(() => {
        setOpen(false);
      }, 150); // Short delay to allow moving between trigger and content
    }

    // Cleanup timer on unmount
    return () => {
      if (closeTimerRef.current) {
        clearTimeout(closeTimerRef.current);
      }
    };
  }, [isHoveringTrigger, isHoveringContent]);

  const handleTriggerMouseEnter = () => {
    setIsHoveringTrigger(true);
  };

  const handleTriggerMouseLeave = () => {
    setIsHoveringTrigger(false);
  };

  const handleContentMouseEnter = () => {
    setIsHoveringContent(true);
  };

  const handleContentMouseLeave = () => {
    setIsHoveringContent(false);
  };

  const handleItemClick = (item: DropdownItem) => {
    if (item.onClick) {
      item.onClick();
    }
    setOpen(false);
    setIsHoveringTrigger(false);
    setIsHoveringContent(false);
  };

  return (
    <Popover.Root open={open} onOpenChange={setOpen}>
      <div className={className} onMouseEnter={handleTriggerMouseEnter} onMouseLeave={handleTriggerMouseLeave}>
        <Popover.Trigger asChild>
          <button
            type='button'
            className={cn(
              'flex size-9 items-center justify-center rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground',
              buttonClassName
            )}
          >
            {typeof iconNumber === 'number' ? <AritoIcon icon={iconNumber} /> : iconNumber}
          </button>
        </Popover.Trigger>

        <Popover.Portal>
          <Popover.Content
            className='z-[9999] max-h-[300px] w-56 overflow-y-auto rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5'
            sideOffset={5}
            align='end'
            onMouseEnter={handleContentMouseEnter}
            onMouseLeave={handleContentMouseLeave}
          >
            <div className='py-1'>
              {items.map((item, index) => (
                <button
                  key={`${item.value}-${index}`}
                  type='button'
                  className='flex w-full items-center px-4 py-2 text-left text-sm hover:bg-gray-100'
                  onClick={() => handleItemClick(item)}
                >
                  {item.icon &&
                    (typeof item.icon === 'number' ? <AritoIcon icon={item.icon} className='mx-1' /> : item.icon)}
                  <span className={item.icon ? 'ml-2' : ''}>{item.label}</span>
                </button>
              ))}
            </div>
          </Popover.Content>
        </Popover.Portal>
      </div>
    </Popover.Root>
  );
};

export default RadixHoverDropdown;
