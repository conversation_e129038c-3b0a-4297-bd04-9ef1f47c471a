import React from 'react';
import { TKNoSearchColBasicInfo } from '@/components/cac-loai-form/popup-form-type-2/cols-definition';
import { KhuVucColDef, NhanVienColDef, PhuongThucColDef, ThanhToanColDef } from './cols-definition';
import BasicInfoTabType10 from '@/components/cac-loai-form/popup-form-type-10/BasicInfoTabType10';
import BasicInfoTabType2 from '@/components/cac-loai-form/popup-form-type-2/BasicInfoTabType2';
import { NhomColDef } from '@/components/cac-loai-form/popup-form-type-10/cols-definition';
import { Type2Tabs } from '@/components/cac-loai-form/popup-form-type-2/Tabs';
import { FormField } from '@/components/custom/arito/form/form-field';

import BasicInfoTabType13 from '../popup-form-type-13/BasicInfoTabType13';
import { Type13Tabs } from '../popup-form-type-13/Tabs';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const GeneralTab = ({ formMode }: Props) => {
  return (
    <div className='grid grid-cols-1 gap-x-8 space-y-2 p-4 lg:grid-cols-1 lg:space-y-0'>
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Nhân viên bán hàng'
        name='salesEmployee'
        type='text'
        disabled={formMode === 'view'}
        labelClassName='min-w-[180px]'
        withSearch
        searchEndpoint='/employee'
        actionButtons={['add', 'edit']}
        searchColumns={NhanVienColDef}
        headerFields={<BasicInfoTabType13 formMode={formMode} />}
        tabs={Type13Tabs({ formMode })}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Tài khoản ngầm định'
        name='defaultAccount'
        type='text'
        disabled={formMode === 'view'}
        labelClassName='min-w-[180px]'
        withSearch
        searchEndpoint='/financial-account'
        actionButtons={['add', 'edit']}
        searchColumns={TKNoSearchColBasicInfo}
        headerFields={<BasicInfoTabType2 formMode={formMode} />}
        tabs={Type2Tabs({ formMode })}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Mã th.toán công nợ'
        name='accountPayable'
        type='text'
        labelClassName='min-w-[180px]'
        disabled={formMode === 'view'}
        withSearch
        searchEndpoint='/financial-account'
        searchColumns={ThanhToanColDef}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Ph/th th.toán (HĐĐT)'
        name='paymentMethod'
        type='text'
        labelClassName='min-w-[180px]'
        disabled={formMode === 'view'}
        withSearch
        searchEndpoint='/financial-account'
        searchColumns={PhuongThucColDef}
      />
      <FormField
        className='w-[27%] items-start gap-y-1 sm:items-center'
        label='Giới hạn tiền nợ'
        name='debtLimit'
        type='number'
        labelClassName='min-w-[180px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Nhóm 1'
        name='group1'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
        withSearch
        searchEndpoint='/financial-account'
        actionButtons={['add', 'edit']}
        searchColumns={NhomColDef}
        headerFields={<BasicInfoTabType10 formMode={formMode} />}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Nhóm 2'
        name='group2'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
        withSearch
        searchEndpoint='/financial-account'
        actionButtons={['add', 'edit']}
        searchColumns={NhomColDef}
        headerFields={<BasicInfoTabType10 formMode={formMode} />}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Nhóm 3'
        name='group3'
        type='text'
        disabled={formMode === 'view'}
        labelClassName='min-w-[180px]'
        withSearch
        searchEndpoint='/financial-account'
        actionButtons={['add', 'edit']}
        searchColumns={NhomColDef}
        headerFields={<BasicInfoTabType10 formMode={formMode} />}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Khu vực'
        name='area'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
        withSearch
        searchEndpoint='/financial-account'
        searchColumns={KhuVucColDef}
      />
      <div className='grid grid-cols-1 gap-x-20 lg:grid-cols-2'>
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Điện thoại'
          name='phoneNumber'
          labelClassName='min-w-[180px]'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Fax'
          name='fax'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>
      <div className='grid grid-cols-1 gap-x-20 lg:grid-cols-2'>
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Thư(Email)'
          name='email'
          labelClassName='min-w-[180px]'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Trang chủ(Website)'
          name='website'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Ghi chú'
        name='note'
        labelClassName='min-w-[180px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Trạng thái'
        name='status'
        labelClassName='min-w-[180px]'
        type='select'
        disabled={formMode === 'view'}
        options={[
          { value: 'active', label: '1. Còn sử dụng' },
          { value: 'inactive', label: '0. Không sử dụng' }
        ]}
      />
    </div>
  );
};

export default GeneralTab;
