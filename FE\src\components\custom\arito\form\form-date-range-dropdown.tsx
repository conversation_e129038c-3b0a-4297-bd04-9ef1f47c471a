import { useFormContext } from 'react-hook-form';
import React, { useState } from 'react';
import dayjs from 'dayjs';
import { FormField } from '@/components/custom/arito/form/form-field';
import AritoIcon from '@/components/custom/arito/icon';
import { DATE_RANGE_OPTIONS } from '@/lib/dateUtil';

interface AritoFormDateRangeDropdownProps {
  fromDateName: string;
  toDateName: string;
  setDates?: (fromDate: string, toDate: string) => void;
}

const AritoFormDateRangeDropdown: React.FC<AritoFormDateRangeDropdownProps> = ({
  fromDateName,
  toDateName,
  setDates
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { setValue } = useFormContext();

  const handleMouseEnter = () => {
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    setTimeout(() => {
      setIsOpen(false);
    }, 300);
  };

  const handleSelectOption = (optionId: string) => {
    const today = dayjs();
    const option = DATE_RANGE_OPTIONS.find(opt => opt.id === optionId);

    if (option) {
      const { from, to } = option.getValue(today);
      setValue(fromDateName, from);
      setValue(toDateName, to);
      setDates?.(from, to);
    }

    setIsOpen(false);
  };

  return (
    <div className='flex items-center gap-2'>
      <div className='grid flex-1 grid-cols-1 gap-4 md:grid-cols-2'>
        <FormField name={fromDateName} type='date' />
        <FormField name={toDateName} type='date' />
      </div>
      <div className='relative'>
        <button
          type='button'
          className='flex h-9 w-9 items-center justify-center rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground'
          onMouseEnter={handleMouseEnter}
        >
          <AritoIcon icon={284} />
        </button>
        {isOpen && (
          <div
            className='absolute right-0 z-50 mt-1 max-h-[300px] w-56 overflow-y-auto rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5'
            onMouseEnter={() => setIsOpen(true)}
            onMouseLeave={handleMouseLeave}
          >
            <div className='py-1'>
              {DATE_RANGE_OPTIONS.map(option => (
                <button
                  key={option.id}
                  type='button'
                  className='w-full px-4 py-2 text-left text-sm hover:bg-gray-100'
                  onClick={() => handleSelectOption(option.id)}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AritoFormDateRangeDropdown;
