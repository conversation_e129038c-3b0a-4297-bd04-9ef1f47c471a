import { SxProps } from '@mui/material';

export const fieldStyles = {
  formGroup: {
    display: 'grid',
    gridTemplateColumns: {
      xs: '1fr', // Single column on mobile
      sm: 'auto 1fr' // Label and input columns
    },
    alignItems: {
      xs: 'flex-start',
      sm: 'center'
    },
    marginBottom: '12px',
    gap: '8px'
  },

  input: {
    '& .MuiInput-root': {
      fontSize: '14px',
      '&:before': {
        borderBottom: '1px solid #e5e7eb'
      },
      '&:hover:not(.Mui-disabled):before': {
        borderBottom: '1px solid #2563EB'
      },
      '&.Mui-focused:after': {
        borderBottom: '1px solid #2563EB'
      }
    },
    '& .MuiInput-input': {
      padding: '4px 8px'
    }
  },

  label: {
    display: 'flex',
    alignItems: 'center',
    color: '#000000',
    minWidth: 'fit-content',
    fontSize: '0.75rem',
    width: '100%',
    padding: '0 8px'
  },

  error: {
    marginLeft: { xs: 0, sm: 0, md: '160px' },
    gridColumn: { xs: '1', sm: '2' },
    paddingLeft: { xs: 0, sm: '8px' },
    fontSize: '0.75rem',
    marginTop: '4px',
    color: 'error.main'
  },

  searchDialog: {
    title: {
      backgroundColor: '#f2f7fc',
      color: '#000000',
      fontWeight: 'bold',
      fontSize: '18px',
      py: 1,
      px: 2,
      padding: '8px 16px',
      borderBottom: '1px solid #d1d5db',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      minHeight: '40px',
      height: '40px'
    },
    searchBar: {
      padding: '8px 16px',
      backgroundColor: '#f9fcfd',
      borderBottom: '1px solid #e5e7eb',
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between'
    },
    actions: {
      borderTop: '1px solid #e5e7eb',
      py: 1,
      px: 2,
      padding: '8px 16px',
      backgroundColor: '#f2f7fc',
      display: 'flex',
      justifyContent: 'flex-end',
      minHeight: '43px',
      height: '43px'
    }
  },

  buttons: {
    confirm: {
      backgroundColor: '#53a3a3',
      '&:hover': {
        backgroundColor: '#438585'
      },
      '&.Mui-disabled': {
        backgroundColor: '#94a3b8',
        color: 'white'
      },
      textTransform: 'none',
      fontWeight: 'normal',
      borderRadius: '2px',
      padding: '4px 12px',
      minWidth: '80px',
      fontSize: '0.8rem',
      marginLeft: '8px'
    },
    cancel: {
      color: '#53a3a3',
      borderColor: '#53a3a3',
      borderRadius: '2px',
      '&:hover': {
        borderColor: '#438585',
        backgroundColor: 'rgba(37, 99, 235, 0.04)'
      },
      textTransform: 'none',
      fontWeight: 'normal',
      minWidth: '80px',
      padding: '4px 12px',
      fontSize: '0.8rem',
      ml: 1
    }
  },

  chips: {
    chip: {
      margin: '2px',
      backgroundColor: '#E5F1FF',
      color: '#1E3A8A',
      borderRadius: '3px',
      fontSize: '12px',
      height: '22px',
      padding: '0 4px 0 8px',
      '.MuiChip-deleteIcon': {
        width: '16px',
        height: '16px',
        margin: '0 2px 0 4px',
        color: '#1E3A8A',
        '&:hover': {
          color: '#1E3A8A'
        }
      }
    }
  }
} as const;

export const inputVariants = {
  standard: {
    fontSize: '14px',
    borderBottom: '1px solid #e5e7eb',
    '&:hover': {
      borderBottom: '1px solid #2563EB'
    },
    '&:focus': {
      borderBottom: '1px solid #2563EB'
    }
  }
} as const;

export const paperProps = {
  sx: {
    borderRadius: '0px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    overflow: 'hidden'
  }
} as const;

export const dialogStyles = (isMobile: boolean, isFullScreen: boolean): SxProps => ({
  ...(isMobile || isFullScreen
    ? {
        height: '100%',
        width: '100%',
        maxWidth: '100vw',
        maxHeight: '100vh',
        margin: 0
      }
    : {
        height: '450px',
        width: '850px',
        maxHeight: '80vh',
        maxWidth: '90vw'
      }),
  ...paperProps.sx
});
