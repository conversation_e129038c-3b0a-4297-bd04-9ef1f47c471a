import { FieldConfig } from '@/components/arito/arito-form-fields';
import { TabConfig } from '@/components/arito/arito-filter-modal';

export const declareCostAllocationObjectFilterFields: FieldConfig[] = [
  {
    key: 'ky',
    name: 'ky',
    label: 'Kỳ',
    type: 'text',
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 3
    }
  },
  {
    key: 'nam',
    name: 'nam',
    label: 'Năm',
    type: 'text',
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 3
    }
  },
  {
    key: 'don_vi',
    name: 'don_vi',
    label: 'Đơn vị',
    type: 'select',
    options: [
      { value: '0318423416', label: '0318423416 - CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ' }
    ],
    gridPosition: {
      row: 2,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'sao_chep',
    name: 'sao_chep',
    label: 'Sao chép',
    type: 'select',
    options: [
      { value: '0', label: '0. Không' },
      { value: '1', label: '1. <PERSON>é<PERSON> từ kỳ trước' }
    ],
    gridPosition: {
      row: 3,
      col: 0,
      colSpan: 8
    }
  }
];

export const declareCostAllocationObjectFilterTabs: TabConfig[] = [];

export const declareCostAllocationObjectFields: FieldConfig[] = [
  {
    key: 'ma_to',
    name: 'ma_to',
    label: 'Mã yếu tố',
    type: 'search',
    isRequired: true,
    searchModalTitle: 'Danh mục yếu tố',
    searchColumns: [
      { field: 'ma_to', headerName: 'Mã yếu tố', width: 150 },
      { field: 'ten_to', headerName: 'Tên yếu tố', width: 300 }
    ],
    docType: 'ProductionTeam',
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'ma_san_pham',
    name: 'ma_san_pham',
    label: 'Mã sản phẩm',
    type: 'search',
    searchModalTitle: 'Danh mục sản phẩm',
    searchColumns: [
      { field: 'ma_vat_tu', headerName: 'Mã vật tư', width: 150 },
      { field: 'ten_vat_tu', headerName: 'Tên vật tư', width: 300 },
      { field: 'dvt', headerName: 'Đvt lẻ', width: 100 }
    ],
    docType: 'Product',
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 6
    }
  }
];

export const declareCostAllocationObjectTabs: TabConfig[] = [];
