import { Button } from '@mui/material';
import AritoIcon from '@/components/custom/arito/icon';

interface DialogActionsProps {
  onConfirm: () => void;
  onCancel: () => void;
  isConfirmDisabled: boolean;
}

export const DialogActions = ({ onConfirm, onCancel, isConfirmDisabled }: DialogActionsProps) => {
  return (
    <div
      style={{
        borderTop: '1px solid #e5e7eb',
        padding: '4px 16px',
        backgroundColor: '#f2f7fc',
        display: 'flex',
        justifyContent: 'flex-end',
        minHeight: '43px',
        height: '43px',
        alignItems: 'center'
      }}
    >
      <Button
        onClick={onConfirm}
        variant='contained'
        disabled={isConfirmDisabled}
        sx={{
          backgroundColor: '#2563EB',
          '&:hover': {
            backgroundColor: '#1E40AF'
          },
          '&.Mui-disabled': {
            backgroundColor: '#94a3b8',
            color: 'white'
          },
          textTransform: 'none',
          fontWeight: 'normal',
          borderRadius: '2px',
          padding: '3px 10px',
          minWidth: '80px',
          fontSize: '0.8rem'
        }}
      >
        <AritoIcon icon={884} marginX='4px' />
        Đồng ý
      </Button>
      <Button
        onClick={onCancel}
        variant='outlined'
        sx={{
          color: '#2563EB',
          borderColor: '#2563EB',
          borderRadius: '2px',
          '&:hover': {
            borderColor: '#1E40AF',
            backgroundColor: 'rgba(37, 99, 235, 0.1)'
          },
          textTransform: 'none',
          fontWeight: 'normal',
          minWidth: '80px',
          padding: '3px 10px',
          fontSize: '0.8rem',
          ml: 1
        }}
      >
        <AritoIcon icon={885} marginX='4px' />
        Hủy
      </Button>
    </div>
  );
};
