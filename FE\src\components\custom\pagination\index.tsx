import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>utton, Typography } from '@mui/material';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { useRef } from 'react';
import { PaginationProps } from './types';

export function Pagination({ paginationModel, totalItems, onPageChange }: PaginationProps) {
  const paginationRef = useRef<HTMLDivElement>(null);
  const totalPages = Math.ceil(totalItems / paginationModel.pageSize);
  const startItem = paginationModel.page * paginationModel.pageSize + 1;
  const endItem = Math.min((paginationModel.page + 1) * paginationModel.pageSize, totalItems);

  const renderPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    const currentPage = paginationModel.page + 1;

    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <Button
          key={i}
          variant='text'
          onClick={() => onPageChange(i - 1)}
          sx={{
            minWidth: '32px',
            height: '32px',
            padding: '4px 8px',
            color: i === currentPage ? 'black' : 'text.secondary',
            fontWeight: i === currentPage ? 'bold' : 'normal',
            fontSize: '0.75rem',
            '&:hover': {
              backgroundColor: 'transparent',
              color: 'black'
            }
          }}
        >
          {i}
        </Button>
      );
    }
    return pages;
  };

  return (
    <div ref={paginationRef} className='flex flex-shrink-0 items-center gap-4 pr-4'>
      <Typography component='div' sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
        <span style={{ fontWeight: 600, color: 'black' }}>
          {startItem} - {endItem}
        </span>
        {' trong '}
        <span style={{ fontWeight: 600, color: 'black' }}>{totalItems}</span>
      </Typography>
      <Divider orientation='vertical' flexItem sx={{ height: '20px', my: 'auto' }} />
      <div className='flex items-center'>
        <IconButton
          size='small'
          onClick={() => onPageChange(paginationModel.page - 1)}
          disabled={paginationModel.page === 0}
          sx={{
            color: 'text.secondary',
            '&:hover': { color: 'black' }
          }}
        >
          <NavigateBeforeIcon fontSize='small' />
        </IconButton>
        {renderPageNumbers()}
        <IconButton
          size='small'
          onClick={() => onPageChange(paginationModel.page + 1)}
          disabled={paginationModel.page >= totalPages - 1}
          sx={{
            color: 'text.secondary',
            '&:hover': { color: 'black' }
          }}
        >
          <NavigateNextIcon fontSize='small' />
        </IconButton>
      </div>
    </div>
  );
}
