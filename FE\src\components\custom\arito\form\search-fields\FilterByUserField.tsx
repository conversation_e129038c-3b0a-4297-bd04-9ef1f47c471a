import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface FilterByUserFieldProps {
  name?: string;
  label?: string;
  labelClassName?: string;
  className?: string;
  inputClassName?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
  options?: { value: string; label: string }[];
}

// Default filter by user options
const defaultFilterByUserOptions = [
  { value: 'all', label: 'Tất cả' },
  { value: 'created_by', label: 'Lọc theo người tạo' }
];

export const FilterByUserField: React.FC<FilterByUserFieldProps> = ({
  name = 'filterByUser',
  label = 'Lọc theo người SD',
  labelClassName = 'text-sm font-normal text-[13px] pr-2 sm:mb-0 text-left flex items-center min-w-[150px]',
  className = 'flex items-center',
  inputClassName = 'w-[114px]',
  disabled = false,
  formMode = 'add',
  options = defaultFilterByUserOptions
}) => {
  return (
    <div className={className}>
      <Label className={labelClassName}>{label}</Label>
      <FormField
        name={name}
        type='select'
        className={inputClassName}
        label=''
        options={options}
        disabled={disabled || formMode === 'view'}
      />
    </div>
  );
};

export default FilterByUserField;
