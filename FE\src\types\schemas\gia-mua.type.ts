/**
 * TypeScript interface for GiaMua (Purchase Price) model
 *
 * This interface represents the structure of the GiaMua model from the backend.
 * It defines pricing information for products from specific vendors.
 */

import { DonViTinh } from './don-vi-tinh.type';
import { DoiTuong } from './doi-tuong.type';
import { NgoaiTe } from './ngoai-te.type';
import { ApiResponse } from '../api.type';
import { VatTu } from './vat-tu.type';

export interface GiaMua {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the product this price applies to
   */
  ma_vat_tu: string;

  /**
   * Reference to the unit of measure for this price
   */
  don_vi_tinh: string;

  /**
   * Date from which this price is effective
   */
  ngay_hieu_luc?: string | null;

  /**
   * Reference to the vendor offering this price
   */
  nha_cung_cap: string;

  /**
   * Reference to the currency for this price
   */
  ngoai_te?: string | null;

  /**
   * Minimum quantity for this price to apply
   */
  so_luong_tu: number;

  /**
   * Purchase price amount
   */
  gia_mua: number;

  /**
   * Status of the purchase price
   */
  trang_thai: number;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;

  /**
   * Nested data fields from API response
   */
  ma_vat_tu_data?: VatTu | null;
  don_vi_tinh_data?: DonViTinh | null;
  nha_cung_cap_data?: DoiTuong | null;
  ngoai_te_data?: NgoaiTe | null;
}

/**
 * Type for GiaMua API response
 */
export type GiaMuaResponse = ApiResponse<GiaMua>;

/**
 * Type for creating or updating a GiaMua
 */
export interface GiaMuaInput {
  ma_vat_tu: string;
  don_vi_tinh: string;
  ngay_hieu_luc?: string | null;
  nha_cung_cap?: string | null; // Made optional as per user request
  ngoai_te?: string | null;
  so_luong_tu?: number | null; // Made optional as per user request
  gia_mua?: number | null; // Made optional as per user request
  trang_thai: number;
}
