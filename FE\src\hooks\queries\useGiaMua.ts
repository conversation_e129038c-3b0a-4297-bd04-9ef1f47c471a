import { useState, useEffect } from 'react';
import { GiaMua, GiaMuaInput, GiaMuaResponse } from '@/types/schemas/gia-mua.type';
import { FormValues } from '@/features/danh-muc/mua-hang/gia-mua/schemas';
import { VatTu, DonViTinh, DoiTuong, NgoaiTe } from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface GiaMuaFormData {
  formData: FormValues;
  vatTu: VatTu | null;
  donViTinh: DonViTinh | null;
  nhaCungCap: DoiTuong | null;
  ngoaiTe: NgoaiTe | null;
}

interface UseGiaMuaReturn {
  giaMuas: GiaMua[];
  isLoading: boolean;
  addGiaMua: (newGiaMua: GiaMuaInput | GiaMuaFormData) => Promise<GiaMua>;
  updateGiaMua: (uuid: string, updatedGiaMua: GiaMuaInput | GiaMuaFormData) => Promise<GiaMua>;
  deleteGiaMua: (uuid: string) => Promise<void>;
  refreshGiaMuas: () => Promise<void>;
}

/**
 * Hook for managing GiaMua (Purchase Price) data
 *
 * This hook provides functions to fetch, create, update, and delete purchase prices.
 */
export const useGiaMua = (initialGiaMuas: GiaMua[] = []): UseGiaMuaReturn => {
  const [giaMuas, setGiaMuas] = useState<GiaMua[]>(initialGiaMuas);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchGiaMuas = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<GiaMuaResponse>(`/entities/${entity.slug}/erp/${QUERY_KEYS.GIA_MUA}/`);
      setGiaMuas(response.data.results);
    } catch (error) {
      console.error('Error fetching purchase prices:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addGiaMua = async (newGiaMua: GiaMuaInput | GiaMuaFormData): Promise<GiaMua> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      let payload: GiaMuaInput;

      // Check if input is GiaMuaFormData and validate/convert it
      if (isGiaMuaFormData(newGiaMua)) {
        validateGiaMuaFormData(newGiaMua);
        payload = convertFormDataToInput(newGiaMua);
      } else {
        payload = newGiaMua;
      }

      // Thêm header Content-Type để đảm bảo server hiểu đúng định dạng dữ liệu
      const response = await api.post<GiaMua>(`/entities/${entity.slug}/erp/${QUERY_KEYS.GIA_MUA}/`, payload, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      const addedGiaMua = response.data;

      setGiaMuas(prev => [...prev, addedGiaMua]);
      return addedGiaMua;
    } catch (error: any) {
      console.error('Error adding purchase price:', error);
      if (error.response) {
        // Tạo thông báo lỗi chi tiết
        let errorMessage = error.response.data?.detail || '';

        // Handle validation errors which come as an object with field names as keys
        if (typeof error.response.data === 'object' && !error.response.data?.detail) {
          const validationErrors = [];
          for (const field in error.response.data) {
            if (Array.isArray(error.response.data[field])) {
              validationErrors.push(`${field}: ${error.response.data[field].join(', ')}`);
            }
          }
          if (validationErrors.length > 0) {
            errorMessage = validationErrors.join('\n');
          } else {
            errorMessage = JSON.stringify(error.response.data);
          }
        }

        if (!errorMessage) {
          errorMessage = error.message || 'Unknown error';
        }

        throw new Error(errorMessage);
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateGiaMua = async (uuid: string, updatedGiaMua: GiaMuaInput | GiaMuaFormData): Promise<GiaMua> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      let payload: GiaMuaInput;

      // Check if input is GiaMuaFormData and validate/convert it
      if (isGiaMuaFormData(updatedGiaMua)) {
        validateGiaMuaFormData(updatedGiaMua);
        payload = convertFormDataToInput(updatedGiaMua);
      } else {
        payload = updatedGiaMua;
      }

      // Thêm header Content-Type để đảm bảo server hiểu đúng định dạng dữ liệu
      const response = await api.put<GiaMua>(`/entities/${entity.slug}/erp/${QUERY_KEYS.GIA_MUA}/${uuid}/`, payload, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      const updatedGiaMuaData = response.data;

      // Cập nhật state giaMuas với dữ liệu mới
      setGiaMuas(prev => prev.map(giaMua => (giaMua.uuid === updatedGiaMuaData.uuid ? updatedGiaMuaData : giaMua)));

      return updatedGiaMuaData;
    } catch (error: any) {
      console.error('Error updating purchase price:', error);
      if (error.response) {
        // Tạo thông báo lỗi chi tiết
        let errorMessage = error.response.data?.detail || '';

        // Handle validation errors which come as an object with field names as keys
        if (typeof error.response.data === 'object' && !error.response.data?.detail) {
          const validationErrors = [];
          for (const field in error.response.data) {
            if (Array.isArray(error.response.data[field])) {
              validationErrors.push(`${field}: ${error.response.data[field].join(', ')}`);
            }
          }
          if (validationErrors.length > 0) {
            errorMessage = validationErrors.join('\n');
          } else {
            errorMessage = JSON.stringify(error.response.data);
          }
        }

        if (!errorMessage) {
          errorMessage = error.message || 'Unknown error';
        }

        throw new Error(errorMessage);
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteGiaMua = async (uuid: string): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/purchase-prices/${uuid}/`);
      setGiaMuas(prev => prev.filter(giaMua => giaMua.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting purchase price:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshGiaMuas = async (): Promise<void> => {
    await fetchGiaMuas();
  };

  // Helper function to check if data is GiaMuaFormData
  const isGiaMuaFormData = (data: GiaMuaInput | GiaMuaFormData): data is GiaMuaFormData => {
    return 'formData' in data && 'vatTu' in data && 'donViTinh' in data && 'nhaCungCap' in data && 'ngoaiTe' in data;
  };

  // Helper function to validate GiaMuaFormData
  const validateGiaMuaFormData = (data: GiaMuaFormData): void => {
    // 1. Mã vật tư - bắt buộc
    if (!data.vatTu?.uuid) {
      throw new Error('Mã vật tư không được bỏ trống');
    }

    // 2. Đơn vị tính - bắt buộc (có thể có giá trị mặc định)
    if (!data.donViTinh?.uuid) {
      throw new Error('Đơn vị tính không được bỏ trống');
    }

    // 3. Ngày hiệu lực - bắt buộc
    if (!data.formData.ngay_hieu_luc || data.formData.ngay_hieu_luc.trim() === '') {
      throw new Error('Ngày hiệu lực không được bỏ trống');
    }

    // 5. Ngoại tệ - bắt buộc (có thể có giá trị mặc định)
    if (!data.ngoaiTe?.uuid) {
      throw new Error('Ngoại tệ không được bỏ trống');
    }
  };

  // Helper function to convert GiaMuaFormData to GiaMuaInput
  const convertFormDataToInput = (data: GiaMuaFormData): GiaMuaInput => {
    return {
      ma_vat_tu: data.vatTu?.uuid || '',
      don_vi_tinh: data.donViTinh?.uuid || '',
      ngay_hieu_luc: data.formData.ngay_hieu_luc || null,
      nha_cung_cap: data.nhaCungCap?.uuid || null,
      ngoai_te: data.ngoaiTe?.uuid || '',
      so_luong_tu:
        data.formData.so_luong_tu !== undefined &&
        data.formData.so_luong_tu !== '' &&
        data.formData.so_luong_tu !== null
          ? Number(data.formData.so_luong_tu)
          : null,
      gia_mua:
        data.formData.gia_mua !== undefined && data.formData.gia_mua !== '' && data.formData.gia_mua !== null
          ? Number(data.formData.gia_mua)
          : null,
      trang_thai: Number(data.formData.trang_thai) || 1
    };
  };

  useEffect(() => {
    fetchGiaMuas();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity?.slug]);

  return {
    giaMuas,
    isLoading,
    addGiaMua,
    updateGiaMua,
    deleteGiaMua,
    refreshGiaMuas
  };
};
