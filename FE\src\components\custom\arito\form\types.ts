import { GridColDef } from '@mui/x-data-grid';
import { ActionButtonType } from '../input-table/components/TableActionButtonSet/ActionButtonsMap';
import { ActionButtonFormType } from './form-field/hooks/ActionButtonFormMap';
import { TabItem } from '@/components/custom/arito';

export interface AritoFormFieldProps<T = any> extends React.HTMLAttributes<HTMLInputElement> {
  /**
   * The value of the form field
   */
  value?: T;

  /**
   * Callback function to handle value change
   */
  onValueChange?: (value: T) => void;

  /**
   * The label for the form field
   */
  label?: string;

  /**
   * The name of the form field (used for form registration)
   */
  name: string;

  /**
   * The react-hook-form control object
   */
  control?: any;

  /**
   * The type of input field
   */
  type?: 'text' | 'number' | 'select' | 'checkbox' | 'date' | 'table' | 'multiselect' | 'password';

  /**
   * Whether the field is disabled
   */
  disabled?: boolean;

  /**
   * Options for select fields
   */
  options?: { value: any; label: string }[];

  /**
   * Error message to display
   */
  error?: string;

  // withSearch and withDate properties have been removed as they are no longer used

  /**
   * Additional CSS class name
   */
  className?: string;

  /**
   * Columns for table type fields
   */
  columns?: any[];

  /**
   * Default value for the field
   */
  defaultValue?: any;

  /**
   * Placeholder text for the field
   */
  placeholder?: string;

  /**
   * Endpoint for search functionality
   */
  searchEndpoint?: string;

  /**
   * Key to use for displaying search result labels
   */
  searchResultLabelKey?: string;

  /**
   * Key to use for search result values
   */
  searchResultValueKey?: string;

  /**
   * Column definitions for search results table
   */
  searchColumns?: GridColDef[];

  /**
   * Default column to search in
   */
  defaultSearchColumn?: string;

  /**
   * Field to display related information
   */
  displayRelatedField?: string;

  /**
   * Additional CSS class name for the input container
   * These classes will override any conflicting base classes
   * For example, passing "w-20" will override the default "w-full"
   */
  inputClassName?: string;

  /**
   * Additional CSS class name for the label
   * These classes will override any conflicting base classes
   * For example, passing "w-20" will override the default "w-full"
   */
  labelClassName?: string;

  /**
   * Custom action for the form field
   */
  action?: React.ReactNode;

  /**
   * Custom title for the search dialog
   */
  dialogTitle?: string;

  /**
   * Hide the row number column in the search dialog
   */
  hideRowNumberColumn?: boolean;

  /**
   * Action bar for the search dialog
   */
  actionButtons?: ActionButtonFormType[];

  /**
   * Header fields for the search dialog
   */
  headerFields?: React.ReactNode;

  /**
   * Tabs for the search dialog
   */
  tabs?: React.ReactNode[] | TabItem[] | undefined;

  withSearch?: boolean;

  /**
   * Whether to use date picker functionality
   */
  withDate?: boolean;

  /**
   * Action buttons for table type fields
   * Pass an array of action types to show specific buttons (e.g., ['add', 'delete', 'copy'])
   * Pass an empty array [] to hide all buttons
   */
  tableActionButtons?: ActionButtonType[];
}
