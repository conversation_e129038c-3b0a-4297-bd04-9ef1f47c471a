import { Button } from '@mui/material';
import { useState } from 'react';
import { AccountModel, DonVi, DoiTuong } from '@/types/schemas';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { searchSchema, SearchFormValues } from './schema';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';
import BasicInfoTab from './BasicInfoTab';
import DetailTab from './DetailTab';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (filters: any) => void;
}

const SearchDialog = ({ open, onClose, onSearch }: SearchDialogProps) => {
  const [customer, setCustomer] = useState<DoiTuong | undefined>();
  const [account, setAccount] = useState<AccountModel | undefined>();
  const [unit, setUnit] = useState<DonVi | undefined>();

  const handleSubmit = (data: SearchFormValues) => {
    const searchData = {
      ...data,
      customer: customer,
      account: account,
      unit: unit
    };
    onSearch(searchData);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Lọc bút toán điều chỉnh giảm công nợ'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button
            type='submit'
            form='search-form'
            variant='contained'
            sx={{
              borderRadius: 0,
              backgroundColor: '#3DA9A0'
            }}
          >
            <AritoIcon icon={884} marginX='4px' />
            Đồng ý
          </Button>
          <Button
            onClick={onClose}
            variant='outlined'
            sx={{
              borderRadius: 0,
              borderColor: '#d32f2f',
              color: '#d32f2f',
              '&:hover': {
                borderColor: '#c62828',
                backgroundColor: 'rgba(211, 47, 47, 0.04)'
              }
            }}
          >
            <AritoIcon icon={885} marginX='4px' />
            Huỷ
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab account={account} setAccount={setAccount} />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'detail',
                  label: 'Chi tiết',
                  component: (
                    <DetailTab
                      customer={customer}
                      account={account}
                      unit={unit}
                      setCustomer={setCustomer}
                      setAccount={setAccount}
                      setUnit={setUnit}
                    />
                  )
                }
              ]}
              className='border-b border-b-gray-200'
            />
          </div>
        }
      />
    </AritoDialog>
  );
};

export default SearchDialog;
