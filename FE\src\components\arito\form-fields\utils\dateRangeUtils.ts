// Define the date range options
export const dateRangeOptions = [
  { value: 'thisMonth', label: 'Tháng này' },
  { value: 'monthToDate', label: '<PERSON><PERSON>u tháng đến hiện tại' },
  { value: 'prevMonth', label: 'Tháng trước' },
  { value: 'nextMonth', label: 'Tháng sau' },
  { value: 'today', label: 'Hôm nay' },
  { value: 'thisWeek', label: 'Tuần này' },
  { value: 'month1', label: 'Tháng 1' },
  { value: 'month2', label: 'Tháng 2' },
  { value: 'month3', label: 'Tháng 3' },
  { value: 'month4', label: 'Tháng 4' },
  { value: 'month5', label: 'Tháng 5' },
  { value: 'month6', label: 'Tháng 6' },
  { value: 'month7', label: 'Tháng 7' },
  { value: 'month8', label: 'Tháng 8' },
  { value: 'month9', label: 'Tháng 9' },
  { value: 'month10', label: 'Tháng 10' },
  { value: 'month11', label: 'Tháng 11' },
  { value: 'month12', label: 'Tháng 12' },
  { value: 'quarter1', label: 'Quý 1' },
  { value: 'quarter2', label: 'Quý 2' },
  { value: 'quarter3', label: 'Quý 3' },
  { value: 'quarter4', label: 'Quý 4' },
  { value: 'thisQuarter', label: 'Quý này' },
  { value: 'prevQuarter', label: 'Quý trước' },
  { value: 'quarterToDate', label: 'Đầu quý đến hiện tại' },
  { value: 'thisYear', label: 'Năm nay' },
  { value: 'prevYear', label: 'Năm trước' },
  { value: 'nextYear', label: 'Năm sau' },
  { value: 'yearToDate', label: 'Đầu năm đến hiện tại' }
];

export type DateRangePreset = (typeof dateRangeOptions)[number]['value'];

export const calculateDateRange = (preset: DateRangePreset): [Date, Date] => {
  const today = new Date();
  let startDate = new Date();
  let endDate = new Date();

  switch (preset) {
    case 'today':
      break;
    case 'thisMonth':
      startDate.setDate(1);
      endDate.setMonth(today.getMonth() + 1, 0);
      break;
    case 'monthToDate':
      startDate.setDate(1);
      break;
    case 'prevMonth':
      startDate.setMonth(today.getMonth() - 1, 1);
      endDate.setMonth(today.getMonth(), 0);
      break;
    case 'nextMonth':
      startDate.setMonth(today.getMonth() + 1, 1);
      endDate.setMonth(today.getMonth() + 2, 0);
      break;
    case 'thisWeek':
      startDate.setDate(today.getDate() - today.getDay());
      endDate.setDate(today.getDate() + (6 - today.getDay()));
      break;
    case 'month1':
      startDate.setMonth(0, 1);
      endDate.setMonth(1, 0);
      break;
    case 'month2':
      startDate.setMonth(1, 1);
      endDate.setMonth(2, 0);
      break;
    case 'month3':
      startDate.setMonth(2, 1);
      endDate.setMonth(3, 0);
      break;
    case 'month4':
      startDate.setMonth(3, 1);
      endDate.setMonth(4, 0);
      break;
    case 'month5':
      startDate.setMonth(4, 1);
      endDate.setMonth(5, 0);
      break;
    case 'month6':
      startDate.setMonth(5, 1);
      endDate.setMonth(6, 0);
      break;
    case 'month7':
      startDate.setMonth(6, 1);
      endDate.setMonth(7, 0);
      break;
    case 'month8':
      startDate.setMonth(7, 1);
      endDate.setMonth(8, 0);
      break;
    case 'month9':
      startDate.setMonth(8, 1);
      endDate.setMonth(9, 0);
      break;
    case 'month10':
      startDate.setMonth(9, 1);
      endDate.setMonth(10, 0);
      break;
    case 'month11':
      startDate.setMonth(10, 1);
      endDate.setMonth(11, 0);
      break;
    case 'month12':
      startDate.setMonth(11, 1);
      endDate.setMonth(0, 0);
      endDate.setFullYear(endDate.getFullYear() + 1);
      break;
    case 'quarter1':
      startDate.setMonth(0, 1);
      endDate.setMonth(3, 0);
      break;
    case 'quarter2':
      startDate.setMonth(3, 1);
      endDate.setMonth(6, 0);
      break;
    case 'quarter3':
      startDate.setMonth(6, 1);
      endDate.setMonth(9, 0);
      break;
    case 'quarter4':
      startDate.setMonth(9, 1);
      endDate.setMonth(12, 0);
      break;
    case 'thisQuarter':
      const currentQuarter = Math.floor(today.getMonth() / 3);
      startDate.setMonth(currentQuarter * 3, 1);
      endDate.setMonth(currentQuarter * 3 + 3, 0);
      break;
    case 'prevQuarter':
      const prevQuarter = Math.floor(today.getMonth() / 3) - 1;
      if (prevQuarter < 0) {
        startDate.setFullYear(today.getFullYear() - 1);
        startDate.setMonth(9, 1);
        endDate.setFullYear(today.getFullYear() - 1);
        endDate.setMonth(12, 0);
      } else {
        startDate.setMonth(prevQuarter * 3, 1);
        endDate.setMonth(prevQuarter * 3 + 3, 0);
      }
      break;
    case 'quarterToDate':
      const qtdQuarter = Math.floor(today.getMonth() / 3);
      startDate.setMonth(qtdQuarter * 3, 1);
      break;
    case 'thisYear':
      startDate.setMonth(0, 1);
      endDate.setMonth(12, 0);
      break;
    case 'prevYear':
      startDate.setFullYear(today.getFullYear() - 1, 0, 1);
      endDate.setFullYear(today.getFullYear() - 1, 11, 31);
      break;
    case 'nextYear':
      startDate.setFullYear(today.getFullYear() + 1, 0, 1);
      endDate.setFullYear(today.getFullYear() + 1, 11, 31);
      break;
    case 'yearToDate':
      startDate.setMonth(0, 1);
      break;
  }

  return [startDate, endDate];
};

export const formatDate = (date: Date) => {
  return date.toISOString().split('T')[0];
};

export default {
  dateRangeOptions,
  calculateDateRange,
  formatDate
};
