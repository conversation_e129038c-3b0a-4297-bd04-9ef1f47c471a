# Arito Popup Form Field Component

A versatile form field component that supports multiple input types with search functionality.

## Directory Structure

```
arito-popup-form-field/
├── README.md
├── index.tsx                # Main component entry point
├── components/             
│   ├── SearchDialog.tsx    # Search popup dialog
│   ├── CheckboxField.tsx   # Checkbox input type
│   ├── SelectField.tsx     # Select/dropdown input
│   ├── DateField.tsx       # Date picker input
│   ├── TextField.tsx       # Text/number input
│   └── MultiSelectField.tsx # Multi-select with chips
├── hooks/
│   ├── useSearchResults.ts # Search functionality hook
│   └── useDebounce.ts     # Debounce hook for search
└── styles/
    ├── constants.ts        # Shared style constants
    └── fieldStyles.ts      # Common field styles
```

## Components

### Main Component (index.tsx)
- Routes to appropriate field component based on type prop
- Handles form context and common field props
- Manages field errors and validation

### SearchDialog.tsx
- Handles search popup UI and logic
- Manages search results and selection
- Supports single and multi-select modes
- Provides fullscreen toggle functionality

### Field Components
Each field component handles specific input type rendering and logic:

- **CheckboxField**: Checkbox input with label
- **SelectField**: Dropdown selection with options
- **DateField**: Date picker with localization
- **TextField**: Basic text/number input
- **MultiSelectField**: Multi-select with chip display

## Hooks

### useSearchResults.ts
```typescript
const useSearchResults = (searchEndpoint: string) => {
  // Manages search API calls and results
  // Handles loading states and errors
  // Returns search methods and state
}
```

### useDebounce.ts
```typescript
const useDebounce = <T>(value: T, delay: number): T => {
  // Debounces rapid value changes
  // Useful for search input performance
}
```

## Props Interface

```typescript
interface PopupFormFieldProps {
  label: string;                 // Field label
  name: string;                  // Field name for form
  type?: FormFieldType;         // Input type
  disabled?: boolean;           // Disable field
  options?: SelectOption[];     // Options for select/multiselect
  error?: string;               // Error message
  withSearch?: boolean;         // Enable search
  withMultiSearch?: boolean;    // Enable multi-select search
  withDate?: boolean;           // Enable date picker
  className?: string;           // Additional CSS classes
  columns?: any[];             // Table columns
  searchEndpoint?: string;      // Search API endpoint
  searchResultLabelKey?: string;// Result label key
  searchResultValueKey?: string;// Result value key
  searchColumns?: ExtendedGridColDef[]; // Search table columns
  defaultSearchColumn?: string; // Default search column
  displayRelatedField?: string; // Related field to display
  labelWidth?: string;         // Label width
  inputWidth?: string;         // Input width
}
```

## Usage Example

```tsx
<PopupFormField
  label="Customer"
  name="customer_id"
  type="text"
  withSearch={true}
  searchEndpoint="/api/customers"
  searchResultLabelKey="name"
  searchResultValueKey="id"
  displayRelatedField="email"
/>
```

## Style Constants

Common styles are centralized in `styles/constants.ts`:

```typescript
export const fieldStyles = {
  input: {
    fontSize: "14px",
    borderBottom: "1px solid #e5e7eb",
    // ... other common styles
  },
  // ... other style constants
}
```

## Implementation Notes

1. All components maintain existing functionality
2. Style consistency across all field types
3. Clear separation of concerns
4. Improved code maintainability
5. Enhanced testing capabilities

## Dependencies

- @mui/material: UI components
- @mui/x-date-pickers: Date picker
- react-hook-form: Form handling
- dayjs: Date manipulation
