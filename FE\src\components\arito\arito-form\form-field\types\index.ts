import { GridColDef } from '@mui/x-data-grid';
import { Control } from 'react-hook-form';

// Main component props
export interface FormFieldProps {
  label?: string;
  name: string;
  control?: Control<any>;
  type?: 'text' | 'number' | 'select' | 'checkbox' | 'date' | 'table';
  disabled?: boolean;
  options?: { value: any; label: string }[];
  error?: string;
  withSearch?: boolean;
  withDate?: boolean;
  className?: string;
  columns?: GridColDef[];
  searchEndpoint?: string;
  searchResultLabelKey?: string;
  searchResultValueKey?: string;
  searchColumns?: GridColDef[];
  defaultSearchColumn?: string;
  displayRelatedField?: string;
  labelWidth?: string;
  inputWidth?: string;
}

// Search field specific props
export interface SearchFieldProps {
  field: any;
  fieldId: string;
  disabled?: boolean;
  type: 'text' | 'number';
  displayText: string;
  relatedFieldValue: string;
  onSearchClick: () => void;
  onInputChange: (value: string) => void;
}

// Search dialog props
export interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  isFullScreen: boolean;
  isMobile: boolean;
  title: string;
  searchQuery: string;
  onSearchQueryChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isLoading: boolean;
  searchResults: any[];
  selectedResult: any;
  searchColumns?: GridColDef[];
  searchResultLabelKey?: string;
  searchResultValueKey?: string;
  onResultClick: (result: any) => void;
  onResultDoubleClick: (result: any) => void;
  onConfirm: () => void;
}

// Related field display props
export interface RelatedFieldDisplayProps {
  value: string;
  isMobile: boolean;
}

// Standard field props with conditional options type
export interface StandardFieldProps {
  field: any;
  fieldId: string;
  type: FormFieldProps['type'];
  disabled?: boolean;
  options?: any; // Using any for now since it can be either select options or table columns
  isMobile: boolean;
  label?: string;
  columns?: GridColDef[];
}

// Hook types
export interface UseSearchProps {
  searchEndpoint?: string;
  searchResultValueKey?: string;
  searchResultLabelKey?: string;
  defaultSearchColumn?: string;
  displayRelatedField?: string;
  searchColumns?: GridColDef[];
  initialValue?: any;
  control: Control<any>;
  name: string;
}

export interface UseSearchResult {
  searchDialogOpen: boolean;
  setSearchDialogOpen: (open: boolean) => void;
  searchResults: any[];
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  isLoading: boolean;
  selectedSearchResult: any;
  setSelectedSearchResult: (result: any) => void;
  displayText: string;
  setDisplayText: (text: string) => void;
  relatedFieldValue: string;
  setRelatedFieldValue: (value: string) => void;
  fetchSearchResults: (query?: string) => Promise<void>;
  handleSelectSearchResult: (result: any, onChange: (value: any) => void) => void;
}
