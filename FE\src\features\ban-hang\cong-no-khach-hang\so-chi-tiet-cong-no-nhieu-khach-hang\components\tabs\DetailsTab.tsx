import { customerSearchColumns, groupColumns, regionColumns } from '@/constants/search-columns';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

interface DetailsTabProps {
  searchFieldStates: any;
}

const DetailsTab: React.FC<DetailsTabProps> = ({ searchFieldStates }) => {
  return (
    <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        {/* Mã khách hàng */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã khách hàng:</Label>
          <div className='flex-1'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
              searchColumns={customerSearchColumns}
              columnDisplay='customer_code'
              displayRelatedField='customer_name'
              dialogTitle='Danh mục khách hàng'
              onRowSelection={row => {
                searchFieldStates.setCustomer(row);
              }}
            />
          </div>
        </div>
        <div className='flex items-center'>
          <FormField label='Tính số dư' name='so_du' className='w-[400px]' labelClassName='w-40' type='checkbox' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm khách hàng:</Label>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <SearchField
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=KH1`}
                searchColumns={groupColumns}
                columnDisplay='ma_nhom'
                displayRelatedField='ten_phan_nhom'
                dialogTitle='Danh mục nhóm khách hàng 1'
                onRowSelection={row => {
                  searchFieldStates.setCustomerGroup1(row);
                }}
              />
              <SearchField
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=KH2`}
                searchColumns={groupColumns}
                columnDisplay='ma_nhom'
                displayRelatedField='ten_phan_nhom'
                dialogTitle='Danh mục nhóm khách hàng 2'
                onRowSelection={row => {
                  searchFieldStates.setCustomerGroup2(row);
                }}
              />
              <SearchField
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=KH3`}
                searchColumns={groupColumns}
                columnDisplay='ma_nhom'
                displayRelatedField='ten_phan_nhom'
                dialogTitle='Danh mục nhóm khách hàng 3'
                onRowSelection={row => {
                  searchFieldStates.setCustomerGroup3(row);
                }}
              />
            </div>
          </div>
        </div>
        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Khu vực:</Label>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHU_VUC}`}
              searchColumns={regionColumns}
              columnDisplay='rg_code'
              displayRelatedField='rgname'
              dialogTitle='Danh mục khu vực'
              onRowSelection={row => {
                searchFieldStates.setRegion(row);
              }}
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Người lập:</Label>
          <div className='flex-1'>
            <FormField name='nguoi_lap' label='' type='text' className='w-96' />
          </div>
        </div>
        <div className='flex items-center'>
          <FormField
            name='mau_bc'
            label='Mẫu báo cáo'
            labelClassName='w-40'
            type='select'
            options={[
              { value: 20, label: 'Mẫu tiền chuẩn' },
              { value: 21, label: 'Mẫu ngoại tệ' }
            ]}
            className='w-[400px]'
          />
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
