import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns } from '@/constants/search-columns';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

interface BasicInfoProps {
  searchFieldStates: any;
}

const BasicInfo: React.FC<BasicInfoProps> = ({ searchFieldStates }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày hạch toán từ/đến:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='ngay_ct1' toDateName='ngay_ct2' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            onRowSelection={row => {
              searchFieldStates.setAccount(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>
        <div className='flex items-center'>
          <FormField
            name='ct_vt'
            className='w-[400px]'
            label='Chi tiết theo vật tư'
            labelClassName='w-40'
            type='checkbox'
          />
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
