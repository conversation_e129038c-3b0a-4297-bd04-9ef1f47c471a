import React from 'react';
import { useAnalysisItems, useItemSelection, useItemMovement, AnalysisItem } from './hooks';
import AritoIcon from '@/components/custom/arito/icon';

export default function AritoCreateAnalysisSample({ columnAnalysisItems }: { columnAnalysisItems: AnalysisItem[] }) {
  // Use custom hooks to manage state and logic
  const { columnAnalysis, setColumnAnalysis, columnData, setColumnData, rowData, setRowData, values, setValues } =
    useAnalysisItems(columnAnalysisItems);

  const {
    selectedColumnAnalysis,
    setSelectedColumnAnalysis,
    selectedColumnData,
    setSelectedColumnData,
    selectedRowData,
    setSelectedRowData,
    selectedValues,
    setSelectedValues,
    toggleSelection
  } = useItemSelection();

  const { moveItems, moveRowDataDown } = useItemMovement();

  // Create a wrapper for moveRowDataDown to pass the required state
  const handleMoveRowDataDown = () => {
    moveRowDataDown(selectedRowData, setSelectedRowData, rowData, setRowData, setValues, setColumnData);
  };

  // Wrapper for moveItems to handle column analysis to row data
  const handleMoveColumnToRow = () => {
    moveItems(
      columnAnalysis,
      setColumnAnalysis,
      selectedColumnAnalysis,
      setSelectedColumnAnalysis,
      rowData,
      setRowData,
      'any'
    );
  };

  // Wrapper for moveItems to handle row data to column analysis
  const handleMoveRowToColumn = () => {
    moveItems(rowData, setRowData, selectedRowData, setSelectedRowData, columnAnalysis, setColumnAnalysis, 'any');
  };

  // Wrapper for moveItems to handle moving data up
  const handleMoveDataUp = () => {
    // Move column data to row data
    moveItems(columnData, setColumnData, selectedColumnData, setSelectedColumnData, rowData, setRowData, 'any');

    // Move values to row data
    moveItems(values, setValues, selectedValues, setSelectedValues, rowData, setRowData, 'any');
  };

  return (
    <div className='flex w-full bg-gray-50 p-4'>
      <div className='mr-4 w-1/3 rounded border'>
        <div className='flex items-center border-b bg-gray-200 p-2'>
          <AritoIcon icon={874} />
          <h2 className='ml-2 font-medium text-gray-800'>Cột phân tích</h2>
        </div>
        <div className='h-96 overflow-y-auto'>
          {columnAnalysis.map((item, index) => (
            <div
              key={index}
              className={`cursor-pointer border-b p-2 ${selectedColumnAnalysis.includes(item) ? 'bg-yellow-100' : 'hover:bg-gray-100'}`}
              onClick={() => toggleSelection(item, columnAnalysis, setSelectedColumnAnalysis)}
            >
              {item.label.toString()}
            </div>
          ))}
        </div>
      </div>

      <div className='flex w-2/3 flex-col'>
        <div className='mb-4 flex h-1/2'>
          <div className='mr-4 flex w-1/2 flex-col items-center justify-center rounded border'>
            <div className='flex flex-col justify-center gap-2'>
              <button className='rounded-md border p-3' onClick={handleMoveColumnToRow}>
                <AritoIcon icon={880} />
              </button>

              <button className='rounded-md border p-3' onClick={handleMoveRowToColumn}>
                <AritoIcon icon={878} />
              </button>

              <button className='rounded-md border p-3' onClick={handleMoveDataUp}>
                <AritoIcon icon={881} />
              </button>

              <button className='rounded-md border p-3' onClick={handleMoveRowDataDown}>
                <AritoIcon icon={879} />
              </button>
            </div>
          </div>

          <div className='w-1/2 rounded border'>
            <div className='flex items-center border-b bg-gray-200 p-2'>
              <AritoIcon icon={872} />
              <h2 className='ml-2 font-medium text-gray-800'>Dữ liệu dòng</h2>
            </div>
            <div className='h-40 overflow-y-auto'>
              {rowData.map((item, index) => (
                <div
                  key={index}
                  className={`cursor-pointer border-b p-2 ${selectedRowData.includes(item) ? 'bg-yellow-100' : 'hover:bg-gray-100'}`}
                  onClick={() => toggleSelection(item, rowData, setSelectedRowData)}
                >
                  {item.label.toString()}
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className='flex h-1/2'>
          <div className='mr-4 w-1/2 rounded border'>
            <div className='flex items-center border-b bg-gray-200 p-2'>
              <AritoIcon icon={877} />
              <h2 className='ml-2 font-medium text-gray-800'>Dữ liệu cột</h2>
            </div>
            <div className='h-40 overflow-y-auto'>
              {columnData.map((item, index) => (
                <div
                  key={index}
                  className={`cursor-pointer border-b p-2 ${selectedColumnData.includes(item) ? 'bg-yellow-100' : 'hover:bg-gray-100'}`}
                  onClick={() => toggleSelection(item, columnData, setSelectedColumnData)}
                >
                  {item.label.toString()}
                </div>
              ))}
            </div>
          </div>

          <div className='w-1/2 rounded border'>
            <div className='flex items-center border-b bg-gray-200 p-2'>
              <AritoIcon icon={873} />
              <h2 className='ml-2 font-medium text-gray-800'>Giá trị</h2>
            </div>
            <div className='h-40 overflow-y-auto'>
              {values.map((item, index) => (
                <div
                  key={index}
                  className={`cursor-pointer border-b p-2 ${selectedValues.includes(item) ? 'bg-yellow-100' : 'hover:bg-gray-100'}`}
                  onClick={() => toggleSelection(item, values, setSelectedValues)}
                >
                  {item.label.toString()}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
