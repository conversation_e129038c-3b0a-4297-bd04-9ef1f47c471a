import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { TKNoSearchColBasicInfo } from './cols-definition';
import AritoIcon from '@/components/custom/arito/icon';
import { Type2Tabs } from './Tabs';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTabType2 = ({ formMode }: Props) => {
  return (
    <div className='space-y-4 p-4'>
      <FormField
        className='flex max-w-[300px] items-center justify-between'
        label='Tài khoản'
        name='account'
        type='text'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='flex items-center justify-between'
        label='Tên tài khoản'
        name='accountName'
        type='text'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='flex items-center justify-between'
        label='Tên tiếng anh'
        name='englishName'
        type='text'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='flex max-w-[300px] items-center justify-between'
        label='Tài khoản mẹ'
        name='parentAccount'
        type='text'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
        withSearch={true}
        searchEndpoint='/financial-account/get-parents'
        searchColumns={TKNoSearchColBasicInfo}
        actionButtons={['add', 'edit']}
        headerFields={<BasicInfoTabType2 formMode={formMode} />}
        tabs={Type2Tabs({ formMode })}
      />
      <FormField
        className='flex max-w-[300px] items-center justify-between'
        label='Tk đại diện khác'
        name='otherAccount'
        type='text'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
      />
    </div>
  );
};

export default BasicInfoTabType2;
