import { ControllerRenderProps, FieldValues } from 'react-hook-form';
import { Box, Button, TextField } from '@mui/material';
import { initial } from 'lodash';
import React from 'react';
import {
  searchIconButtonStyle,
  searchInputContainerStyle,
  searchResultsContainerStyle,
  confirmButtonStyle,
  cancelButtonStyle,
  searchInputFieldStyle
} from '../styles';
import AddEditActionFormButtons from '../hooks/AddEditActionFormButtons';
import { SearchTable } from '@/components/custom/arito/search-table';
import { ActionButtonFormType } from '../hooks/ActionButtonFormMap';
import { AritoDialog } from '@/components/custom/arito/dialog';
import AritoIcon from '@/components/custom/arito/icon';
import { TabItem } from '@/components/custom/arito';
import { SearchIcon } from '@/components/icons';
import { useForm } from '../hooks/useForm';
import { ModalForm } from './ModalForm';
import { cn } from '@/lib/utils';

interface SearchFieldProps {
  field: ControllerRenderProps<FieldValues, string>;
  label: string | undefined;
  disabled?: boolean;
  fieldId: string;
  type: string;
  searchEndpoint?: string;
  searchColumns?: any[];
  searchResultLabelKey?: string;
  searchResultValueKey?: string;
  defaultSearchColumn?: string;
  displayRelatedField: boolean;
  relatedFieldValue: string;
  setRelatedFieldValue: (value: string) => void;
  textFieldInputStyle: (type: string) => any;
  className?: string;
  dialogTitle?: string;
  hideRowNumberColumn?: boolean;
  actionButtons?: ActionButtonFormType[];
  headerFields?: React.ReactNode;
  tabs?: React.ReactNode[] | TabItem[] | undefined;
  // Additional props from the parent component
  searchDialogOpen: boolean;
  setSearchDialogOpen: (open: boolean) => void;
  searchQuery: string;
  searchResults: any[];
  isLoading: boolean;
  selectedSearchResult: any;
  setSelectedSearchResult: (result: any) => void;
  displayText: string;
  setDisplayText: (text: string) => void;
  isFullScreen: boolean;
  handleSearchClick: () => void;
  handleSearchQueryChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSelectSearchResult: (result: any, onChange: any) => void;
  handleConfirmSelection: (onChange: any) => void;
  toggleFullScreen: () => void;
  placeholder?: string;
}

export const SearchField: React.FC<SearchFieldProps> = ({
  field,
  label,
  disabled,
  fieldId,
  type,
  searchEndpoint,
  searchColumns,
  searchResultLabelKey,
  searchResultValueKey,
  defaultSearchColumn,
  displayRelatedField,
  relatedFieldValue,
  setRelatedFieldValue,
  textFieldInputStyle,
  className,
  dialogTitle,
  hideRowNumberColumn,
  actionButtons,
  headerFields,
  tabs,
  // Additional props from the parent component
  searchDialogOpen,
  setSearchDialogOpen,
  searchQuery,
  searchResults,
  isLoading,
  selectedSearchResult,
  setSelectedSearchResult,
  displayText,
  setDisplayText,
  isFullScreen,
  handleSearchClick,
  handleSearchQueryChange,
  handleSelectSearchResult,
  handleConfirmSelection,
  toggleFullScreen,
  placeholder
}) => {
  const { initialData, defaultMode } = {
    initialData: {},
    defaultMode: 'add'
  };
  const { showForm, formMode, currentObj, handleCloseForm, handleOpenAddForm, handleOpenEditForm, handleOpenViewForm } =
    useForm(initialData, defaultMode as 'add' | 'edit' | 'view');
  return (
    <div className={cn('relative flex w-32 flex-shrink-0 items-center', className)}>
      <TextField
        name={field.name}
        ref={field.ref}
        onBlur={field.onBlur}
        type={type}
        disabled={disabled}
        fullWidth
        size='small'
        variant='standard'
        id={fieldId}
        value={displayText || field.value || ''}
        onChange={e => {
          const newValue = e.target.value;
          field.onChange(newValue);
          setDisplayText(newValue);
          setRelatedFieldValue('');
        }}
        placeholder={placeholder}
        sx={{
          ...textFieldInputStyle(type),
          '& .MuiInputBase-input::placeholder': {
            opacity: 1,
            color: 'rgba(0, 0, 0, 0.6)'
          }
        }}
        InputProps={{
          endAdornment: !disabled ? (
            <Box
              sx={searchIconButtonStyle}
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                handleSearchClick();
              }}
            >
              <SearchIcon size={16} color='#2563EB' />
            </Box>
          ) : null
        }}
      />

      {relatedFieldValue && (
        <div className='ml-0 mt-1 max-w-full flex-grow overflow-hidden text-ellipsis whitespace-nowrap text-left text-sm text-gray-700 sm:ml-3 sm:mt-0 sm:max-w-[65%]'>
          {relatedFieldValue}
        </div>
      )}

      <AritoDialog
        open={searchDialogOpen}
        onClose={() => {}}
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        fullScreen={isFullScreen}
        onFullscreenToggle={toggleFullScreen}
        title={dialogTitle || `Danh mục ${String(label).toLowerCase()}`}
        titleIcon={<AritoIcon icon={584} className='mx-2' />}
        actions={
          <>
            <Button
              onClick={() => handleConfirmSelection(field.onChange)}
              variant='contained'
              disabled={!selectedSearchResult}
              sx={confirmButtonStyle}
            >
              <AritoIcon icon={884} className='mx-1' />
              Đồng ý
            </Button>
            <Button onClick={() => setSearchDialogOpen(false)} variant='outlined' sx={cancelButtonStyle}>
              <AritoIcon icon={885} className='mx-1' />
              Hủy
            </Button>
          </>
        }
      >
        {!searchEndpoint && (
          <div className={'px-3 py-2 text-red-500'}>
            Vui lòng cung cấp searchEndpoint để sử dụng chức năng tìm kiếm.
          </div>
        )}

        {searchEndpoint && (
          <>
            <Box sx={searchInputContainerStyle}>
              <TextField
                variant='outlined'
                placeholder='Tìm kiếm...'
                size='small'
                value={searchQuery}
                onChange={handleSearchQueryChange}
                sx={searchInputFieldStyle}
                InputProps={{
                  startAdornment: <SearchIcon size={14} color='#2563EB' />
                }}
              />
              {actionButtons && (
                <AddEditActionFormButtons
                  onAddForm={handleOpenAddForm}
                  onEditForm={() => handleOpenEditForm(selectedSearchResult)}
                  errorMessage={''}
                  tableActionButtonsForm={actionButtons}
                />
              )}
            </Box>

            <SearchTable
              searchResults={searchResults}
              searchColumns={searchColumns}
              searchResultLabelKey={searchResultLabelKey}
              searchResultValueKey={searchResultValueKey}
              selectedResult={selectedSearchResult}
              onResultClick={result => {
                setSelectedSearchResult(result);
              }}
              onResultDoubleClick={result => {
                handleSelectSearchResult(result, field.onChange);
              }}
            />
          </>
        )}
        {showForm && (
          <ModalForm
            title={dialogTitle || `Danh mục ${String(label).toLowerCase()}`}
            headerFields={headerFields || <></>}
            tabs={tabs || []}
            open={showForm}
            onClose={handleCloseForm}
            formMode={formMode}
            initialData={currentObj || {}}
            handleSubmit={() => {}}
          />
        )}
      </AritoDialog>
    </div>
  );
};
