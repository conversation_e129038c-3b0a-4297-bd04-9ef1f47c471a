import { Dialog, DialogContent, IconButton, Typography, Box, Divider, useMediaQuery, useTheme } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import React from 'react';
import AritoIcon from '@/components/custom/arito/icon';

interface AritoConfirmModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
}

const AritoConfirmModal: React.FC<AritoConfirmModalProps> = ({
  open,
  onClose,
  onConfirm,
  title = 'Cảnh báo',
  message,
  confirmText = 'Đồng ý',
  cancelText = 'Hủy'
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Dialog
      open={open}
      onClose={(event, reason) => {
        if (reason !== 'backdropClick') {
          onClose();
        }
      }}
      maxWidth='xs'
      fullWidth={isMobile}
      BackdropProps={{
        style: { backgroundColor: 'transparent' }
      }}
      PaperProps={{
        style: {
          backgroundColor: '#f8fcfd',
          overflow: 'hidden',
          borderRadius: 0,
          minWidth: isMobile ? '100%' : '600px'
        }
      }}
    >
      <Box
        sx={{
          backgroundColor: '#f0f7fc',
          padding: 0,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid #e0e0e0',
          height: '36px'
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            marginLeft: '8px'
          }}
        >
          <AritoIcon icon={260} marginX='4px' />
          <Typography color='#444' variant='body2'>
            {title}
          </Typography>
        </Box>
        <IconButton
          sx={{
            color: 'black',
            paddingX: 2.5,
            borderRadius: 0,
            height: '100%',
            '&:hover': {
              backgroundColor: '#d9e6ed'
            }
          }}
          size='small'
          onClick={onClose}
        >
          <CloseIcon sx={{ fontSize: '12px' }} />
        </IconButton>
      </Box>

      <DialogContent sx={{ padding: '0px' }}>
        <Typography fontSize='12px' variant='body2' sx={{ marginBottom: '8px', padding: '10px 20px' }}>
          {message}
        </Typography>

        <Divider />

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '8px',
            padding: '6px'
          }}
        >
          <button
            onClick={onConfirm}
            style={{
              backgroundColor: '#53a3a3',
              color: 'white',
              border: 'none',
              padding: '6px 6px',
              paddingRight: '12px',
              cursor: 'default',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '4px',
              fontSize: '14px',
              width: '100px',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={e => (e.currentTarget.style.backgroundColor = '#438585')}
            onMouseOut={e => (e.currentTarget.style.backgroundColor = '#53a3a3')}
          >
            <AritoIcon icon={884} marginX='4px' />
            {confirmText}
          </button>

          <button
            onClick={onClose}
            style={{
              backgroundColor: 'white',
              color: '#53a3a3',
              border: '1px solid #53a3a3',
              padding: '6px 6px',
              paddingRight: '12px',
              cursor: 'default',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '4px',
              fontSize: '14px',
              width: '100px',
              transition: 'all 0.2s'
            }}
            onMouseOver={e => {
              e.currentTarget.style.backgroundColor = '#53a3a3';
              e.currentTarget.style.color = 'white';
            }}
            onMouseOut={e => {
              e.currentTarget.style.backgroundColor = 'white';
              e.currentTarget.style.color = '#53a3a3';
            }}
          >
            <AritoIcon icon={885} marginX='4px' />
            {cancelText}
          </button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default AritoConfirmModal;
