import { TextField as MuiText<PERSON>ield } from '@mui/material';
import { Controller } from 'react-hook-form';
import React from 'react';
import { fieldStyles } from '../styles/constants';

interface TextFieldProps {
  name: string;
  control: any;
  type?: 'text' | 'number';
  disabled?: boolean;
  isViewMode?: boolean;
  fieldId: string;
  value?: string | number;
  onChange?: (value: string) => void;
  displayText?: string;
  onDisplayTextChange?: (text: string) => void;
}

export const TextFieldComponent: React.FC<TextFieldProps> = ({
  name,
  control,
  type = 'text',
  disabled = false,
  isViewMode = false,
  fieldId,
  value,
  onChange,
  displayText,
  onDisplayTextChange
}) => {
  return (
    <Controller
      name={name}
      control={control}
      defaultValue=''
      render={({ field }) => (
        <MuiTextField
          {...field}
          type={type}
          disabled={disabled || isViewMode}
          fullWidth
          size='small'
          variant='standard'
          id={fieldId}
          value={displayText || field.value || ''}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            const newValue = e.target.value;
            field.onChange(newValue);
            onChange?.(newValue);
            onDisplayTextChange?.(newValue);
          }}
          sx={{
            '& .MuiInput-root': {
              fontSize: '14px',
              '&:before': {
                borderBottom: '1px solid #e5e7eb'
              },
              '&:hover:not(.Mui-disabled):before': {
                borderBottom: '1px solid #2563EB'
              },
              '&.Mui-focused:after': {
                borderBottom: '1px solid #2563EB'
              }
            },
            '& .MuiInput-input': {
              padding: '4px 8px'
            },
            '& .MuiInputAdornment-root': {
              marginRight: '0'
            },
            marginTop: type === 'number' ? '8px' : '0px'
          }}
        />
      )}
    />
  );
};
