{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CStudyWork%5CERP%5CFE%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-no-ssr.js&modules=D%3A%5CStudyWork%5CERP%5CFE%5Csrc%5Capp%5Cclient-providers.tsx&modules=D%3A%5CStudyWork%5CERP%5CFE%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CStudyWork%5CERP%5CFE%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CStudyWork%5CERP%5CFE%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Noto_Sans%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22subsets%22%3A%5B%22latin%22%2C%22vietnamese%22%5D%2C%22variable%22%3A%22--font-noto-sans%22%7D%5D%2C%22variableName%22%3A%22notoSans%22%7D&modules=D%3A%5CStudyWork%5CERP%5CFE%5Csrc%5Ccomponents%5Carito%5Carito-app-bar.tsx&modules=D%3A%5CStudyWork%5CERP%5CFE%5Csrc%5Ccomponents%5Cui%5Ctoaster.tsx&server=false!"]}