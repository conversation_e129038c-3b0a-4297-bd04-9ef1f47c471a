import { useState, useEffect } from 'react';
import { UseSearchProps, UseSearchResult } from '../types';

export const useSearch = ({
  searchEndpoint,
  searchResultValueKey,
  searchResultLabelKey,
  defaultSearchColumn,
  displayRelatedField,
  searchColumns,
  control,
  name
}: UseSearchProps): UseSearchResult => {
  const [searchDialogOpen, setSearchDialogOpen] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSearchResult, setSelectedSearchResult] = useState<any>(null);
  const [displayText, setDisplayText] = useState<string>('');
  const [relatedFieldValue, setRelatedFieldValue] = useState<string>('');

  const fetchSearchResults = async (query = '') => {
    if (!searchEndpoint) return;

    setIsLoading(true);
    try {
      // Placeholder implementation - ERPNext functionality removed
      setSearchResults([]);
    } catch (error) {
      console.error('Error fetching search results:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectSearchResult = (result: any, onChange: (value: any) => void) => {
    const valueKey = searchResultValueKey || defaultSearchColumn || 'name';
    const labelKey = searchResultLabelKey || defaultSearchColumn || 'name';

    const actualValue = result[valueKey];

    if (actualValue === undefined) {
      const objectKeys = Object.keys(result);
      const fallbackValue = result.name || result.id || (objectKeys.length > 0 ? result[objectKeys[0]] : undefined);

      onChange(fallbackValue);
      setDisplayText(result[labelKey] || String(fallbackValue));
    } else {
      onChange(actualValue);
      setDisplayText(result[labelKey] !== undefined ? result[labelKey] : String(actualValue));
    }

    if (displayRelatedField && result[displayRelatedField]) {
      setRelatedFieldValue(result[displayRelatedField]);
    } else {
      setRelatedFieldValue('');
    }

    setSearchDialogOpen(false);
  };

  // Initialize display text from form value
  useEffect(() => {
    const getInitialDisplayText = async () => {
      if (!searchEndpoint) return;

      const valueKey = searchResultValueKey || defaultSearchColumn || 'name';
      const labelKey = searchResultLabelKey || defaultSearchColumn || 'name';
      const initialFormValues = control._formValues;
      const initialValue = initialFormValues[name];

      if (!initialValue) return;

      try {
        if (searchResults.length > 0) {
          const matchingResult = searchResults.find(result => result[valueKey] === initialValue);

          if (matchingResult) {
            setDisplayText(matchingResult[labelKey]);
            if (displayRelatedField && matchingResult[displayRelatedField]) {
              setRelatedFieldValue(matchingResult[displayRelatedField]);
            }
            return;
          }
        }

        // Placeholder implementation - ERPNext functionality removed
      } catch (error) {
        console.error('Error fetching initial display text:', error);
      }
    };

    getInitialDisplayText();
  }, [
    control._formValues,
    name,
    searchEndpoint,
    searchResultLabelKey,
    searchResultValueKey,
    searchResults,
    defaultSearchColumn,
    displayRelatedField
  ]);

  return {
    searchDialogOpen,
    setSearchDialogOpen,
    searchResults,
    searchQuery,
    setSearchQuery,
    isLoading,
    selectedSearchResult,
    setSelectedSearchResult,
    displayText,
    setDisplayText,
    relatedFieldValue,
    setRelatedFieldValue,
    fetchSearchResults,
    handleSelectSearchResult
  };
};
