import { Suspense } from 'react';
import PhanBoTienHangTraChoCacHoaDonPage from '@/features/mua-hang/phan-bo-tien-hang-tra-cho-cac-hoa-don/page';
import AritoLoading from '@/components/custom/arito/loading';
import { AritoError } from '@/components/arito/arito-error';

export default async function SalesInvoicesPage() {
  const [error, invoices] = [null, []];

  // if (error) {
  //   return <AritoError />;
  // }

  return (
    <Suspense fallback={<AritoLoading />}>
      <PhanBoTienHangTraChoCacHoaDonPage initialRows={invoices} />
    </Suspense>
  );
}
