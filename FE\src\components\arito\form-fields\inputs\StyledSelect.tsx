import { styled } from '@mui/material/styles';
import { StyledComponentProps } from '../types';

// Select with only bottom border
export const StyledSelect = styled('select')<StyledComponentProps>(({ theme, disabled }) => ({
  border: 'none',
  borderBottom: disabled ? 'none' : '1px solid #ddd',
  padding: '2px 2px',
  fontSize: disabled ? '13px' : '12px',
  fontWeight: disabled ? 600 : 400,
  backgroundColor: 'transparent',
  width: '100%',
  height: '22px',
  appearance: disabled ? 'none' : 'auto',
  overflow: 'visible',
  textOverflow: 'clip',
  whiteSpace: 'normal',
  '&:focus': {
    outline: 'none',
    borderBottomColor: '#0b87c9',
    borderBottomWidth: '1px'
  },
  '&:disabled': {
    backgroundColor: 'transparent',
    cursor: 'default',
    color: 'black',
    opacity: 1
  }
}));

export default StyledSelect;
