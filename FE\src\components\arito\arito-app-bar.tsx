'use client';

import { useRouter } from 'next/navigation'; // Import useRouter
import { useState } from 'react';
import { CompanySelector, HeaderIcons, Logo, ProfileButton } from './arito-header-ui';
import { clearAuthLocalStorage } from '@/features/xac-thuc/actions/log-out'; // Import the correct function
import { AritoNavMenu, NavMenu } from './arito-nav-menu';
import { MobileNavMenu } from './arito-mobile-menu';
import { ProfileMenu } from './arito-profile-menu';
import { Separator } from '../ui/separator';
import { menus } from '@/constants';

export function AritoAppBar() {
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [expandedMenu, setExpandedMenu] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const router = useRouter(); // Initialize router

  const handleProfileClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
    setIsProfileOpen(true);
  };

  const handleProfileClose = () => {
    setAnchorEl(null);
    setIsProfileOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Client-side logout handler
  const handleLogout = () => {
    handleProfileClose(); // Close the menu first
    clearAuthLocalStorage();
    router.push('/xac-thuc');
  };

  // Mobile menu content
  const mobileMenuContent = (
    <div className='fixed bottom-0 left-0 right-0 top-[49px] z-50 flex flex-col bg-teal-600'>
      <div className='flex-1 overflow-y-auto'>
        {menus.map((menu: NavMenu, index: number) => (
          <MobileNavMenu
            key={menu.title}
            menu={menu}
            isLast={index === menus.length - 1}
            isOpen={expandedMenu === menu.title}
            onToggle={title => {
              if (title === expandedMenu) {
                setExpandedMenu(null);
              } else {
                setExpandedMenu(title);
              }
            }}
          />
        ))}
      </div>
    </div>
  );

  return (
    <header className='z-40 flex h-fit w-screen flex-col bg-teal-600/80'>
      {/* Desktop view */}
      <div className='hidden lg:block'>
        {/* Desktop header */}
        <div className='-mb-[12px] -mt-[12px] flex flex-row items-center justify-between p-2'>
          <Logo />
          {/* Profile dropdown for desktop */}
          <div className='mr-4 flex flex-row items-center gap-4'>
            <CompanySelector />
            <HeaderIcons />
            <ProfileButton onClick={handleProfileClick} />
          </div>
        </div>

        <Separator className='h-[0.5px]' />

        {/* Desktop navigation */}
        <nav className='flex flex-row'>
          {menus.map((menu: NavMenu) => (
            <AritoNavMenu key={menu.title} title={menu.title} items={menu.items} />
          ))}
        </nav>

        {/* Profile dropdown menu */}
        <ProfileMenu
          anchorEl={anchorEl}
          isOpen={isProfileOpen}
          onClose={handleProfileClose}
          onLogout={handleLogout} // Pass the new handler
        />
      </div>

      {/* Mobile view */}
      <div className='block lg:hidden'>
        {/* Mobile header - always visible */}
        <div className='flex flex-row items-center justify-between bg-teal-600/80'>
          <div className='flex items-center justify-between'>
            <button onClick={toggleMobileMenu} className='p-1 text-white focus:outline-none'>
              <svg
                xmlns='http://www.w3.org/2000/svg'
                className='h-6 w-6'
                fill='none'
                viewBox='0 0 24 24'
                stroke='currentColor'
              >
                <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M4 6h16M4 12h16M4 18h16' />
              </svg>
            </button>

            <Logo isMobile={true} />
          </div>

          {/* Mobile header icons */}
          <div className='mr-3 flex items-center gap-3'>
            <CompanySelector isMobile={true} />
            <HeaderIcons isMobile={true} />
            <ProfileButton isMobile={true} onClick={handleProfileClick} />
          </div>
        </div>

        <Separator className='h-[0.5px]' />

        {/* Profile dropdown menu */}
        <ProfileMenu
          anchorEl={anchorEl}
          isOpen={isProfileOpen}
          onClose={handleProfileClose}
          onLogout={handleLogout} // Pass the new handler
        />

        {/* Render the menu content when mobile menu is open */}
        {isMobileMenuOpen && mobileMenuContent}
      </div>
    </header>
  );
}
