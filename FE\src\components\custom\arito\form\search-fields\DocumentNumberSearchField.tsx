import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface DocumentNumberSearchFieldProps {
  name?: string;
  label?: string;
  labelClassName?: string;
  className?: string;
  inputClassName?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
  searchEndpoint?: string;
}

// Define the search columns for document numbers
const documentNumberSearchColumns = [
  { field: 'ma_quyen', headerName: 'Mã quyển', flex: 1 },
  { field: 'ten_quyen', headerName: 'Tên quyển', flex: 2 },
  { field: 'so_khai_bao', headerName: 'Số khai báo', flex: 1 }
];

export const DocumentNumberSearchField: React.FC<DocumentNumberSearchFieldProps> = ({
  name = 'documentNumber',
  label = '<PERSON><PERSON> chứng từ',
  labelClassName,
  className = 'flex items-center',
  inputClassName = 'w-full',
  disabled = false,
  formMode = 'add',
  searchEndpoint = 'accounting/document-books'
}) => {
  return (
    <div className={className}>
      <Label className={cn('mt-3 flex items-center text-left text-sm font-normal sm:mb-0', labelClassName)}>
        {label}
      </Label>
      <FormField
        name={name}
        type='text'
        className={inputClassName}
        label=''
        searchEndpoint={searchEndpoint}
        searchColumns={documentNumberSearchColumns}
        defaultSearchColumn='ma_quyen'
        disabled={disabled || formMode === 'view'}
      />
    </div>
  );
};

export default DocumentNumberSearchField;
