import React, { useState } from 'react';

import { Button } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { GeneralTab, OtherTab, BasicInfo } from './tabs';
import { searchSchema, initialValues } from '../schema';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [formValues, setFormValues] = useState(initialValues);

  const handleSubmit = (data: any) => {
    console.log('Form submitted with data:', data);
    onSearch(data);
    // Don't close the dialog here - the parent component will handle this
  };

  // Function to directly submit the form values and close the dialog
  const handleDirectSubmit = () => {
    onSearch(formValues);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Bảng kê hàng bán bị trả lại'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button variant='contained' onClick={handleDirectSubmit} className='bg-[$26827C]'>
            <AritoIcon icon={884} />
            <span className='ml-1'>Đồng ý</span>
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} />
            <span className='ml-1'>Huỷ</span>
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          setFormValues(data);
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto' style={{ width: '800px', minWidth: '800px' }}>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'general',
                  label: 'Thông tin chung',
                  component: <GeneralTab />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
