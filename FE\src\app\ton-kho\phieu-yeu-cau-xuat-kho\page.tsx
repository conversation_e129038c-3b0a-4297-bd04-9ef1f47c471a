'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import { filter } from 'lodash';
import Swal from 'sweetalert2';
import {
  exportRequestDetailColumns,
  getExportRequestColumns
} from '@/features/ton-kho/phieu-yeu-cau-xuat-kho/cols-definition';
import { ExportRequestDetailItemsTab } from '@/features/ton-kho/phieu-yeu-cau-xuat-kho/components/ExportRequestDetailTab';
import { WarehouseExportActionBar } from '@/features/ton-kho/phieu-yeu-cau-xuat-kho/components/ExportRequestActionBar';
import { BasicInfoTab } from '@/features/ton-kho/phieu-yeu-cau-xuat-kho/components/BasicInfoTab';
import { BottomBar } from '@/features/ton-kho/phieu-yeu-cau-xuat-kho/components/BottomBar';
import { exportRequestSchema } from '@/features/ton-kho/phieu-yeu-cau-xuat-kho/schemas';
import { AritoColoredDot } from '@/components/custom/arito/icon/colored-dot';
import { AritoForm } from '@/components/arito/arito-form/arito-form';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoInputTable } from '@/components/custom/arito';

// Mock data structure for warehouse export requests
const initialRows = [
  {
    id: 1,
    requestNumber: 'YCX001',
    date: '2024-01-20',
    status: 'Lập chứng từ',
    department: 'Production',
    requestedBy: 'John Doe',
    warehouseCode: 'WH001',
    description: 'Monthly material request',
    createdBy: 'admin',
    createdAt: '2024-01-20',
    updatedBy: '',
    updatedAt: '',
    details: [
      {
        id: 1,
        productCode: 'P001',
        productName: 'Raw Material A',
        unit: 'KG',
        quantity: 100,
        warehouseCode: 'WH001',
        note: 'Urgent',
        stockQuantity: 500,
        expectedDate: '2024-01-25'
      }
    ]
  }
];

export default function Page() {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [rows, setRows] = useState<any[]>(initialRows);
  const [inputDetails, setInputDetails] = useState<any[]>([]);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputDetails([]);
    setShowForm(true);
  };

  const handleFormSubmit = async (data: any) => {
    try {
      if (inputDetails.length === 0) {
        Swal.fire({
          icon: 'error',
          title: 'Lỗi nhập liệu',
          text: 'Vui lòng nhập chi tiết phiếu yêu cầu'
        });
        return;
      }

      if (formMode === 'add') {
        const newId = Math.max(...rows.map(row => row.id)) + 1;
        const newRequest = {
          ...data,
          id: newId,
          status: 'Mới',
          createdBy: 'Current User',
          createdAt: new Date().toISOString().split('T')[0],
          details: inputDetails
        };
        setRows([...rows, newRequest]);
      } else if (formMode === 'edit' && currentObj?.id) {
        const updatedRows = rows.map(row => {
          if (row.id === currentObj.id) {
            return {
              ...row,
              ...data,
              updatedBy: 'Current User',
              updatedAt: new Date().toISOString().split('T')[0],
              details: inputDetails
            };
          }
          return row;
        });
        setRows(updatedRows);
      }

      setShowForm(false);
    } catch (error: any) {
      Swal.fire({
        icon: 'error',
        title: 'Lỗi',
        text: error.message
      });
    }
  };

  const handleRowClick = (params: GridRowParams) => {
    const obj = params.row as any;
    setSelectedObj(obj);
    setInputDetails(obj.details || []);
  };

  interface FilterValue {
    name: string;
    value: string;
    color: string;
  }

  const filterValues: FilterValue[] = [
    { name: 'Lập chứng từ', value: 'Lập chứng từ', color: 'pink' },
    { name: 'Chờ duyệt', value: 'Chờ duyệt', color: '#EF4444' },
    { name: 'Đã duyệt', value: 'Đã duyệt', color: '#3B82F6' }
  ];
  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: getExportRequestColumns(handleOpenViewForm, handleOpenEditForm)
    },
    ...filterValues.map(filterValue => {
      const filteredRows = filter(rows, row => row.status === filterValue.value);
      return {
        name: filterValue.name,
        rows: filteredRows,
        columns: getExportRequestColumns(handleOpenViewForm, handleOpenEditForm),
        icon: <AritoColoredDot color={filterValue.color} className='mr-2' />,
        tabProps: { className: 'whitespace-nowrap' }
      };
    }),
    ...(filterValues.length > 0
      ? [
          {
            name: 'Khác',
            rows: rows.filter(row => !filterValues.some(filterValue => row.status === filterValue.value)),
            columns: getExportRequestColumns(handleOpenViewForm, handleOpenEditForm),
            icon: <AritoColoredDot color='black' className='mr-2' />,
            tabProps: { className: 'whitespace-nowrap' }
          }
        ]
      : [])
  ];

  // Calculate total quantities for bottom bar
  const calculateTotals = (details: any[]) => {
    return details.reduce((acc, detail) => acc + (Number(detail.quantity) || 0), 0);
  };
  const from = (
    <div className='flex w-full items-center justify-center gap-2 border-b border-gray-300 p-1 pr-2 lg:justify-end'>
      <button
        className='rounded-[2px] bg-teal-600 p-1 text-xs font-bold text-white'
        onClick={() => {}}
        type='button'
        title='Lệnh sản xuất'
      >
        Lệnh sản xuất
      </button>
    </div>
  );

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm ? (
        <div className='h-full flex-1 lg:overflow-hidden'>
          <AritoForm
            mode={formMode}
            initialData={currentObj || undefined}
            onSubmit={handleFormSubmit}
            onClose={handleCloseForm}
            schema={exportRequestSchema}
            subTitle='Phiếu yêu cầu xuất kho'
            headerFields={<BasicInfoTab formMode={formMode} />}
            from={from}
            tabs={[
              {
                id: 'details',
                label: 'Chi tiết',
                component: (
                  <ExportRequestDetailItemsTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                )
              },
              {
                id: 'attachments',
                label: 'File đính kèm',
                component: <div className='p-4'>File đính kèm</div>
              }
            ]}
            bottomBar={<BottomBar totalQuantity={calculateTotals(inputDetails)} totalAmount={0} formMode={formMode} />}
          />
        </div>
      ) : (
        <>
          <WarehouseExportActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            isEditDisabled={!selectedObj}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables tables={tables} onRowClick={handleRowClick} />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable value={selectedObj?.details || []} columns={exportRequestDetailColumns} mode='view' />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
