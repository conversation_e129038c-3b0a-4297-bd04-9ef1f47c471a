import { useFormContext } from 'react-hook-form';
import React from 'react';
import {
  employeeColumns,
  customerSearchColumns,
  customerGroupColumns,
  productColumns,
  productGroupColumns,
  warehouseColumns
} from '../../cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

const GeneralTab: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-2'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã nhân viên:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.NHAN_VIEN}/`}
            searchColumns={employeeColumns}
            dialogTitle='Danh mục nhân viên'
            columnDisplay='employeeCode'
            displayRelatedField='employeeName'
            onValueChange={value => {
              setValue('employeeCode', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('employeeCode', row.employeeCode);
                setValue('employeeName', row.employeeName);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã khách hàng:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
            searchColumns={customerSearchColumns}
            dialogTitle='Danh mục khách hàng'
            columnDisplay='customer_code'
            displayRelatedField='customer_name'
            onValueChange={value => {
              setValue('customerCode', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('customerCode', row.customer_code);
                setValue('customerName', row.customer_name);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm khách hàng:</Label>
          <div className='flex w-2/3 gap-3'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}/`}
              searchColumns={customerGroupColumns}
              dialogTitle='Danh mục nhóm khách hàng'
              columnDisplay='customerGroupCode'
              displayRelatedField='customerGroupName'
              onValueChange={value => {
                setValue('customerGroup1', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('customerGroup1', row.customerGroupCode);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto max-w-full'
            />
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}/`}
              searchColumns={customerGroupColumns}
              dialogTitle='Danh mục nhóm khách hàng'
              columnDisplay='customerGroupCode'
              displayRelatedField='customerGroupName'
              onValueChange={value => {
                setValue('customerGroup2', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('customerGroup2', row.customerGroupCode);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto max-w-full'
            />
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}/`}
              searchColumns={customerGroupColumns}
              dialogTitle='Danh mục nhóm khách hàng'
              columnDisplay='customerGroupCode'
              displayRelatedField='customerGroupName'
              onValueChange={value => {
                setValue('customerGroup3', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('customerGroup3', row.customerGroupCode);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto max-w-full'
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Khu vực:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.KHU_VUC}/`}
            searchColumns={[
              { field: 'regionCode', headerName: 'Mã khu vực', width: 150 },
              { field: 'regionName', headerName: 'Tên khu vực', width: 250 }
            ]}
            dialogTitle='Danh mục khu vực'
            columnDisplay='regionCode'
            displayRelatedField='regionName'
            onValueChange={value => {
              setValue('region', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('region', row.regionCode);
                setValue('regionName', row.regionName);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vật tư:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
            searchColumns={productColumns}
            dialogTitle='Danh mục vật tư'
            columnDisplay='productCode'
            displayRelatedField='productName'
            onValueChange={value => {
              setValue('productCode', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('productCode', row.productCode);
                setValue('productName', row.productName);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Loại vật tư:</Label>
          <div className='flex-1'>
            <div className='flex items-center gap-4'>
              <div className='w-40'>
                <FormField
                  name='productType'
                  label=''
                  type='select'
                  options={[
                    { value: 'service', label: 'Dịch vụ' },
                    { value: 'material', label: 'Vật tư' },
                    { value: 'spare_part', label: 'Phụ tùng' },
                    { value: 'tool', label: 'CCLĐ' },
                    { value: 'semi_finished', label: 'Bán thành phẩm' },
                    { value: 'finished', label: 'Thành phẩm' },
                    { value: 'goods', label: 'Hàng hóa' },
                    { value: 'outsourced', label: 'Hàng gia công' }
                  ]}
                />
              </div>
              <div className='whitespace-nowrap'>
                <FormField name='trackInventory' type='checkbox' label='Chỉ xem vật tư có theo dõi tồn kho' />
              </div>
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm vật tư:</Label>
          <div className='flex w-2/3 gap-3'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
              searchColumns={productGroupColumns}
              dialogTitle='Danh mục nhóm vật tư'
              columnDisplay='productGroupCode'
              displayRelatedField='productGroupName'
              onValueChange={value => {
                setValue('productGroup1', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('productGroup1', row.productGroupCode);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto max-w-full'
            />
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
              searchColumns={productGroupColumns}
              dialogTitle='Danh mục nhóm vật tư'
              columnDisplay='productGroupCode'
              displayRelatedField='productGroupName'
              onValueChange={value => {
                setValue('productGroup2', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('productGroup2', row.productGroupCode);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto max-w-full'
            />
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
              searchColumns={productGroupColumns}
              dialogTitle='Danh mục nhóm vật tư'
              columnDisplay='productGroupCode'
              displayRelatedField='productGroupName'
              onValueChange={value => {
                setValue('productGroup3', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('productGroup3', row.productGroupCode);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto max-w-full'
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã kho:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
            searchColumns={warehouseColumns}
            dialogTitle='Danh mục kho hàng'
            columnDisplay='warehouseCode'
            displayRelatedField='warehouseName'
            onValueChange={value => {
              setValue('warehouseCode', value);
            }}
            onRowSelection={row => {
              if (row) {
                setValue('warehouseCode', row.warehouseCode);
                setValue('warehouseName', row.warehouseName);
              }
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo:</Label>
          <div className='w-96'>
            <FormField
              name='reportTemplate'
              label=''
              type='select'
              options={[
                { value: 'quantity', label: 'Mẫu số lượng' },
                {
                  value: 'quantityAndValue',
                  label: 'Mẫu số lượng và giá trị'
                },
                {
                  value: 'quantityAndForeignValue',
                  label: 'Mẫu số lượng và giá trị ngoại tệ'
                }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeneralTab;
