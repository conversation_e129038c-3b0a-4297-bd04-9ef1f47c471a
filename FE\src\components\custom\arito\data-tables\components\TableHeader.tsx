import React from 'react';
import { Pagination } from '@/components/custom/pagination';
import { shouldShowTabs } from '../helpers';
import { TableTabs } from './TableTabs';
import { TableData } from '../types';

interface TableHeaderProps {
  tables: TableData[];
  activeTab: number;
  isCompact: boolean;
  menuAnchorEl: HTMLElement | null;
  paginationModel: {
    page: number;
    pageSize: number;
  };
  totalItems: number;
  handleTabChange: (event: React.SyntheticEvent, value: number) => void;
  handleMenuOpen: (event: React.MouseEvent<HTMLButtonElement>) => void;
  handleMenuClose: () => void;
  handleMenuItemClick: (index: number) => void;
  handlePageChange: (page: number) => void;
}

export function TableHeader({
  tables,
  activeTab,
  isCompact,
  menuAnchorEl,
  paginationModel,
  totalItems,
  handleTabChange,
  handleMenuOpen,
  handleMenuClose,
  handleMenuItemClick,
  handlePageChange
}: TableHeaderProps) {
  const showTabs = shouldShowTabs(tables);

  return (
    <div className='flex w-full items-center justify-between border-b border-gray-200'>
      {showTabs && (
        <TableTabs
          tables={tables}
          activeTab={activeTab}
          isCompact={isCompact}
          menuAnchorEl={menuAnchorEl}
          handleTabChange={handleTabChange}
          handleMenuOpen={handleMenuOpen}
          handleMenuClose={handleMenuClose}
          handleMenuItemClick={handleMenuItemClick}
        />
      )}

      <Pagination paginationModel={paginationModel} totalItems={totalItems} onPageChange={handlePageChange} />
    </div>
  );
}
