import { Suspense } from 'react';
import CostReportByElementGroupClientPage from '@/features/gia-thanh/bao-cao/bao-cao-gia-thanh-san-pham-theo-nhom-yeu-to/page/client-page';
import AritoLoading from '@/components/custom/arito/loading';
import { AritoError } from '@/components/arito/arito-error';

export default async function CostReportByElementGroupPage() {
  const [error, costReportData] = [null, []];

  return (
    <Suspense fallback={<AritoLoading />}>
      <CostReportByElementGroupClientPage initialRows={costReportData} />
    </Suspense>
  );
}
