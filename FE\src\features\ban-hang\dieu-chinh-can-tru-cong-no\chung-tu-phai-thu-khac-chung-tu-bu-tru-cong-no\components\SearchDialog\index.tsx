import { But<PERSON> } from '@mui/material';
import React from 'react';
import { z } from 'zod';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';
import BasicInfoTab from './BasicInfoTab';
import DetailTab from './DetailTab';
import FilterTab from './FilterTab';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (filters: any) => void;
}

// Define the schema for the search form
const searchSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  voucherNumberStart: z.string().optional(),
  voucherNumberEnd: z.string().optional(),
  description: z.string().optional(),
  objectCode: z.string().optional(),
  accountCode: z.string().optional(),
  unit: z.string().optional(),
  status: z.string().optional(),
  filterByUser: z.string().optional()
});

type SearchFormValues = z.infer<typeof searchSchema>;

const SearchDialog = ({ open, onClose, onSearch }: SearchDialogProps) => {
  const handleSubmit = (data: SearchFormValues) => {
    onSearch(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Lọc chứng từ'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button
            type='submit'
            form='search-form'
            variant='contained'
            sx={{
              borderRadius: 0,
              backgroundColor: '#1976d2',
              '&:hover': { backgroundColor: '#1565c0' }
            }}
          >
            <AritoIcon icon={884} marginX='4px' />
            Đồng ý
          </Button>
          <Button
            onClick={onClose}
            variant='outlined'
            sx={{
              borderRadius: 0,
              borderColor: '#d32f2f',
              color: '#d32f2f',
              '&:hover': {
                borderColor: '#c62828',
                backgroundColor: 'rgba(211, 47, 47, 0.04)'
              }
            }}
          >
            <AritoIcon icon={885} marginX='4px' />
            Huỷ
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'detail',
                  label: 'Chi tiết',
                  component: <DetailTab formMode='add' />
                }
              ]}
              className='border-b border-b-gray-200'
            />

            <FilterTab formMode='add' />
          </div>
        }
      />
    </AritoDialog>
  );
};

export default SearchDialog;
