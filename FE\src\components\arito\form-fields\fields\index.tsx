import React from 'react';
import MultiSearchField from './MultiSearchField';
import DateRangeField from './DateRangeField';
import TextRangeField from './TextRangeField';
import CheckboxField from './CheckboxField';
import TextAreaField from './TextAreaField';
import { CommonFieldProps } from '../types';
import SelectField from './SelectField';
import SearchField from './SearchField';
import RadioField from './RadioField';
import TextField from './TextField';
import DateField from './DateField';
import FileField from './FileField';

// Field component mapping
const fieldComponents: Record<string, React.FC<CommonFieldProps>> = {
  text: TextField,
  number: TextField,
  select: SelectField,
  checkbox: CheckboxField,
  search: SearchField,
  barcode: SearchField,
  password: TextField,
  date: DateField,
  daterange: DateRangeField,
  textarea: TextAreaField,
  file: FileField,
  radio: RadioField,
  textrange: TextRangeField,
  multisearch: MultiSearchField
};

// Field renderer component
export const RenderField: React.FC<CommonFieldProps> = props => {
  const { field } = props;
  const FieldComponent = fieldComponents[field.type];

  if (!FieldComponent) {
    console.warn(`Unknown field type: ${field.type}`);
    return null;
  }

  return <FieldComponent {...props} />;
};

export {
  TextField,
  SelectField,
  CheckboxField,
  DateField,
  DateRangeField,
  TextAreaField,
  FileField,
  SearchField,
  RadioField,
  TextRangeField,
  MultiSearchField
};

export default {
  RenderField,
  TextField,
  SelectField,
  CheckboxField,
  DateField,
  DateRangeField,
  TextAreaField,
  FileField,
  SearchField,
  RadioField,
  TextRangeField,
  MultiSearchField
};
