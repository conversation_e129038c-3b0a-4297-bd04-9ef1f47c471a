import React from 'react';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

// Options for the "Báo cáo theo" dropdown (without "Không nhóm" option)
const reportByOptions = [
  { value: 'customer', label: 'Khách hàng' },
  { value: 'customerGroup1', label: 'Nhóm khách 1' },
  { value: 'customerGroup2', label: 'Nhóm khách 2' },
  { value: 'customerGroup3', label: 'Nhóm khách 3' },
  { value: 'product', label: 'Vật tư' },
  { value: 'productGroup1', label: 'Nhóm vật tư 1' },
  { value: 'productGroup2', label: 'Nhóm vật tư 2' },
  { value: 'productGroup3', label: 'Nhóm vật tư 3' },
  { value: 'unit', label: 'Đơn vị' },
  { value: 'department', label: 'Bộ phận' },
  { value: 'project', label: 'Vụ việc' },
  { value: 'contract', label: 'Hợp đồng' },
  { value: 'agreement', label: 'Khế ước' },
  { value: 'fee', label: 'Phí' },
  { value: 'product', label: 'Sản phẩm' },
  { value: 'productionOrder', label: 'Lệnh sản xuất' },
  { value: 'employee', label: 'Nhân viên bán hàng' }
];

// Options for the "Nhóm theo" dropdown (includes "Không nhóm" option)
const groupByOptions = [{ value: 'no-group', label: 'Không nhóm' }, ...reportByOptions];

const BasicInfo: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày từ/đến:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='fromDate' toDateName='toDate' />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Báo cáo theo:</Label>
          <div>
            <FormField name='reportBy' type='select' options={reportByOptions} label='' className='w-32' />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm theo:</Label>
          <div>
            <div className='flex items-center justify-between'>
              <div className='flex-1'>
                <FormField name='groupBy' type='select' options={groupByOptions} label='' className='w-32' />
              </div>
              <div className='ml-4 flex items-center'>
                <FormField name='includeProductsAndServices' type='checkbox' label='Hàng hóa và dịch vụ' />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
