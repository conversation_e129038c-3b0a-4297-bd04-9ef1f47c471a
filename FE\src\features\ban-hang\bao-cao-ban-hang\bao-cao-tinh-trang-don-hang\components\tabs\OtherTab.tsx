import { useFormContext } from 'react-hook-form';
import React, { useState } from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { giaoDichSearchColumns, loSearchColumns, viTriSearchColumns } from '@/constants';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

const OtherTab: React.FC = () => {
  const { setValue } = useFormContext();
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);

  const handleSaveFilterTemplate = (data: SaveTemplateFormData) => {
    setSaveFilterTemplateDialogOpen(false);
  };

  return (
    <div className='space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Loại đơn hàng:</Label>
          <div className='w-1/2'>
            <FormField
              name='loai_don_hang'
              label=''
              type='select'
              options={[
                { value: 'all', label: '9.Tất cả' },
                { value: 'order', label: 'Đơn hàng' },
                { value: 'contract', label: 'Hợp đồng' }
              ]}
              className='w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã giao dịch:</Label>
          <div className='w-64'>
            <SearchField
              type='text'
              searchEndpoint={`/`}
              searchColumns={giaoDichSearchColumns}
              dialogTitle='Giao dịch'
              columnDisplay='transactionCode'
              displayRelatedField='transactionName'
              onValueChange={value => {
                setValue('ma_giao_dich', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_giao_dich', row.transactionCode);
                }
              }}
              className='w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã lô:</Label>
          <div className='w-36'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.LO}/`}
              searchColumns={loSearchColumns}
              dialogTitle='Lô hàng'
              columnDisplay='ma_lo'
              displayRelatedField='ten_lo'
              onValueChange={value => {
                setValue('ma_lo', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_lo', row.ma_lo);
                }
              }}
              className='w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã vị trí:</Label>
          <div className='w-36'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VI_TRI_KHO_HANG}/`}
              searchColumns={viTriSearchColumns}
              dialogTitle='Vị trí'
              columnDisplay='ma_vi_tri'
              displayRelatedField='ten_vi_tri'
              onValueChange={value => {
                setValue('ma_vi_tri', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('ma_vi_tri', row.ma_vi_tri);
                }
              }}
              className='w-full'
            />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Diễn giải:</Label>
          <div className='w-2/3'>
            <FormField name='dien_giai' label='' type='text' className='w-full' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mẫu lọc báo cáo:</Label>
          <div className='w-full'>
            <div className='flex items-center gap-1'>
              <div className='w-64'>
                <FormField
                  name='mau_loc_bao_cao'
                  label=''
                  type='select'
                  options={[{ value: 'user_filter', label: 'Người dùng tự lọc' }]}
                  className='w-full'
                />
              </div>

              <div className='h-9 w-9 flex-shrink-0'>
                <RadixHoverDropdown
                  iconNumber={624}
                  items={[
                    {
                      value: 'save_new',
                      label: 'Lưu mẫu mới',
                      icon: 7,
                      onClick: () => setSaveFilterTemplateDialogOpen(true)
                    },
                    {
                      value: 'save_overwrite',
                      label: 'Lưu đè vào mẫu đang chọn',
                      icon: 75,
                      onClick: () => console.log('Overwrite current filter template')
                    },
                    {
                      value: 'delete',
                      label: 'Xóa mẫu đang chọn',
                      icon: 8,
                      onClick: () => console.log('Delete current filter template')
                    }
                  ]}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />
    </div>
  );
};

export default OtherTab;
