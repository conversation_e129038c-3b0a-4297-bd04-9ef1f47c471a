import { IconButton, Tooltip } from '@mui/material';
import React from 'react';
import AritoIcon from '@/components/custom/arito/icon';
import { buttonSx } from './AddEditActionFormButtons';

export type ActionButtonFormType = 'add' | 'edit';

interface ActionButtonFormProps {
  onAddForm?: () => void;
  onEditForm?: () => void;
}

export const getActionButtonForm = (type: ActionButtonFormType, props: ActionButtonFormProps): React.ReactNode => {
  const { onAddForm, onEditForm } = props;
  switch (type) {
    case 'add':
      return (
        <Tooltip title='Thêm' arrow>
          <IconButton size='small' onClick={onAddForm} sx={buttonSx} disabled={!onAddForm}>
            <AritoIcon icon={7} />
          </IconButton>
        </Tooltip>
      );

    case 'edit':
      return (
        <Tooltip title='Sửa' arrow>
          <IconButton size='small' onClick={onEditForm} sx={buttonSx} disabled={!onEditForm}>
            <AritoIcon icon={9} />
          </IconButton>
        </Tooltip>
      );

    default:
      return null;
  }
};
