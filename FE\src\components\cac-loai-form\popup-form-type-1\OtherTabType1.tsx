import { ChangeEvent, useState } from 'react';
import { UploadCloud } from 'lucide-react';
import { FormField } from '@/components/custom/arito/form/form-field';
import AritoIcon from '@/components/custom/arito/icon';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface Props {
  formMode: 'add' | 'edit' | 'view';
  onFileChange?: (file: File | null) => void;
}

const OtherTabType1 = ({ formMode, onFileChange }: Props) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
    onFileChange?.(file);
  };
  return (
    <div className='grid grid-cols-1 gap-x-8 space-y-2 p-4 lg:grid-cols-1 lg:space-y-0'>
      <div className='grid grid-cols-1 gap-x-20 lg:grid-cols-2'>
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Ngày sinh'
          name='dateOfBirth'
          type='date'
          labelClassName='min-w-[180px]'
          disabled={formMode === 'view'}
        />
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Số CMND/CCCD'
          name='identityCard'
          type='text'
          labelClassName='min-w-[180px]'
          disabled={formMode === 'view'}
        />
      </div>
      <div className='grid grid-cols-1 gap-x-20 lg:grid-cols-2'>
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Người đại diện pháp luật'
          name='legalRepresentative'
          type='text'
          labelClassName='min-w-[180px]'
          disabled={formMode === 'view'}
        />
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Chức vụ'
          name='position'
          type='text'
          labelClassName='min-w-[180px]'
          disabled={formMode === 'view'}
        />
      </div>
      <FormField
        className='max-w-[540px] items-start gap-y-1 sm:items-center'
        label='Số tài khoản'
        name='accountNumber'
        type='text'
        labelClassName='min-w-[180px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Tên ngân hàng'
        name='bankName'
        type='text'
        labelClassName='min-w-[180px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Chi nhánh'
        name='branch'
        type='text'
        labelClassName='min-w-[180px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Tỉnh thành'
        name='province'
        type='text'
        labelClassName='min-w-[180px]'
        disabled={formMode === 'view'}
      />
      <div className='flex items-center gap-4'>
        <Label htmlFor='file-upload' className='min-w-[100px] text-sm font-medium'>
          Chọn file
        </Label>
        <div className='flex items-center gap-2'>
          <Label
            htmlFor='file-upload'
            className='flex cursor-pointer items-center gap-2 rounded-md bg-primary px-4 py-2 text-primary-foreground hover:bg-primary/90'
          >
            <UploadCloud className='h-4 w-4' />
            <span>Chọn file</span>
          </Label>
          <Input
            id='file-upload'
            type='file'
            className='hidden'
            onChange={handleFileChange}
            disabled={formMode === 'view'}
          />
          {selectedFile && <span className='text-sm text-gray-600'>{selectedFile.name}</span>}
        </div>
      </div>
    </div>
  );
};

export default OtherTabType1;
