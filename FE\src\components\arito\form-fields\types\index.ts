import { GridColDef } from '@mui/x-data-grid';
import { ReactNode } from 'react';

export interface FieldConfig {
  key: string;
  label: string;
  type:
    | 'text'
    | 'number'
    | 'select'
    | 'checkbox'
    | 'search'
    | 'barcode'
    | 'password'
    | 'date'
    | 'daterange'
    | 'textarea'
    | 'table'
    | 'button'
    | 'compound'
    | 'file'
    | 'radio'
    | 'textrange'
    | 'multisearch';
  options?: Array<{ value: string | number; label: string }>;
  defaultValue?: any;
  required?: boolean;
  placeholder?: string;
  span?: number;
  searchable?: boolean;
  disabled?: boolean;
  tooltip?: string;
  minWidth?: string | number;
  showSearchDisplay?: boolean;
  showDaterangeOptions?: boolean;
  valueRender?: (formValues: Record<string, any>) => any;
  gridPosition?: {
    row: number;
    col: number;
    colSpan?: number;
    rowSpan?: number;
  };
  action?: ReactNode;
  labelPosition?: 'left' | 'right';
  // Table specific properties
  tableConfig?: {
    columns: GridColDef[];
    inputColumns?: GridColDef[];
    pageSize?: number;
    height?: number | string;
  };
  // Button specific properties
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  buttonStyle?: React.CSSProperties;
  buttonWidth?: string;
  // Search specific properties
  searchModalTitle?: string;
  searchColumns?: GridColDef[];
  searchData?: any[];
  searchLoading?: boolean;
  docType?: string;
  searchFilters?: any[][];
  // Compound input specific properties
  inputs?: Array<{
    key: string;
    name: string;
    type: 'text' | 'number' | 'select' | 'search' | 'password' | 'date';
    widthPercentage?: number;
    placeholder?: string;
    options?: Array<{ value: string | number; label: string }>;
    required?: boolean;
    disabled?: boolean;
    searchModalTitle?: string;
    searchColumns?: GridColDef[];
    docType?: string;
    searchFilters?: any[][];
    showSearchDisplay?: boolean;
  }>;
  multiple?: boolean;
  accept?: string;
  [key: string]: any;
}

export interface CommonFieldProps {
  field: FieldConfig;
  maxLabelWidth: number;
  isViewMode: boolean;
}

export interface StyledComponentProps {
  theme?: any;
  disabled?: boolean;
}
