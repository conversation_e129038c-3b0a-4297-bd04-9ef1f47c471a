import { useFormContext } from 'react-hook-form';
import React from 'react';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns } from './cols-definition';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

const BasicInfo: React.FC = () => {
  const { setValue } = useFormContext();

  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày báo cáo:</Label>
          <div>
            <FormField name='reportDate' type='date' label='' className='w-[206px]' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày tính hạn tt:</Label>
          <div>
            <FormField name='payDate' type='date' label='' className='w-[206px]' />
          </div>
        </div>
        <div className='flex w-[82.5%] items-center'>
          <Label className='w-40 min-w-40'>Ngày hóa đơn từ/đến:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='ngay_tu' toDateName='ngay_den' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Tài khoản:</Label>
          <div className='w-3/4'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Tài khoản'
              columnDisplay='code'
              displayRelatedField='name'
              onValueChange={value => {
                setValue('tai_khoan', value);
              }}
              onRowSelection={row => {
                if (row) {
                  setValue('tai_khoan', row.code);
                }
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
