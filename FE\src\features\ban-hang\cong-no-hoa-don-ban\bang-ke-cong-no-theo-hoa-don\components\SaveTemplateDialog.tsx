import { Button } from '@mui/material';
import React from 'react';
import { z } from 'zod';
import AritoCreateAnalysisSample from '@/components/custom/arito/create-analysis-sample';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';

interface SaveTemplateDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: SaveTemplateFormData) => void;
  templateType: 'filter' | 'analysis';
}

export interface SaveTemplateFormData {
  templateName: string;
  templateName2?: string;
  analysisTemplate?: string;
}

const saveTemplateSchema = z.object({
  templateName: z.string().min(1, 'Tên mẫu không được để trống'),
  templateName2: z.string().optional(),
  analysisTemplate: z.string().optional()
});

const SaveTemplateDialog: React.FC<SaveTemplateDialogProps> = ({ open, onClose, onSave, templateType }) => {
  const title = templateType === 'filter' ? 'Lưu mẫu báo cáo' : 'Lưu mới mẫu phân tích';

  const actions = (
    <>
      <Button
        variant='contained'
        color='primary'
        type='button'
        onClick={() => {
          const form = document.querySelector('form');
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
        className='bg-blue-600 normal-case hover:bg-blue-800'
      >
        <span className='mx-1'>
          <AritoIcon icon={884} />
        </span>
        Đồng ý
      </Button>

      <Button
        variant='outlined'
        onClick={onClose}
        className='border-blue-600 normal-case text-blue-600 hover:border-blue-800 hover:bg-blue-50'
      >
        <span className='mx-1'>
          <AritoIcon icon={885} />
        </span>
        Hủy
      </Button>
    </>
  );

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      titleIcon={<AritoIcon icon={12} />}
      maxWidth='lg'
      actions={actions}
    >
      <div className='max-h-[80vh] w-[800px] min-w-[800px] overflow-y-auto bg-gray-50'>
        <AritoForm
          mode='add'
          initialData={{
            templateName: '',
            templateName2: '',
            analysisTemplate: ''
          }}
          onSubmit={onSave}
          schema={saveTemplateSchema}
          hasAritoActionBar={false}
          tabs={
            <div className='w-full space-y-4'>
              <div className='grid w-full grid-cols-1'>
                <div className='rounded-md bg-white p-2'>
                  <div className='flex items-center'>
                    <Label className='w-40 min-w-40 font-medium text-gray-700'>
                      {templateType === 'filter' ? 'Nhập tên mẫu mới:' : 'Nhập tên mới mẫu:'}
                    </Label>
                    <div className='flex-1'>
                      <FormField name='templateName' label='' type='text' className='w-full' />
                    </div>
                  </div>

                  <div className='flex items-center'>
                    <Label className='w-40 min-w-40 font-medium text-gray-700'>Tên 2:</Label>
                    <div className='flex-1'>
                      <FormField name='templateName2' label='' type='text' className='w-full' />
                    </div>
                  </div>
                </div>

                {templateType === 'analysis' && (
                  <div className='mb-4 border-b border-gray-200'>
                    <AritoHeaderTabs
                      tabs={[
                        {
                          id: 'info',
                          label: 'Mẫu phân tích',
                          component: (
                            <div className='rounded-md bg-white p-2'>
                              <AritoCreateAnalysisSample
                                columnAnalysisItems={[
                                  { label: 'Stt', type: 'string' },
                                  { label: 'Ngày c/từ', type: 'string' },
                                  { label: 'Số c/từ', type: 'array  ' },
                                  { label: 'Ngày hóa đơn', type: 'string' },
                                  { label: 'Số hóa đơn', type: 'array' },
                                  { label: 'Mã khách hàng', type: 'array' },
                                  { label: 'Tên khách hàng', type: 'array' },
                                  { label: 'Mã NVBH', type: 'array' },
                                  { label: 'Tên NVBH', type: 'array' },
                                  { label: 'Tổng tiền HĐ nt', type: 'number' },
                                  { label: 'Đã thu nt', type: 'number' },
                                  { label: 'Phải thu nt', type: 'number' },
                                  { label: 'Tổng tiền HĐ', type: 'number' },
                                  { label: 'Đã thu', type: 'number' },
                                  { label: 'Phải thu', type: 'number' },
                                  { label: 'Trong hạn', type: 'number' },
                                  { label: 'Quá hạn %s1 ngày', type: 'number' },
                                  { label: 'Quá hạn %s2 ngày', type: 'number' },
                                  { label: 'Quá hạn %s3 ngày', type: 'number' },
                                  { label: 'Quá hạn %s4 ngày', type: 'number' },
                                  { label: 'Quá hạn trên %s5 ngày', type: 'number' },
                                  { label: 'Ngày đến hạn', type: 'string' },
                                  { label: 'Hạn tt', type: 'string' },
                                  { label: 'Số ngày đến hạn', type: 'number' },
                                  { label: 'Diễn giải', type: 'string' },
                                  { label: 'ID', type: 'string' },
                                  { label: 'Mã chứng từ', type: 'array' },
                                  { label: 'Đơn vị', type: 'array' }
                                ]}
                              />
                            </div>
                          )
                        }
                      ]}
                    />
                  </div>
                )}
              </div>
            </div>
          }
        />
      </div>
    </AritoDialog>
  );
};

export default SaveTemplateDialog;
