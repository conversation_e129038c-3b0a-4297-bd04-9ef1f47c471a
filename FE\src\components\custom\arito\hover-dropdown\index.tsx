import React, { useState, ReactNode, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import AritoIcon from '@/components/custom/arito/icon';

interface DropdownItem {
  label: string;
  value: string | number;
  icon?: number;
  onClick?: () => void;
}

interface HoverDropdownProps {
  items: DropdownItem[];
  trigger?: ReactNode;
  iconNumber?: number;
  className?: string;
  dropdownWidth?: string;
  buttonClassName?: string;
}

const HoverDropdown: React.FC<HoverDropdownProps> = ({
  items,
  trigger,
  iconNumber = 284,
  className = '',
  dropdownWidth = 'w-56',
  buttonClassName = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const triggerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  useEffect(() => {
    if (isOpen && triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      // Calculate dropdown width based on the provided dropdownWidth or default to 224px
      const dropdownWidthPx =
        dropdownRef.current?.offsetWidth ||
        (dropdownWidth.includes('w-') ? parseInt(dropdownWidth.replace('w-', '')) * 4 : 224); // Convert Tailwind width classes to pixels

      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: Math.max(0, rect.right - dropdownWidthPx) // Ensure it doesn't go off-screen to the left
      });
    }
  }, [isOpen, dropdownWidth]);

  const handleMouseEnter = () => {
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    setTimeout(() => {
      setIsOpen(false);
    }, 300);
  };

  const handleItemClick = (item: DropdownItem) => {
    if (item.onClick) {
      item.onClick();
    }
    setIsOpen(false);
  };

  // Portal dropdown menu to body
  const renderDropdown = () => {
    // Don't render if dropdown is closed, component not mounted, or we're in a server environment
    if (!isOpen || !isMounted || typeof window === 'undefined') return null;

    return createPortal(
      <div
        ref={dropdownRef}
        className={`fixed z-[9999] ${dropdownWidth} max-h-[300px] overflow-y-auto rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5`}
        style={{
          top: `${dropdownPosition.top}px`,
          left: `${dropdownPosition.left}px`
        }}
        onMouseEnter={() => setIsOpen(true)}
        onMouseLeave={handleMouseLeave}
      >
        <div className='py-1'>
          {items.map((item, index) => (
            <button
              key={`${item.value}-${index}`}
              type='button'
              className='flex w-full items-center px-4 py-2 text-left text-sm hover:bg-gray-100'
              onClick={() => handleItemClick(item)}
            >
              {item.icon && <AritoIcon icon={item.icon} className='mx-1' />}
              <span className={item.icon ? 'ml-2' : ''}>{item.label}</span>
            </button>
          ))}
        </div>
      </div>,
      document.body
    );
  };

  return (
    <div className={`${className} inline-block`}>
      {/* Trigger button */}
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        style={{ lineHeight: 0 }} /* Prevent line height issues */
      >
        {trigger || (
          <button
            type='button'
            className={`flex h-9 w-9 items-center justify-center rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground ${buttonClassName}`}
            style={{ verticalAlign: 'middle' }} /* Ensure proper alignment */
          >
            <AritoIcon icon={iconNumber} />
          </button>
        )}
      </div>

      {/* Render dropdown through portal */}
      {renderDropdown()}
    </div>
  );
};

export default HoverDropdown;
