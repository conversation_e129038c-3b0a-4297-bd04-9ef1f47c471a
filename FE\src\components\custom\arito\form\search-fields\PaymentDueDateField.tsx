import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface PaymentDueDateFieldProps {
  name?: string;
  label?: string;
  labelClassName?: string;
  className?: string;
  inputClassName?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
}

export const PaymentDueDateField: React.FC<PaymentDueDateFieldProps> = ({
  name = 'paymentDueDate',
  label = 'Hạn thanh toán',
  labelClassName,
  className = 'flex items-center',
  inputClassName = 'w-full',
  disabled = false,
  formMode = 'add'
}) => {
  return (
    <div className={className}>
      <Label className={cn('mt-3 flex items-center text-left text-sm font-normal sm:mb-0', labelClassName)}>
        {label}
      </Label>
      <FormField
        name={name}
        type='date'
        className={inputClassName}
        label=''
        disabled={disabled || formMode === 'view'}
      />
    </div>
  );
};

export default PaymentDueDateField;
