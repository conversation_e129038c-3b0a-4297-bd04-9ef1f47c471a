import React, { useState } from 'react';

import { Button } from '@mui/material';
import { DetailsTab, FilterByObjectTab, OtherTab, BasicInfo } from './tabs';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { searchSchema, initialValues } from '../schema';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const [formValues, setFormValues] = useState(initialValues);

  const handleSubmit = (data: any) => {
    onSearch(data);
  };

  const handleDirectSubmit = () => {
    onSearch(formValues);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo bán hàng so s<PERSON>h bán hàng hai kỳ'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
      actions={
        <>
          <Button variant='contained' onClick={handleDirectSubmit} className='bg-[#26827C]'>
            <AritoIcon icon={884} />
            <span className='ml-1'>Đồng ý</span>
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} />
            <span className='ml-1'>Huỷ</span>
          </Button>
        </>
      }
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          setFormValues(data);
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab />
                },
                {
                  id: 'filter-by-object',
                  label: 'Lọc theo đối tượng',
                  component: <FilterByObjectTab />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
