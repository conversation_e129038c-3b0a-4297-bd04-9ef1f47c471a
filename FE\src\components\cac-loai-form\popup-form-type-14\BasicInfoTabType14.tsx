import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTabType14 = ({ formMode }: Props) => {
  return (
    <div className='space-y-4 p-4'>
      <div className='grid grid-cols-1 gap-x-2 lg:grid lg:grid-cols-[2fr,2fr,1fr,1fr]'>
        <FormField
          className='flex w-[300px] items-center justify-between'
          label='Mã khách hàng'
          name='customerCode'
          labelClassName='min-w-[140px]'
          type='text'
          disabled={formMode === 'view'}
        />
        <FormField
          className='flex w-[220px] items-center justify-between'
          label=''
          name='supplierType'
          labelClassName='min-w-[100px]'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { label: '<PERSON>anh nghiệp', value: '1' },
            { label: 'Cá nhân', value: '2' }
          ]}
        />
        <FormField
          className='flex w-[220px] items-center justify-between'
          type='checkbox'
          label='Khách hàng'
          labelClassName='min-w-[100px]'
          name='customer'
          disabled={formMode === 'view'}
        />
        <FormField
          className='flex w-[220px] items-center justify-between'
          type='checkbox'
          label='Nhà cung cấp'
          labelClassName='min-w-[100px]'
          name='supplier'
          disabled={formMode === 'view'}
        />
      </div>
      <FormField
        className='flex max-w-[300px] items-center justify-between'
        label='Tên khách hàng'
        name='customerName'
        type='text'
        labelClassName='min-w-[140px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='flex max-w-[300px] items-center justify-between'
        label='Tên khác'
        name='otherName'
        type='text'
        labelClassName='min-w-[140px]'
        disabled={formMode === 'view'}
      />
      <FormField
        className='flex max-w-[300px] items-center justify-between'
        label='Địa chỉ'
        name='address'
        type='text'
        labelClassName='min-w-[140px]'
        disabled={formMode === 'view'}
      />
      <div className='flex items-center gap-x-20'>
        <div className='flex items-center gap-x-2'>
          <FormField
            className='flex max-w-[300px] items-center justify-between'
            label='Mã số thuế'
            name='taxCode'
            labelClassName='min-w-[140px]'
            type='text'
            disabled={formMode === 'view'}
          />
          <div className='cursor-pointer'>
            <AritoIcon icon={15} />
          </div>
        </div>
        <FormField
          className='flex max-w-[300px] items-center justify-between'
          label='Người liên hệ'
          name='contactPerson'
          labelClassName='min-w-[100px]'
          type='text'
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  );
};

export default BasicInfoTabType14;
