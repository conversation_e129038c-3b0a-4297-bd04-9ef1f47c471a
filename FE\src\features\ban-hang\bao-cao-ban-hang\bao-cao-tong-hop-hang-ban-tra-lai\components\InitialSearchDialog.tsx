import React, { useState } from 'react';

import { Button } from '@mui/material';

import { AritoHeaderTabs, BottomBar } from '@/components/custom/arito';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito';

import { GeneralTab, OtherTab, BasicInfo } from './tabs';
import { searchSchema, initialValues } from '../schema';
import AritoIcon from '@/components/custom/arito/icon';
import { useSearchFieldStates } from '../hooks';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const { generalTabSearchFieldStates, otherTabSearchFieldStates } = useSearchFieldStates();
  const {
    employee,
    customer,
    customerGroup1,
    customerGroup2,
    customerGroup3,
    productGroup1,
    productGroup2,
    productGroup3,
    warehouse
  } = generalTabSearchFieldStates;
  const { transactionCode, itemAccount, revenueAccount, costAccount, batchCode, locationCode } =
    otherTabSearchFieldStates;

  const handleSubmit = (data: any) => {
    const combinedData = {
      ...data,
      ma_nv: employee?.uuid || '',
      ma_kh: customer?.uuid || '',
      nh_kh1: customerGroup1?.uuid || '',
      nh_kh2: customerGroup2?.uuid || '',
      nh_kh3: customerGroup3?.uuid || '',
      nh_vt1: productGroup1?.uuid || '',
      nh_vt2: productGroup2?.uuid || '',
      nh_vt3: productGroup3?.uuid || '',
      ma_kho: warehouse?.uuid || '',
      ma_gd: transactionCode?.uuid || '',
      tk_vt: itemAccount?.uuid || '',
      tk_dt: revenueAccount?.uuid || '',
      tk_gv: costAccount?.uuid || '',
      ma_lo: batchCode?.uuid || '',
      ma_vi_tri: locationCode?.uuid || ''
    };

    onSearch(combinedData);
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo tổng hợp hàng bán trả lại'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='search'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={data => {
          handleSubmit(data);
        }}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-100px)] min-w-[900px] overflow-y-hidden'>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'general',
                  label: 'Thông tin chung',
                  component: <GeneralTab searchFieldStates={generalTabSearchFieldStates} />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab searchFieldStates={otherTabSearchFieldStates} />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
        bottomBar={<BottomBar onClose={onClose} mode='search' />}
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
