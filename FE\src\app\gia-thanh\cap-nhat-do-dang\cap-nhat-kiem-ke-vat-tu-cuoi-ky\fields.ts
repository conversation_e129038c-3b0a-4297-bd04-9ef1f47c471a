import { FieldConfig } from '@/components/arito/arito-form-fields';
import { TabConfig } from '@/components/arito/arito-filter-modal';

export const updateEOPInventoryFilterFields: FieldConfig[] = [
  {
    key: 'ky',
    name: 'ky',
    label: 'Kỳ',
    type: 'text',
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 4
    }
  },
  {
    key: 'nam',
    name: 'nam',
    label: 'Năm',
    type: 'text',
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 4
    }
  },
  {
    key: 'ma_yeu_to',
    name: 'ma_yeu_to',
    label: 'Mã yếu tố',
    type: 'search',
    searchModalTitle: 'Danh mục yếu tố',
    searchColumns: [
      { field: 'ma_to', headerName: 'Mã yếu tố', width: 150 },
      { field: 'ten_to', headerName: 'Tên yếu tố', width: 300 }
    ],
    gridPosition: {
      row: 2,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'don_vi',
    name: 'don_vi',
    label: 'Đơn vị',
    type: 'select',
    options: [
      { value: '0318423416', label: '0318423416 - CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ' }
    ],
    gridPosition: {
      row: 3,
      col: 0,
      colSpan: 9
    }
  }
];

export const updateEOPInventoryFilterTabs: TabConfig[] = [];

export const updateEOPInventoryFields: FieldConfig[] = [
  {
    key: 'ma_to',
    name: 'ma_to',
    label: 'Mã yếu tố',
    type: 'search',
    searchModalTitle: 'Danh mục mã tố',
    searchColumns: [
      { field: 'ma_to', headerName: 'Mã yếu tố', width: 150 },
      { field: 'ten_to', headerName: 'Tên yếu tố', width: 300 }
    ],
    docType: 'ProductionCode',
    gridPosition: {
      row: 0,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'ma_san_pham',
    name: 'ma_san_pham',
    label: 'Mã sản phẩm',
    type: 'search',
    searchModalTitle: 'Danh mục sản phẩm',
    searchColumns: [
      { field: 'ma_san_pham', headerName: 'Mã vật tư', width: 150 },
      { field: 'ten_san_pham', headerName: 'Tên vật tư', width: 300 },
      { field: 'dvt', headerName: 'Đvt lẻ', width: 100 }
    ],
    docType: 'Material',
    gridPosition: {
      row: 1,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'ma_vat_tu',
    name: 'ma_vat_tu',
    label: 'Mã vật tư',
    type: 'search',
    searchModalTitle: 'Danh mục vật tư',
    searchColumns: [
      { field: 'ma_vat_tu', headerName: 'Mã vật tư', width: 150 },
      { field: 'ten_vat_tu', headerName: 'Tên vật tư', width: 300 },
      { field: 'dvt', headerName: 'Đvt', width: 100 },
      { field: 'nhom', headerName: 'Nhóm 1', width: 100 },
      { field: 'theo_doi_lo', headerName: 'Theo dõi lô', width: 100 },
      { field: 'quy_cach', headerName: 'Quy cách', width: 100 },
      { field: 'hinh_anh', headerName: 'Hình ảnh', width: 100 }
    ],
    docType: 'Material',
    gridPosition: {
      row: 2,
      col: 0,
      colSpan: 6
    }
  },
  {
    key: 'sl_do_dang',
    name: 'sl_do_dang',
    label: 'Sl dở dang',
    type: 'number',
    defaultValue: 0,
    gridPosition: {
      row: 3,
      col: 0,
      colSpan: 6
    }
  }
];

export const updateEOPInventoryTabs: TabConfig[] = [];
