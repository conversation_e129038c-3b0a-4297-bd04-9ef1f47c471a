import { DataGrid } from '@mui/x-data-grid';
import { styleDataGrid, slotsStyle, slotPropsStyle } from './style';
import { getFormattedColumns } from './columns';
import { InputTableProps } from './types';
import { formatRows } from './helper';
import { useSort } from './hooks';
import { cn } from '@/lib/utils';

export function InputTable<T = any>({
  rows = [],
  columns,
  mode = 'add',
  className,
  getRowId,
  actionButtons,
  selectedRowId,
  onRowClick,
  onCellClick
}: InputTableProps<T>) {
  const { sortConfig, handleSort } = useSort();

  return (
    <div className={cn('flex h-72 flex-col', className)}>
      {actionButtons}

      <div className={'relative h-full w-auto flex-auto overflow-auto'}>
        <DataGrid
          rows={formatRows(rows)}
          columns={getFormattedColumns(columns, mode, undefined, handleSort, sortConfig)}
          rowHeight={34}
          hideFooter
          disableColumnFilter
          disableColumnMenu
          pagination
          pageSizeOptions={[10]}
          onRowClick={onRowClick}
          onCellClick={onCellClick}
          rowSelectionModel={selectedRowId ? [selectedRowId] : []}
          isCellEditable={() => false}
          slots={slotsStyle}
          slotProps={slotPropsStyle}
          sx={styleDataGrid(mode)}
          disableVirtualization={true}
          getRowId={getRowId || (row => row?.uuid || row?.id || row?.code || String(Math.random()))}
        />
      </div>
    </div>
  );
}
