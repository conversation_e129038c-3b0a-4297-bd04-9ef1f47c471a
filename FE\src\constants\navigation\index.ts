import { giaThanhMenu } from './menus/gia-thanh';
import { taiChinhMenu } from './menus/tai-chinh';
import { nganSachMenu } from './menus/ngan-sach';
import { phanTichMenu } from './menus/phan-tich';
import { tienMatMenu } from './menus/tien-mat';
import { banHangMenu } from './menus/ban-hang';
import { muaHangMenu } from './menus/mua-hang';
import { phaiTraMenu } from './menus/phai-tra';
import { tongHopMenu } from './menus/tong-hop';
import { danhMucMenu } from './menus/danh-muc';
import { heThongMenu } from './menus/he-thong';
import { troGiupMenu } from './menus/tro-giup';
import { tienGuiMenu } from './menus/tien-gui';
import { phaiThuMenu } from './menus/phai-thu';
import { baoCaoMenu } from './menus/bao-cao';
import { hoaDonMenu } from './menus/hoa-don';
import { taiSanMenu } from './menus/tai-san';
import { congCuMenu } from './menus/cong-cu';
import { tonKhoMenu } from './menus/ton-kho';
import { thueMenu } from './menus/thue';
import { NavMenu } from './types';
export type { NavMenu } from './types';

export const menus: NavMenu[] = [
  tienMatMenu,
  tienGuiMenu,
  banHangMenu,
  muaHangMenu,
  phaiTraMenu,
  phaiThuMenu,
  baoCaoMenu,
  phanTichMenu,
  hoaDonMenu,
  taiSanMenu,
  congCuMenu,
  tonKhoMenu,
  giaThanhMenu,
  thueMenu,
  nganSachMenu,
  tongHopMenu,
  danhMucMenu,
  heThongMenu,
  troGiupMenu,
  taiChinhMenu
];
