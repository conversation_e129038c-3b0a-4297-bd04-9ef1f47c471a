import { Box, Checkbox, Typography } from '@mui/material';
import { Controller } from 'react-hook-form';
import React from 'react';
import { fieldStyles } from '../styles/constants';

interface CheckboxFieldProps {
  name: string;
  label: string;
  control: any;
  disabled?: boolean;
  isViewMode?: boolean;
  fieldId: string;
  error?: string;
  className?: string;
  inputWidth?: string;
}

export const CheckboxField: React.FC<CheckboxFieldProps> = ({
  name,
  label,
  control,
  disabled = false,
  isViewMode = false,
  fieldId,
  error,
  className,
  inputWidth
}) => {
  return (
    <Box
      sx={{
        width: inputWidth !== '1fr' ? inputWidth : '100%'
      }}
    >
      <div
        className={`form-group ${className || ''}`}
        style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '12px'
        }}
      >
        <Controller
          name={name}
          control={control}
          defaultValue={false}
          render={({ field }) => (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Checkbox
                {...field}
                checked={!!field.value}
                disabled={disabled || isViewMode}
                id={fieldId}
                title={label}
                sx={{
                  color: '#d1d5db',
                  padding: '4px',
                  marginRight: '6px',
                  '&.Mui-checked': {
                    color: '#2563EB'
                  },
                  '&:hover': {
                    color: disabled || isViewMode ? '#d1d5db' : '#2563EB'
                  }
                }}
              />

              <Typography
                variant='body2'
                component='label'
                htmlFor={fieldId}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  color: '#000000',
                  fontSize: '0.75rem',
                  cursor: disabled || isViewMode ? 'default' : 'pointer',
                  padding: '0 8px'
                }}
              >
                {label}
              </Typography>
            </Box>
          )}
        />
      </div>

      {error && (
        <Typography
          variant='body2'
          color='error'
          sx={{
            marginLeft: '24px',
            fontSize: '0.75rem',
            marginTop: '4px'
          }}
        >
          {typeof error === 'string' ? error : 'Invalid input'}
        </Typography>
      )}
    </Box>
  );
};
