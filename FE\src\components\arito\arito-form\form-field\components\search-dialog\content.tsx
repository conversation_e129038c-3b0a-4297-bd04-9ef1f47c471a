import { Box, CircularProgress, TextField, Typography } from '@mui/material';
import { SearchTable } from '@/components/custom/arito/search-table';

interface DialogContentProps {
  searchQuery: string;
  onSearchQueryChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isLoading: boolean;
  searchResults: any[];
  searchColumns?: any[];
  searchResultLabelKey?: string;
  searchResultValueKey?: string;
  selectedResult: any;
  onResultClick: (result: any) => void;
  onResultDoubleClick: (result: any) => void;
}

export const DialogContent = ({
  searchQuery,
  onSearchQueryChange,
  isLoading,
  searchResults,
  searchColumns,
  searchResultLabelKey,
  searchResultValueKey,
  selectedResult,
  onResultClick,
  onResultDoubleClick
}: DialogContentProps) => {
  return (
    <Box
      sx={{
        p: 0,
        display: 'flex',
        flexDirection: 'column',
        height: 'calc(100% - 75px)'
      }}
    >
      <Box
        sx={{
          p: 1,
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          alignItems: 'center',
          height: '38px',
          backgroundColor: '#f9fcfd',
          justifyContent: 'space-between',
          marginLeft: '-1%'
        }}
      >
        <TextField
          variant='outlined'
          placeholder='Tìm kiếm...'
          size='small'
          value={searchQuery}
          onChange={onSearchQueryChange}
          sx={{
            width: '300px',
            ml: 1,
            '& .MuiOutlinedInput-input': {
              padding: '4px 8px 4px 12px',
              fontSize: '0.8rem',
              height: '22px'
            },
            '& .MuiOutlinedInput-root': {
              borderRadius: '0px'
            }
          }}
          InputProps={{
            startAdornment: (
              <Box
                sx={{
                  color: 'text.secondary',
                  display: 'flex',
                  alignItems: 'center',
                  mr: 0.5
                }}
              >
                <svg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 20 20' fill='#2563EB'>
                  <path
                    fillRule='evenodd'
                    d='M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z'
                    clipRule='evenodd'
                  />
                </svg>
              </Box>
            )
          }}
        />

        <Typography
          variant='caption'
          sx={{
            color: 'text.secondary',
            mr: 2,
            ml: 1,
            fontSize: '0.7rem'
          }}
        >
          Nhấp đúp để chọn ngay
        </Typography>
      </Box>

      {isLoading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            p: 3
          }}
        >
          <CircularProgress size={24} />
        </Box>
      ) : (
        <Box
          sx={{
            flexGrow: 1,
            overflowY: 'auto',
            height: '100%',
            maxHeight: 'calc(100% - 40px)',
            '& .MuiDataGrid-root': {
              border: 'none',
              height: '100% !important'
            }
          }}
        >
          <SearchTable
            searchResults={searchResults}
            searchColumns={searchColumns}
            searchResultLabelKey={searchResultLabelKey}
            searchResultValueKey={searchResultValueKey}
            selectedResult={selectedResult}
            onResultClick={onResultClick}
            onResultDoubleClick={onResultDoubleClick}
          />
        </Box>
      )}
    </Box>
  );
};
