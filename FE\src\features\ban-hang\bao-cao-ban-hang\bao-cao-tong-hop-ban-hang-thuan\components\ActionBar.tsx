import { <PERSON><PERSON>, Printer, Refresh<PERSON><PERSON>, Search, Sheet } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button/index';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onPrintClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  className?: string;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  className
}) => {
  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'><PERSON><PERSON>o cáo tổng hợp bán hàng thuần</h1>
          <div className='text-[10px] text-gray-500'>
            <p>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              <span className='font-semibold'>Từ ngày 01/04/2025</span>
              <span> đến ngày </span>
              <span className='font-semibold'>03/04/2025</span>
            </p>
          </div>
        </div>
      }
    >
      {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />}
      <AritoMenuButton
        title='In ấn'
        icon={<Printer />}
        items={[
          {
            title: 'Theo vật tư (Giá trị)',
            icon: <AritoIcon icon={20} />,
            group: 0
          },
          {
            title: 'Theo vật tư (Số lượng, Giá trị)',
            icon: <AritoIcon icon={20} />,
            group: 0
          },
          {
            title: 'Theo vật tư (Số lượng, Giá trị ngoại tệ)',
            icon: <AritoIcon icon={20} />,
            group: 0
          },
          {
            title: 'Theo khách hàng (Giá trị)',
            icon: <AritoIcon icon={20} />,
            group: 0
          },
          {
            title: 'Theo khách hàng (Số lượng, Giá trị)',
            icon: <AritoIcon icon={20} />,
            group: 0
          },
          {
            title: 'Theo khách hàng, vật tư (Giá trị)',
            icon: <AritoIcon icon={20} />,
            group: 0
          },
          {
            title: 'Theo khách hàng, vật tư (Số lượng, Giá trị)',
            icon: <AritoIcon icon={20} />,
            group: 0
          },
          {
            title: 'Theo vật tư (Giá trị) - Song ngữ',
            icon: <AritoIcon icon={20} />,
            group: 1
          },
          {
            title: 'Theo vật tư (Số lượng, Giá trị) - Song ngữ',
            icon: <AritoIcon icon={20} />,
            group: 1
          },
          {
            title: 'Theo vật tư (Số lượng, Giá trị ngoại tệ) - Song ngữ',
            icon: <AritoIcon icon={20} />,
            group: 1
          },
          {
            title: 'Theo khách hàng (Giá trị) - Song ngữ',
            icon: <AritoIcon icon={20} />,
            group: 1
          },
          {
            title: 'Theo khách hàng (Số lượng, Giá trị) - Song ngữ',
            icon: <AritoIcon icon={20} />,
            group: 1
          },
          {
            title: 'Theo khách hàng, vật tư (Giá trị) - Song ngữ',
            icon: <AritoIcon icon={20} />,
            group: 1
          },
          {
            title: 'Theo khách hàng, vật tư (Số lượng, Giá trị) - Song ngữ',
            icon: <AritoIcon icon={20} />,
            group: 1
          }
        ]}
      />
      {/* No print button */}
      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='destructive' />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
      )}
      {onExportDataClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
