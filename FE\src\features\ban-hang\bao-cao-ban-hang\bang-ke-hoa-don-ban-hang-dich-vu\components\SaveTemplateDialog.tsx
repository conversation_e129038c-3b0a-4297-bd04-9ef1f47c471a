import { Button } from '@mui/material';
import React from 'react';
import { z } from 'zod';
import { FormField } from '@/components/custom/arito/form/form-field';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';

interface SaveTemplateDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: SaveTemplateFormData) => void;
  templateType: 'filter' | 'analysis';
}

export interface SaveTemplateFormData {
  templateName: string;
  templateName2?: string;
  analysisTemplate?: string;
}

const saveTemplateSchema = z.object({
  templateName: z.string().min(1, 'Tên mẫu không được để trống'),
  templateName2: z.string().optional(),
  analysisTemplate: z.string().optional()
});

const SaveTemplateDialog: React.FC<SaveTemplateDialogProps> = ({ open, onClose, onSave, templateType }) => {
  const title = templateType === 'filter' ? 'Lưu mẫu báo cáo' : 'Lưu mới mẫu phân tích';

  const actions = (
    <>
      <Button
        variant='contained'
        color='primary'
        type='button'
        onClick={() => {
          const form = document.querySelector('form');
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
        sx={{
          textTransform: 'none',
          backgroundColor: '#2563EB',
          '&:hover': {
            backgroundColor: '#1E40AF'
          }
        }}
      >
        <AritoIcon icon={884} marginX='4px' />
        Đồng ý
      </Button>

      <Button
        variant='outlined'
        onClick={onClose}
        sx={{
          textTransform: 'none',
          color: '#2563EB',
          borderColor: '#2563EB',
          '&:hover': {
            borderColor: '#1E40AF',
            backgroundColor: 'rgba(37, 99, 235, 0.1)'
          }
        }}
      >
        <AritoIcon icon={885} marginX='4px' />
        Hủy
      </Button>
    </>
  );

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      titleIcon={<AritoIcon icon={12} />}
      maxWidth='lg'
      actions={actions}
    >
      <div
        style={{
          width: '800px',
          minWidth: '800px',
          maxHeight: '80vh',
          overflowY: 'auto',
          backgroundColor: '#f8f9fa'
        }}
      >
        <AritoForm
          mode='add'
          initialData={{
            templateName: '',
            templateName2: '',
            analysisTemplate: ''
          }}
          onSubmit={onSave}
          schema={saveTemplateSchema}
          hasAritoActionBar={false}
          tabs={
            <div className='w-full space-y-2'>
              <div className='grid w-full grid-cols-1 gap-4'>
                <div className='rounded-md bg-white p-4'>
                  <div className='flex items-center'>
                    <Label className='w-40 min-w-40 font-medium text-gray-700'>
                      {templateType === 'filter' ? 'Nhập tên mẫu mới:' : 'Nhập tên mới mẫu:'}
                    </Label>
                    <div className='flex-1'>
                      <FormField name='templateName' label='' type='text' className='w-full' />
                    </div>
                  </div>

                  <div className='flex items-center'>
                    <Label className='w-40 min-w-40 font-medium text-gray-700'>Tên 2:</Label>
                    <div className='flex-1'>
                      <FormField name='templateName2' label='' type='text' className='w-full' />
                    </div>
                  </div>
                </div>

                {templateType === 'analysis' && (
                  <div className='mt-8'>
                    <div className='mb-4 border-b border-gray-200'>
                      <AritoHeaderTabs
                        tabs={[
                          {
                            id: 'analysis',
                            label: 'Mẫu phân tích',
                            component: <></>
                          }
                        ]}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          }
        />
      </div>
    </AritoDialog>
  );
};

export default SaveTemplateDialog;
