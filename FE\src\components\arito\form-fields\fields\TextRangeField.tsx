import { Controller, useFormContext } from 'react-hook-form';
import React from 'react';
import { InputFieldWrapper, ErrorMessage } from '../components/InputFieldWrapper';
import { StyledInput } from '../inputs/StyledInput';
import { FormRow } from '../components/FormRow';
import { CommonFieldProps } from '../types';

export const TextRangeField: React.FC<CommonFieldProps> = ({ field, maxLabelWidth, isViewMode }) => {
  const { key, label, required, disabled = isViewMode, minWidth, valueRender } = field;

  // Label style
  const labelStyle = {
    width: `${maxLabelWidth}px`,
    minWidth: `${maxLabelWidth}px`,
    fontSize: '12px',
    fontWeight: 400,
    marginRight: '8px',
    whiteSpace: 'nowrap' as const,
    overflow: 'visible' as const,
    display: 'inline-block'
  };

  // Input wrapper style
  const inputWrapperStyle = {
    flex: '1 1 auto',
    position: 'relative' as const,
    minWidth: minWidth ? (typeof minWidth === 'number' ? `${minWidth}px` : minWidth) : '0',
    display: 'flex',
    flexDirection: 'column' as const
  };

  // Container style
  const containerStyle = {
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    position: 'relative' as const,
    overflow: 'visible'
  };

  // Colon element for view mode
  const colonElement = disabled && (
    <span
      style={{
        marginRight: '10px',
        fontSize: '12px',
        fontWeight: 400
      }}
    >
      :
    </span>
  );

  // Label content
  const labelContent = (
    <span style={labelStyle}>
      {label}
      {!disabled && required && <span style={{ color: 'red' }}>*</span>}
    </span>
  );

  return (
    <FormRow>
      {labelContent}
      <InputFieldWrapper style={inputWrapperStyle}>
        <div style={containerStyle}>
          {colonElement}
          {valueRender ? (
            <StyledInput type='text' value={valueRender({}) || ''} disabled={true} />
          ) : (
            <div style={{ display: 'flex', width: '100%', alignItems: 'center' }}>
              <Controller
                name={`${key}_tu`}
                render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                  <>
                    <StyledInput
                      type='text'
                      value={value || ''}
                      onChange={onChange}
                      disabled={disabled}
                      ref={ref}
                      style={{ width: '100%' }}
                      placeholder='Từ'
                    />
                    {error && <ErrorMessage>{error.message}</ErrorMessage>}
                  </>
                )}
              />
              <div style={{ margin: '0 4px' }}></div>
              <Controller
                name={`${key}_den`}
                render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
                  <>
                    <StyledInput
                      type='text'
                      value={value || ''}
                      onChange={onChange}
                      disabled={disabled}
                      ref={ref}
                      style={{ width: '100%' }}
                      placeholder='Đến'
                    />
                    {error && <ErrorMessage>{error.message}</ErrorMessage>}
                  </>
                )}
              />
            </div>
          )}
        </div>
      </InputFieldWrapper>
    </FormRow>
  );
};

export default TextRangeField;
