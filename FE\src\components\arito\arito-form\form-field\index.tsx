import { Box, Typography, useMediaQuery, useTheme } from '@mui/material';
import { Controller } from 'react-hook-form';
import { useContext } from 'react';
import { StandardField } from './components/standard-field';
import { SearchDialog } from './components/search-dialog';
import { SearchField } from './components/search-field';
import { AritoFormContext } from '../arito-form';
import { useSearch } from './hooks/use-search';
import { FormFieldProps } from './types';

export const FormField = ({
  label,
  name,
  type = 'text',
  disabled = false,
  options,
  error,
  withSearch,
  className,
  columns,
  searchEndpoint,
  searchResultLabelKey,
  searchResultValueKey,
  searchColumns,
  defaultSearchColumn,
  displayRelatedField,
  labelWidth = '100px',
  inputWidth = '1fr'
}: FormFieldProps) => {
  const { control, errors, isViewMode } = useContext(AritoFormContext);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const fieldError = error || errors?.[name]?.message;
  const fieldId = name;

  // Use search hook if withSearch is true
  const searchProps = useSearch({
    searchEndpoint,
    searchResultValueKey,
    searchResultLabelKey,
    defaultSearchColumn,
    displayRelatedField,
    searchColumns,
    control,
    name
  });

  return (
    <div>
      <Box
        className={`form-group ${className || ''}`}
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            sm: type === 'table' ? '1fr' : !label ? `0px ${inputWidth}` : `${labelWidth} ${inputWidth}`
          },
          alignItems: { xs: 'flex-start', sm: 'center' },
          gap: 1,
          mb: { xs: 2, sm: 1 }
        }}
      >
        {type !== 'checkbox' && (
          <Typography
            variant='body2'
            component='label'
            htmlFor={fieldId}
            sx={{
              display: type === 'table' || !label ? 'none' : 'flex',
              alignItems: 'center',
              color: '#000000',
              minWidth: 'fit-content',
              fontSize: '0.75rem',
              width: { xs: '100%', sm: 'auto' },
              marginBottom: { xs: '8px', sm: 0 },
              textAlign: { xs: 'left', sm: 'left' }
            }}
          >
            {label}
          </Typography>
        )}

        <Box
          sx={{
            width: '100%',
            gridColumn: type === 'table' || !label ? '1 / -1' : 'auto'
          }}
        >
          <Controller
            name={name}
            control={control}
            render={({ field }) => {
              // Search field rendering
              if (withSearch && (type === 'text' || type === 'number')) {
                return (
                  <>
                    <SearchField
                      field={field}
                      fieldId={fieldId}
                      disabled={disabled}
                      type={type}
                      displayText={searchProps.displayText}
                      relatedFieldValue={searchProps.relatedFieldValue}
                      onSearchClick={() => searchProps.setSearchDialogOpen(true)}
                      onInputChange={value => {
                        field.onChange(value);
                        searchProps.setDisplayText(value);
                        searchProps.setRelatedFieldValue('');
                      }}
                      isMobile={isMobile}
                    />
                    <SearchDialog
                      open={searchProps.searchDialogOpen}
                      onClose={() => searchProps.setSearchDialogOpen(false)}
                      isFullScreen={false}
                      isMobile={isMobile}
                      title={`Danh mục ${String(label).toLowerCase()}`}
                      searchQuery={searchProps.searchQuery}
                      onSearchQueryChange={e => searchProps.setSearchQuery(e.target.value)}
                      isLoading={searchProps.isLoading}
                      searchResults={searchProps.searchResults}
                      selectedResult={searchProps.selectedSearchResult}
                      searchColumns={searchColumns}
                      searchResultLabelKey={searchResultLabelKey}
                      searchResultValueKey={searchResultValueKey}
                      onResultClick={result => searchProps.setSelectedSearchResult(result)}
                      onResultDoubleClick={result => searchProps.handleSelectSearchResult(result, field.onChange)}
                      onConfirm={() =>
                        searchProps.handleSelectSearchResult(searchProps.selectedSearchResult, field.onChange)
                      }
                    />
                  </>
                );
              }

              // Standard field rendering
              return (
                <StandardField
                  field={field}
                  fieldId={fieldId}
                  type={type}
                  disabled={disabled}
                  options={options}
                  columns={columns}
                  isMobile={isMobile}
                  label={label}
                />
              );
            }}
          />
        </Box>
      </Box>

      {fieldError && (
        <Typography
          variant='body2'
          color='error'
          sx={{
            marginTop: 1,
            marginLeft: { lg: '160px' }
          }}
        >
          {fieldError}
        </Typography>
      )}
    </div>
  );
};
