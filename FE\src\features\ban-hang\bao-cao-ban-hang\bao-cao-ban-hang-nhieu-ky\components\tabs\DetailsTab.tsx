import React, { useState } from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const DetailsTab: React.FC = () => {
  const [saveAnalysisTemplateDialogOpen, setSaveAnalysisTemplateDialogOpen] = useState(false);

  const handleSaveAnalysisTemplate = (data: SaveTemplateFormData) => {
    setSaveAnalysisTemplateDialogOpen(false);
  };
  return (
    <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        {/* 1. M<PERSON> khách hàng */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'><PERSON><PERSON> khách hàng:</Label>
          <FormField
            name='customerCode'
            label=''
            type='text'
            searchEndpoint='/api/customers'
            searchResultLabelKey='customerName'
            searchResultValueKey='customerCode'
            searchColumns={[
              {
                field: 'customerCode',
                headerName: 'Mã đối tượng',
                width: 120
              },
              {
                field: 'customerName',
                headerName: 'Tên đối tượng',
                width: 200
              },
              {
                field: 'receivable',
                headerName: 'Công nợ p/thu',
                width: 120
              },
              {
                field: 'payable',
                headerName: 'Công nợ p/trả',
                width: 120
              },
              {
                field: 'taxCode',
                headerName: 'Mã số thuế',
                width: 120
              },
              {
                field: 'email',
                headerName: 'Email',
                width: 150
              },
              {
                field: 'phone',
                headerName: 'Số điện thoại',
                width: 120
              }
            ]}
          />
        </div>

        {/* 2. Nhóm khách hàng */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm khách hàng:</Label>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='customerGroup1'
                label=''
                type='text'
                searchEndpoint='/api/customer-groups'
                searchResultLabelKey='customerGroupName'
                searchResultValueKey='customerGroupCode'
                searchColumns={[
                  {
                    field: 'customerGroupCode',
                    headerName: 'Mã nhóm khách hàng',
                    width: 150
                  },
                  {
                    field: 'customerGroupName',
                    headerName: 'Tên nhóm khách hàng',
                    width: 250
                  }
                ]}
              />
              <FormField
                name='customerGroup2'
                label=''
                type='text'
                searchEndpoint='/api/customer-groups'
                searchResultLabelKey='customerGroupName'
                searchResultValueKey='customerGroupCode'
                searchColumns={[
                  {
                    field: 'customerGroupCode',
                    headerName: 'Mã nhóm khách hàng',
                    width: 150
                  },
                  {
                    field: 'customerGroupName',
                    headerName: 'Tên nhóm khách hàng',
                    width: 250
                  }
                ]}
              />
              <FormField
                name='customerGroup3'
                label=''
                type='text'
                searchEndpoint='/api/customer-groups'
                searchResultLabelKey='customerGroupName'
                searchResultValueKey='customerGroupCode'
                searchColumns={[
                  {
                    field: 'customerGroupCode',
                    headerName: 'Mã nhóm khách hàng',
                    width: 150
                  },
                  {
                    field: 'customerGroupName',
                    headerName: 'Tên nhóm khách hàng',
                    width: 250
                  }
                ]}
              />
            </div>
          </div>
        </div>

        {/* 3. Khu vực */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Khu vực:</Label>
          <FormField
            name='region'
            label=''
            type='text'
            searchEndpoint='/api/regions'
            searchResultLabelKey='regionName'
            searchResultValueKey='regionCode'
            searchColumns={[
              {
                field: 'regionCode',
                headerName: 'Mã khu vực',
                width: 150
              },
              {
                field: 'regionName',
                headerName: 'Tên khu vực',
                width: 250
              }
            ]}
          />
        </div>

        {/* 4. Mã nhân viên */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã nhân viên:</Label>
          <FormField
            name='employeeCode'
            label=''
            type='text'
            searchEndpoint='/api/employees'
            searchResultLabelKey='employeeName'
            searchResultValueKey='employeeCode'
            searchColumns={[
              {
                field: 'employeeCode',
                headerName: 'Mã nhân viên',
                width: 150
              },
              {
                field: 'employeeName',
                headerName: 'Tên nhân viên',
                width: 250
              }
            ]}
          />
        </div>

        {/* 5. Mã vật tư */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vật tư:</Label>
          <FormField
            name='productCode'
            label=''
            type='text'
            searchEndpoint='/api/products'
            searchResultLabelKey='productName'
            searchResultValueKey='productCode'
            searchColumns={[
              { field: 'productCode', headerName: 'Mã vật tư', width: 120 },
              { field: 'productName', headerName: 'Tên vật tư', width: 200 },
              { field: 'uom', headerName: 'Đvt', width: 80 },
              { field: 'group1', headerName: 'Nhóm 1', width: 120 },
              {
                field: 'batchTracking',
                headerName: 'Theo dõi lô',
                width: 120
              },
              { field: 'specification', headerName: 'Quy cách', width: 150 },
              { field: 'image', headerName: 'Hình ảnh', width: 100 }
            ]}
          />
        </div>

        {/* 6. Loại vật tư */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Loại vật tư:</Label>
          <div className='flex-1'>
            <div className='flex items-center gap-4'>
              <div className='w-40'>
                <FormField
                  name='productType'
                  label=''
                  type='select'
                  options={[
                    { value: 'service', label: 'Dịch vụ' },
                    { value: 'material', label: 'Vật tư' },
                    { value: 'spare_part', label: 'Phụ tùng' },
                    { value: 'tool', label: 'CCLĐ' },
                    { value: 'semi_finished', label: 'Bán thành phẩm' },
                    { value: 'finished', label: 'Thành phẩm' },
                    { value: 'goods', label: 'Hàng hóa' },
                    { value: 'outsourced', label: 'Hàng gia công' }
                  ]}
                />
              </div>
              <div className='whitespace-nowrap'>
                <FormField name='trackInventory' type='checkbox' label='Chỉ xem vật tư có theo dõi tồn kho' />
              </div>
            </div>
          </div>
        </div>

        {/* 7. Nhóm vật tư */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm vật tư:</Label>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='productGroup1'
                label=''
                type='text'
                searchEndpoint='/api/product-groups'
                searchResultLabelKey='productGroupName'
                searchResultValueKey='productGroupCode'
                searchColumns={[
                  {
                    field: 'productGroupCode',
                    headerName: 'Mã nhóm vật tư',
                    width: 150
                  },
                  {
                    field: 'productGroupName',
                    headerName: 'Tên nhóm vật tư',
                    width: 250
                  }
                ]}
              />
              <FormField
                name='productGroup2'
                label=''
                type='text'
                searchEndpoint='/api/product-groups'
                searchResultLabelKey='productGroupName'
                searchResultValueKey='productGroupCode'
                searchColumns={[
                  {
                    field: 'productGroupCode',
                    headerName: 'Mã nhóm vật tư',
                    width: 150
                  },
                  {
                    field: 'productGroupName',
                    headerName: 'Tên nhóm vật tư',
                    width: 250
                  }
                ]}
              />
              <FormField
                name='productGroup3'
                label=''
                type='text'
                searchEndpoint='/api/product-groups'
                searchResultLabelKey='productGroupName'
                searchResultValueKey='productGroupCode'
                searchColumns={[
                  {
                    field: 'productGroupCode',
                    headerName: 'Mã nhóm vật tư',
                    width: 150
                  },
                  {
                    field: 'productGroupName',
                    headerName: 'Tên nhóm vật tư',
                    width: 250
                  }
                ]}
              />
            </div>
          </div>
        </div>

        {/* 8. Mã kho */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã kho:</Label>
          <FormField
            name='warehouseCode'
            label=''
            type='text'
            searchEndpoint='/api/warehouses'
            searchResultLabelKey='warehouseName'
            searchResultValueKey='warehouseCode'
            searchColumns={[
              { field: 'warehouseCode', headerName: 'Mã kho', width: 120 },
              { field: 'warehouseName', headerName: 'Tên kho', width: 200 },
              { field: 'unit', headerName: 'Đơn vị', width: 120 },
              {
                field: 'locationTracking',
                headerName: 'Theo dõi vị trí',
                width: 120
              }
            ]}
          />
        </div>

        {/* 9. Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo:</Label>
          <div className='w-[400px]'>
            <FormField
              name='reportTemplate'
              label=''
              type='select'
              options={[
                { value: 'quantity', label: 'Báo cáo phân tích nhiều kì (Mẫu số lượng)' },
                { value: 'revenue', label: 'Báo cáo phân tích nhiều kì (Mẫu doanh thu)' },
                { value: 'revenue-profit', label: 'Báo cáo phân tích nhiều kì (Mẫu doanh thu/lợi nhuận)' }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
