import { Button } from '@mui/material';
import AritoIcon from '@/components/custom/arito/icon';
import { cn } from '@/lib/utils';

interface BottomBarProps {
  mode: 'view' | 'edit' | 'add' | 'search';
  bottomBar?: React.ReactNode;
  onSubmit?: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  onClose: () => void;
  className?: string;
}

interface ActionButtonProps {
  onClick?: () => void;
  icon: number;
  title: string;
  primary?: boolean;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'contained' | 'outlined' | 'text';
}

const ActionButton = ({ onClick, icon, title, primary, type, variant = 'contained' }: ActionButtonProps) => (
  <Button
    className={`flex items-center justify-center gap-2 px-4 py-2 !capitalize ${
      primary ? '!bg-main !text-white' : 'border border-main !text-main'
    }`}
    onClick={type !== 'submit' ? onClick : undefined}
    type={type || 'button'}
    title={title}
    variant={variant}
  >
    <AritoIcon icon={icon} className='mx-1' />
    <p className='flex items-center justify-center text-sm font-bold'>{title}</p>
  </Button>
);

export function BottomBar({
  mode,
  bottomBar,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy,
  onClose,
  className
}: BottomBarProps) {
  const viewModeButtons = [
    { show: onAdd, onClick: onAdd, icon: 571, title: 'Thêm', primary: true },
    { show: onEdit, onClick: onEdit, icon: 9, title: 'Sửa' },
    { show: onDelete, onClick: onDelete, icon: 8, title: 'Xoá' },
    { show: onCopy, onClick: onCopy, icon: 11, title: 'Sao chép' }
  ];

  return (
    <div
      className={cn(
        `flex w-full justify-end gap-1 border-t border-gray-200 bg-[#f9fcfd] px-4 py-2 sm:px-4 sm:py-2`,
        className
      )}
    >
      {bottomBar || (
        <>
          {mode !== 'view' && <ActionButton onClick={onSubmit} icon={884} title='Lưu' primary type='submit' />}
          {mode === 'view' && (
            <>
              {viewModeButtons.map(
                button =>
                  button.show && (
                    <ActionButton
                      key={button.title}
                      onClick={button.onClick!}
                      icon={button.icon}
                      title={button.title}
                      primary={button.primary}
                      variant='outlined'
                    />
                  )
              )}
            </>
          )}
          <ActionButton onClick={onClose} icon={885} variant='outlined' title='Huỷ' />
        </>
      )}
    </div>
  );
}
