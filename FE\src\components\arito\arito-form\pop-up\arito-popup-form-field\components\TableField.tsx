import { Controller } from 'react-hook-form';
import { Box } from '@mui/material';
import React from 'react';
import { AritoInputTable } from '@/components/custom/arito';

interface TableFieldProps {
  name: string;
  control: any;
  disabled?: boolean;
  isViewMode?: boolean;
  columns?: any[];
}

export const TableField: React.FC<TableFieldProps> = ({
  name,
  control,
  disabled = false,
  isViewMode = false,
  columns = []
}) => {
  return (
    <Controller
      name={name}
      control={control}
      defaultValue={[]}
      render={({ field }) => (
        <Box sx={{ height: '300px', width: '100%' }}>
          <AritoInputTable
            value={field.value || []}
            columns={columns}
            onChange={field.onChange}
            mode={disabled || isViewMode ? 'view' : 'edit'}
          />
        </Box>
      )}
    />
  );
};
