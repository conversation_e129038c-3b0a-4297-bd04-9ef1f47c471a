import { useFormContext } from 'react-hook-form';
import { SearchField, FormField } from '@/components/custom/arito/form';
import { paymentDaySearchCols } from './search-cols-definition';
import { generateDaysInCurrentMonth } from '@/lib/dateUtil';
import { NgayThanhToan } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { WeekOption } from '../hooks/useWeek';
import { FormMode } from '@/types/form';

const paymentDays = generateDaysInCurrentMonth();

interface GeneralTabProps {
  selectedPaymentDay: NgayThanhToan | null;
  setSelectedPaymentDay: (paymentDay: NgayThanhToan) => void;
  selectedWeek: WeekOption | null;
  setSelectedWeek: (week: WeekOption) => void;
  formMode: FormMode;
}

const GeneralTab: React.FC<GeneralTabProps> = ({
  selectedPaymentDay,
  setSelectedPaymentDay,
  selectedWeek,
  setSelectedWeek,
  formMode
}) => {
  const { watch } = useFormContext();
  const loaiDieuKien = watch('loai_dk');

  return (
    <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center gap-4'>
          <Label>Ngày gom hóa đơn từ/đến</Label>
          <div className='flex items-center gap-2'>
            <FormField name='ngay_gom_hd1' label='' className='w-32' type='number' disabled={formMode === 'view'} />
            <FormField name='ngay_gom_hd2' label='' className='w-32' type='number' disabled={formMode === 'view'} />
          </div>
        </div>

        {loaiDieuKien === '2' && (
          <>
            <div className='flex items-center'>
              <Label className='w-48'>Ngày trong tuần (thứ):</Label>
              <div className='flex-1'>
                <FormField
                  name='ngay_tt_tuan'
                  label=''
                  type='select'
                  options={[
                    { value: '1', label: 'Chủ nhật' },
                    { value: '2', label: 'Thứ 2' },
                    { value: '3', label: 'Thứ 3' },
                    { value: '4', label: 'Thứ 4' },
                    { value: '5', label: 'Thứ 5' },
                    { value: '6', label: 'Thứ 6' },
                    { value: '7', label: 'Thứ 7' }
                  ]}
                  disabled={formMode === 'view'}
                />
              </div>
            </div>
            <div className='mt-2 flex items-center'>
              <Label className='w-48'>Tuần (thứ):</Label>
              <div className='flex-1'>
                <SearchField
                  type='text'
                  columnDisplay='value'
                  className='w-32'
                  dialogTitle='Danh sách tuần'
                  value={selectedWeek?.value || ''}
                  onRowSelection={row => setSelectedWeek(row)}
                  rows={[
                    { value: '1', label: 'Tuần thứ 1 của tháng' },
                    { value: '2', label: 'Tuần thứ 2 của tháng' },
                    { value: '3', label: 'Tuần thứ 3 của tháng' },
                    { value: '4', label: 'Tuần thứ 4 của tháng' },
                    { value: '5', label: 'Tuần thứ 5 của tháng' }
                  ]}
                  searchColumns={[
                    { field: 'value', headerName: 'Mã', width: 100 },
                    { field: 'label', headerName: 'Tên', width: 200 }
                  ]}
                  disabled={formMode === 'view'}
                />
              </div>
            </div>
          </>
        )}

        {loaiDieuKien === '1' && (
          <div className='flex items-center'>
            <Label className='mr-20'>Ngày thanh toán: </Label>
            <div className='flex-1'>
              <SearchField<NgayThanhToan>
                type='text'
                columnDisplay='ma'
                className='w-32'
                dialogTitle='Danh mục ngày thanh toán'
                value={selectedPaymentDay?.ma || ''}
                onRowSelection={row => setSelectedPaymentDay(row)}
                rows={paymentDays}
                searchColumns={paymentDaySearchCols}
                disabled={formMode === 'view'}
              />
            </div>
            <span className='px-3 text-xs'>(Trong tháng)</span>
          </div>
        )}
        <div className='flex items-center'>
          <Label className='w-48'>Số ngày thanh toán:</Label>
          <div className='flex-1'>
            <FormField name='so_ngay' label='' type='number' disabled={formMode === 'view'} />
          </div>
        </div>
        <div className='flex items-center'>
          <FormField
            name='status'
            label='Trạng thái'
            labelClassName='w-48'
            type='select'
            options={[
              { value: '1', label: '1 - Còn sử dụng' },
              { value: '0', label: '0 - Không sử dụng' }
            ]}
            className='w-[400px]'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  );
};

export default GeneralTab;
