import { Allotment } from 'allotment';
import 'allotment/dist/style.css';
import React from 'react';

interface AritoSplitPaneProps {
  split: 'vertical' | 'horizontal';
  minSize: number;
  defaultSize: number;
  children: React.ReactNode;
}

export const AritoSplitPane: React.FC<AritoSplitPaneProps> = ({ split, minSize, defaultSize, children }) => {
  const childrenArray = React.Children.toArray(children);
  return (
    <Allotment vertical={split === 'horizontal'} className='h-full bg-white'>
      <Allotment.Pane minSize={minSize} preferredSize={defaultSize} className='bg-white'>
        {childrenArray[0]}
      </Allotment.Pane>
      <Allotment.Pane className='bg-white'>{childrenArray[1]}</Allotment.Pane>
    </Allotment>
  );
};
