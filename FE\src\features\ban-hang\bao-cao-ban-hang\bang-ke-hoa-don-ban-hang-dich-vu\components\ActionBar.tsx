import { Pin, RefreshCw, Search, Sheet } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  fromDate?: string | Date;
  toDate?: string | Date;
  className?: string;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  fromDate,
  toDate,
  className
}) => {
  // Create date range display
  const getDateRangeDisplay = () => {
    if (!fromDate || !toDate) return '';

    const formattedFromDate = fromDate instanceof Date ? fromDate.toISOString().split('T')[0] : fromDate;
    const formattedToDate = toDate instanceof Date ? toDate.toISOString().split('T')[0] : toDate;

    if (formattedFromDate && formattedToDate) {
      return `Từ ngày ${formattedFromDate} đến ngày ${formattedToDate}`;
    }

    return '';
  };

  const dateRangeText = getDateRangeDisplay();
  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div className='flex flex-col'>
          <h1 className='text-xl font-bold'>Bảng kê hóa đơn bán hàng - dịch vụ</h1>
          {dateRangeText && (
            <p className='mt-1 text-sm text-gray-600'>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              {dateRangeText}
            </p>
          )}
        </div>
      }
    >
      {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />}

      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='destructive' />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
      )}
      {onExportDataClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};
