import React from 'react';
import { dateRangeOptions, calculateDateRange, formatDate } from './utils/dateRangeUtils';
import { calculateMaxLabelWidth } from './utils/calculateMaxLabelWidth';
import { RenderField } from './fields';
import { FieldConfig } from './types';

// Grid renderer function
export const renderFieldsGrid = (
  fields: FieldConfig[],
  isViewMode: boolean,
  isMobile: boolean,
  tabColumns?: number
) => {
  if (!fields || fields.length === 0) return null;

  const maxLabelWidth = calculateMaxLabelWidth(fields);
  const columns =
    tabColumns || Math.max(...fields.map(f => (f.gridPosition?.col || 0) + (f.gridPosition?.colSpan || 1)));

  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: isMobile ? '1fr' : `repeat(${columns}, 1fr)`,
        gap: '0px',
        padding: '0px'
      }}
    >
      {fields.map(field => {
        const colSpan =
          field.type === 'table' && !field.gridPosition?.colSpan ? columns : field.gridPosition?.colSpan || 1;

        const style: React.CSSProperties = isMobile
          ? {
              gridColumn: '1'
            }
          : {
              gridColumn: field.gridPosition
                ? `${field.gridPosition.col + 1} / span ${colSpan}`
                : field.type === 'table'
                  ? `1 / span ${columns}`
                  : 'auto',
              gridRow: field.gridPosition
                ? `${field.gridPosition.row + 1} / span ${field.gridPosition.rowSpan || 1}`
                : 'auto'
            };

        return (
          <div key={field.key} style={style}>
            <RenderField field={field} maxLabelWidth={maxLabelWidth} isViewMode={isViewMode} />
          </div>
        );
      })}
    </div>
  );
};

// For backward compatibility
export const renderField = (field: FieldConfig, maxLabelWidth: number, isViewMode: boolean) => {
  return <RenderField field={field} maxLabelWidth={maxLabelWidth} isViewMode={isViewMode} />;
};

export * from './types';
export * from './components/FormRow';
export * from './components/InputFieldWrapper';
export * from './inputs/StyledInput';
export * from './inputs/StyledSelect';
export * from './inputs/StyledDateInput';
export * from './inputs/StyledTextArea';
export * from './inputs/StyledFileInput';
export * from './utils/calculateMaxLabelWidth';
export * from './utils/dateRangeUtils';
export * from './fields';

export default {
  renderField,
  renderFieldsGrid,
  calculateMaxLabelWidth,
  dateRangeOptions,
  calculateDateRange,
  formatDate,
  RenderField
};
