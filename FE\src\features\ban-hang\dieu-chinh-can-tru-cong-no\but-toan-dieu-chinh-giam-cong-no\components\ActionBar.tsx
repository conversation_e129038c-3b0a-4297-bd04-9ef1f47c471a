import { <PERSON><PERSON>, <PERSON>cil, Plus, RefreshCw, Trash, Search } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
  onSearch?: () => void;
}

export default function ActionBar({ onAdd, onEdit, onDelete, onCopy, onSearch }: ActionBarProps) {
  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Bút toán điều chỉnh giảm công nợ</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAdd} variant='primary' />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEdit} />
      <AritoActionButton title='Xóa' icon={Trash} onClick={onDelete} />
      <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopy} />
      <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearch} />

      <AritoMenuButton
        items={[
          {
            title: 'Refresh',
            icon: <RefreshCw className='h-4 w-4' />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'In nhiều',
            icon: <AritoIcon icon={17} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: () => {},
            group: 2
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: () => {},
            group: 2
          }
        ]}
      />
    </AritoActionBar>
  );
}
