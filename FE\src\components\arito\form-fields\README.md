# Arito Form Fields

A modular form field system for building dynamic forms with various input types.

## Structure

```
form-fields/
├── components/           # Common UI components
│   ├── FormRow.tsx
│   └── InputFieldWrapper.tsx
├── fields/              # Individual field components
│   ├── TextField.tsx
│   ├── SelectField.tsx
│   ├── CheckboxField.tsx
│   ├── DateField.tsx
│   ├── DateRangeField.tsx
│   ├── TextAreaField.tsx
│   ├── FileField.tsx
│   ├── SearchField.tsx
│   └── index.tsx
├── inputs/              # Base styled inputs
│   ├── StyledInput.tsx
│   ├── StyledSelect.tsx
│   ├── StyledDateInput.tsx
│   ├── StyledTextArea.tsx
│   └── StyledFileInput.tsx
├── utils/               # Utility functions
│   ├── calculateMaxLabelWidth.ts
│   └── dateRangeUtils.ts
├── types/              # TypeScript type definitions
│   └── index.ts
└── index.tsx           # Main entry point
```

## Usage

### Basic Usage

```tsx
import { renderField, renderFieldsGrid } from "@/components/arito/form-fields";

// Single field
const field = {
  key: "name",
  label: "Name",
  type: "text",
  required: true,
};

<RenderField
  field={field}
  maxLabelWidth={120}
  isViewMode={false}
/>;

// Grid of fields
const fields = [
  {
    key: "name",
    label: "Name",
    type: "text",
    required: true,
    gridPosition: { row: 0, col: 0 }
  },
  {
    key: "email",
    label: "Email",
    type: "text",
    required: true,
    gridPosition: { row: 0, col: 1 }
  }
];

<FormProvider {...methods}>
  {renderFieldsGrid(fields, false, false, 2)}
</FormProvider>
```

### Supported Field Types

- text
- number
- select
- checkbox
- search
- barcode
- password
- date
- daterange
- textarea
- table
- button
- compound
- file

### Field Configuration

Each field accepts a `FieldConfig` object with the following properties:

```typescript
interface FieldConfig {
  key: string;          // Unique identifier
  label: string;        // Field label
  type: string;         // Field type
  required?: boolean;   // Is field required
  placeholder?: string; // Placeholder text
  disabled?: boolean;   // Is field disabled
  minWidth?: string | number; // Minimum width
  // ... additional type-specific properties
}
```

## Development

To add a new field type:

1. Create a new component in `fields/`
2. Add the component to the mapping in `fields/index.tsx`
3. Update the FieldConfig type in `types/index.ts`
4. Export the new component in `index.tsx`

## Styling

All components use styled-components with a consistent theme. Base styles are defined in the individual styled component files in the `inputs/` directory.
