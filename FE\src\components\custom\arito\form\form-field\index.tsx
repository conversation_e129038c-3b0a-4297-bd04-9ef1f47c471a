'use client';

import { Chip, MenuItem, Select, TextField, useMediaQuery, useTheme } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Controller } from 'react-hook-form';
import { useContext } from 'react';
import dayjs from 'dayjs';
import {
  datePickerStyle,
  selectFieldStyle,
  selectMenuPaperStyle,
  textFieldInputStyle,
  numberInputStyle
} from './styles';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import { useFormFieldHandlers } from './hooks/useFormFieldHandlers';
import { Checkbox } from '@/components/ui/checkbox';
import { AritoFormContext } from '../../index';
import { AritoFormFieldProps } from '../types';
import { Label } from '@/components/ui/label';
import { SearchField } from './components';
import { cn } from '@/lib/utils';

export const FormField = ({
  label,
  value,
  onValueChange,
  name,
  type = 'text',
  disabled = false,
  options,
  error,
  className,
  columns,
  searchEndpoint,
  searchResultLabelKey,
  searchResultValueKey,
  searchColumns,
  defaultSearchColumn,
  displayRelatedField,
  defaultValue = '',
  placeholder,
  labelClassName = 'shrink-0',
  inputClassName,
  dialogTitle,
  hideRowNumberColumn,
  actionButtons,
  headerFields,
  tableActionButtons,
  tabs
}: AritoFormFieldProps) => {
  const { control, errors, isViewMode } = useContext(AritoFormContext);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const fieldError = error || errors?.[name]?.message;

  const {
    searchDialogOpen,
    setSearchDialogOpen,
    searchQuery,
    searchResults,
    isLoading,
    selectedSearchResult,
    setSelectedSearchResult,
    displayText,
    setDisplayText,
    relatedFieldValue,
    setRelatedFieldValue,
    isFullScreen,
    handleSearchClick,
    handleSearchQueryChange,
    handleSelectSearchResult,
    handleConfirmSelection,
    toggleFullScreen
  } = useFormFieldHandlers({
    searchEndpoint,
    searchResultLabelKey,
    defaultSearchColumn,
    displayRelatedField,
    isMobile
  });

  const fieldId = name;

  return (
    <div>
      <div className={cn('flex items-center gap-1', className)}>
        {type !== 'checkbox' && !(type === 'table' || !label) && (
          <Label
            htmlFor={fieldId}
            className={cn(`mt-3 flex items-center pr-2 text-left text-sm font-normal sm:mb-0`, labelClassName)}
          >
            {label}
          </Label>
        )}

        <div className={cn('w-full', className)}>
          <Controller
            name={name}
            control={control}
            render={({ field }) => {
              const isSearchField = searchEndpoint && searchEndpoint !== '';
              if (isSearchField) {
                return (
                  <SearchField
                    field={field}
                    label={label}
                    disabled={disabled}
                    fieldId={fieldId}
                    type={type}
                    searchEndpoint={searchEndpoint}
                    searchColumns={searchColumns}
                    searchResultLabelKey={searchResultLabelKey}
                    searchResultValueKey={searchResultValueKey}
                    className={inputClassName}
                    defaultSearchColumn={defaultSearchColumn}
                    dialogTitle={dialogTitle}
                    hideRowNumberColumn={hideRowNumberColumn}
                    actionButtons={actionButtons}
                    headerFields={headerFields}
                    tabs={tabs}
                    displayRelatedField={!!displayRelatedField}
                    relatedFieldValue={relatedFieldValue}
                    setRelatedFieldValue={setRelatedFieldValue}
                    textFieldInputStyle={textFieldInputStyle}
                    searchDialogOpen={searchDialogOpen}
                    setSearchDialogOpen={setSearchDialogOpen}
                    searchQuery={searchQuery}
                    searchResults={searchResults}
                    isLoading={isLoading}
                    selectedSearchResult={selectedSearchResult}
                    setSelectedSearchResult={setSelectedSearchResult}
                    displayText={displayText}
                    setDisplayText={setDisplayText}
                    isFullScreen={isFullScreen}
                    handleSearchClick={handleSearchClick}
                    handleSearchQueryChange={handleSearchQueryChange}
                    handleSelectSearchResult={handleSelectSearchResult}
                    handleConfirmSelection={handleConfirmSelection}
                    toggleFullScreen={toggleFullScreen}
                    placeholder={placeholder}
                  />
                );
              }

              switch (type) {
                case 'table':
                  return (
                    <AritoInputTable
                      value={field.value ? field.value : []}
                      columns={columns || []}
                      onChange={field.onChange}
                      mode={disabled ? 'view' : 'edit'}
                      tableActionButtons={tableActionButtons}
                    />
                  );
                case 'select':
                  return (
                    <Select
                      {...field}
                      disabled={disabled}
                      size='small'
                      fullWidth
                      id={fieldId}
                      sx={selectFieldStyle}
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            ...selectMenuPaperStyle,
                            maxWidth: '240px',
                            maxHeight: '300px',
                            overflowY: 'auto',
                            '& .MuiMenuItem-root': {
                              whiteSpace: 'normal',
                              wordBreak: 'break-word',
                              padding: '8px 16px',
                              minHeight: 'auto',
                              lineHeight: 1.4
                            }
                          }
                        }
                      }}
                    >
                      {options?.map(option => (
                        <MenuItem
                          key={option.value.toString()}
                          value={option.value}
                          className='whitespace-normal break-words'
                          onClick={() => {
                            onValueChange && onValueChange(option.value);
                          }}
                        >
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  );
                case 'checkbox':
                  return (
                    <div className='flex items-center space-x-2'>
                      <Checkbox
                        id={fieldId}
                        checked={field.value === undefined ? !!value : !!field.value}
                        onCheckedChange={field.onChange || onValueChange}
                        disabled={disabled}
                        className='size-4 rounded-sm border-gray-300'
                      />
                      <Label htmlFor={fieldId} className={cn('text-sm text-black', labelClassName)}>
                        {label}
                      </Label>
                    </div>
                  );
                case 'date':
                  return (
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                      <DatePicker
                        value={field.value ? dayjs(field.value) : null}
                        onChange={(newValue: dayjs.Dayjs | null) => {
                          field.onChange(newValue ? newValue.format('YYYY-MM-DD') : null);
                        }}
                        disabled={disabled}
                        format='DD/MM/YYYY'
                        localeText={{
                          cancelButtonLabel: 'Hủy',
                          toolbarTitle: 'Chọn ngày',
                          todayButtonLabel: 'Hôm nay',
                          previousMonth: 'Tháng trước',
                          nextMonth: 'Tháng sau'
                        }}
                        slotProps={{
                          textField: {
                            size: 'small',
                            fullWidth: true,
                            variant: 'standard',
                            inputProps: { id: fieldId },
                            sx: datePickerStyle
                          }
                        }}
                      />
                    </LocalizationProvider>
                  );
                case 'multiselect':
                  return (
                    <Select
                      {...field}
                      disabled={disabled}
                      size='small'
                      fullWidth
                      multiple
                      id={fieldId}
                      value={field.value || []}
                      sx={selectFieldStyle}
                      renderValue={selected => (
                        <div className='flex flex-wrap gap-1'>
                          {(selected as string[]).map(value => {
                            const label = options?.find(option => option.value === value)?.label;
                            return (
                              <Chip key={value} label={label} size='small' className='m-0.5 h-5 bg-gray-100 text-xs' />
                            );
                          })}
                        </div>
                      )}
                      MenuProps={{
                        PaperProps: {
                          sx: selectMenuPaperStyle
                        }
                      }}
                    >
                      {options?.map(option => (
                        <MenuItem key={option.value.toString()} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  );
                default:
                  return (
                    <TextField
                      {...field}
                      type={type}
                      disabled={disabled}
                      fullWidth
                      size='small'
                      variant='standard'
                      id={fieldId}
                      value={value || field.value || ''}
                      onChange={onValueChange || field.onChange}
                      placeholder={placeholder}
                      sx={{
                        ...numberInputStyle(type),
                        '& .MuiInputBase-input::placeholder': {
                          opacity: 1,
                          color: 'rgba(0, 0, 0, 0.6)'
                        }
                      }}
                    />
                  );
              }
            }}
          />
        </div>
      </div>

      {fieldError && <span className='mt-1 block text-xs text-red-500'>{fieldError}</span>}
    </div>
  );
};
