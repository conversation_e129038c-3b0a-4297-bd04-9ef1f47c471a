import { Dialog } from '@mui/material';
import { useState } from 'react';
import { SearchDialogProps } from '../../types';
import { DialogContent } from './content';
import { DialogActions } from './actions';
import { DialogHeader } from './header';

export const SearchDialog = ({
  open,
  onClose,
  isFullScreen,
  isMobile,
  title,
  searchQuery,
  onSearchQueryChange,
  isLoading,
  searchResults,
  selectedResult,
  searchColumns,
  searchResultLabelKey,
  searchResultValueKey,
  onResultClick,
  onResultDoubleClick,
  onConfirm
}: SearchDialogProps) => {
  const [localIsFullScreen, setLocalIsFullScreen] = useState(isFullScreen);

  const handleToggleFullScreen = () => {
    setLocalIsFullScreen(!localIsFullScreen);
  };

  return (
    <Dialog
      open={open}
      onClose={() => {}} // Empty function to disable closing on backdrop click
      disableEscapeKeyDown // Disable Esc key to close modal
      fullScreen={isMobile || localIsFullScreen}
      PaperProps={{
        sx: {
          borderRadius: '0px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          overflow: 'hidden',
          ...(isMobile || localIsFullScreen
            ? {
                height: '100%',
                width: '100%',
                maxWidth: '100vw',
                maxHeight: '100vh',
                margin: 0
              }
            : {
                height: '450px',
                width: '850px',
                maxHeight: '80vh',
                maxWidth: '90vw'
              })
        }
      }}
    >
      <DialogHeader
        title={title}
        isFullScreen={localIsFullScreen}
        isMobile={isMobile}
        onToggleFullScreen={handleToggleFullScreen}
      />
      <DialogContent
        searchQuery={searchQuery}
        onSearchQueryChange={onSearchQueryChange}
        isLoading={isLoading}
        searchResults={searchResults}
        searchColumns={searchColumns}
        searchResultLabelKey={searchResultLabelKey}
        searchResultValueKey={searchResultValueKey}
        selectedResult={selectedResult}
        onResultClick={onResultClick}
        onResultDoubleClick={onResultDoubleClick}
      />
      <DialogActions onConfirm={onConfirm} onCancel={onClose} isConfirmDisabled={!selectedResult} />
    </Dialog>
  );
};
