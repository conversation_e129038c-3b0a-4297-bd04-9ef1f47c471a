import { DocumentNumberRangeField } from '@/components/custom/arito/form/search-fields/DocumentNumberRangeField';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns } from '../../cols-definition';
import { AccountModel } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  account?: AccountModel;
  setAccount?: (account: AccountModel) => void;
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ account, setAccount }) => {
  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='space-y-3 p-4'>
        {/* <PERSON><PERSON><PERSON> chứ từ/đến */}
        <div className='flex items-center'>
          <Label className='flex min-w-[150px] items-center pr-2 text-left text-[13px] font-normal sm:mb-0'>
            Ngày c/từ(từ/đến)
          </Label>
          <AritoFormDateRangeDropdown fromDateName='fromDate' toDateName='toDate' />
        </div>

        {/* Số chứ từ/đến */}
        <div className='flex items-center'>
          <DocumentNumberRangeField fromName='voucherNumberStart' toName='voucherNumberEnd' />
        </div>

        {/* Loại xuất */}
        <div className='flex items-center'>
          <Label className='flex min-w-[150px] items-center pr-2 text-left text-[13px] font-normal sm:mb-0'>
            Loại xuất
          </Label>
          <FormField
            name='exportType'
            type='select'
            className='w-[200px]'
            label=''
            options={[
              { value: '0', label: 'Tất cả' },
              { value: '1', label: '1. Theo hóa đơn' },
              { value: '2', label: '2. Theo khách hàng' }
            ]}
          />
        </div>

        {/* Tài khoản nợ */}
        <div className='flex items-center'>
          <Label className='flex min-w-[150px] items-center pr-2 text-left text-[13px] font-normal sm:mb-0'>
            Tài khoản nợ
          </Label>
          <SearchField<AccountModel>
            type='text'
            displayRelatedField='name'
            columnDisplay='code'
            className='w-[11.25rem]'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            value={account?.code || ''}
            relatedFieldValue={account?.name || ''}
            onRowSelection={setAccount}
          />
        </div>

        {/* Diễn giải */}
        <div className='flex items-center'>
          <Label className='flex min-w-[150px] items-center pr-2 text-left text-[13px] font-normal sm:mb-0'>
            Diễn giải
          </Label>
          <FormField name='description' type='text' className='w-96' />
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
