import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const TaxTNCNTab: React.FC<Props> = ({ formMode }) => {
  return (
    <div className='p-4'>
      <div className='flex flex-wrap items-center gap-4'>
        <FormField
          className='flex min-w-[300px] items-center'
          label='Mã số thuế TNCN'
          name='taxCode'
          type='text'
          labelClassName='min-w-[140px]'
          disabled={formMode === 'view'}
        />

        <div className='ml-4 flex items-center gap-4'>
          <FormField
            className='flex items-center gap-2'
            type='checkbox'
            label='Quyết toán thuế TNCN'
            name='isTaxSettlement'
            disabled={formMode === 'view'}
          />

          <FormField
            className='flex items-center gap-2'
            type='checkbox'
            label='Tính thuế TNCN'
            name='isCalculateTax'
            disabled={formMode === 'view'}
          />

          <FormField
            className='flex items-center gap-2'
            type='checkbox'
            label='Tính thế lũy tiến'
            name='isProgressiveTax'
            disabled={formMode === 'view'}
          />

          <FormField
            className='flex items-center gap-2'
            type='checkbox'
            label=''
            name='empty'
            disabled={formMode === 'view'}
          />
        </div>
      </div>

      <div className='mt-2 flex flex-wrap items-center'>
        <div className='flex min-w-[160px] items-center'>
          <span className='min-w-[120px] text-sm'>Hợp đồng lao động</span>
        </div>

        <FormField
          className='flex items-center gap-2'
          type='checkbox'
          label='Quyết toán dưới 12 tháng (CN nước ngoài)'
          name='isUnder12MonthSettlement'
          disabled={formMode === 'view'}
        />
      </div>

      <FormField
        className='flex max-w-[443px] items-center justify-between'
        label='Đối tượng thuế'
        name='taxObject'
        type='select'
        labelClassName='min-w-[140px]'
        disabled={formMode === 'view'}
        options={[
          { label: '0. Không cư trú', value: 'non_resident' },
          { label: '1. Cư trú', value: 'resident' }
        ]}
      />

      <div className='flex flex-wrap items-center gap-4'>
        <FormField
          className='flex min-w-[300px] items-center'
          label='Loại giấy tờ'
          name='idType'
          type='select'
          labelClassName='min-w-[140px]'
          disabled={formMode === 'view'}
          options={[
            { label: '1. CMND', value: 'cmnd' },
            { label: '2. Thẻ CCCD', value: 'cccd' },
            { label: '3. Hộ chiếu', value: 'passport' },
            { label: '4. Giấy khai sinh', value: 'birth_certificate' },
            { label: '5. Khác', value: 'other' }
          ]}
        />

        <FormField
          className='flex flex-1 items-center'
          label='Số giấy tờ'
          name='idNumber'
          type='text'
          labelClassName='min-w-[100px]'
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  );
};

export default TaxTNCNTab;
