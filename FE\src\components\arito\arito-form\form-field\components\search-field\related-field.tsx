import { Typography } from '@mui/material';
import { RelatedFieldDisplayProps } from '../../types';

export const RelatedFieldDisplay = ({ value, isMobile }: RelatedFieldDisplayProps) => {
  if (!value) return null;

  return (
    <Typography
      variant='body2'
      sx={{
        marginLeft: { xs: 0, sm: '12px' },
        marginTop: { xs: '4px', sm: 0 },
        fontSize: '14px',
        color: '#374151',
        maxWidth: { xs: '100%', sm: '65%' },
        flexGrow: 1,
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        textAlign: { xs: 'left', sm: 'left' },
        width: '100%'
      }}
    >
      {value}
    </Typography>
  );
};
