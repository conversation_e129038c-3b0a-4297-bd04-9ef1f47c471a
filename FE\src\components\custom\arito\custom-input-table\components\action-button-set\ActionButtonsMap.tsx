import { IconButton, Tooltip, Button } from '@mui/material';
import React from 'react';
import { ButtonType } from '@/components/custom/arito/custom-input-table/types';
import AritoIcon from '@/components/custom/arito/icon';
import { buttonSx, labelButtonSx } from './styles';

// Re-export ButtonType for consistency
export type ActionButtonType = ButtonType;

interface ActionButtonProps {
  onAddRow?: () => void;
  onDeleteRow?: () => void;
  onCopyRow?: () => void;
  onPasteRow?: () => void;
  onMoveRow?: (direction: 'up' | 'down') => void;
  onExport?: () => void;
  onPin?: () => void;
  onViewInventory?: () => void;
  onViewReceipt?: () => void;
  onDownloadExcel?: () => void;
  onUploadExcel?: () => void;
  onSaveAllocation?: () => void;
  onDeleteAllocation?: () => void;
}

// Using labelButtonSx from styles.ts

export const getActionButton = (type: ButtonType, props: ActionButtonProps): React.ReactNode => {
  const {
    onAddRow,
    onDeleteRow,
    onCopyRow,
    onPasteRow,
    onMoveRow,
    onExport,
    onPin,
    onDownloadExcel,
    onUploadExcel,
    onViewInventory,
    onViewReceipt,
    onSaveAllocation,
    onDeleteAllocation
  } = props;

  switch (type) {
    case 'add':
      return (
        <Tooltip title='Thêm dòng mới' arrow>
          <IconButton size='small' onClick={onAddRow} sx={buttonSx} disabled={!onAddRow}>
            <AritoIcon icon={7} />
          </IconButton>
        </Tooltip>
      );

    case 'delete':
      return (
        <Tooltip title='Xóa dòng đã chọn' arrow>
          <span>
            <IconButton size='small' onClick={onDeleteRow} disabled={!onDeleteRow} sx={buttonSx}>
              <AritoIcon icon={8} />
            </IconButton>
          </span>
        </Tooltip>
      );

    case 'copy':
      return (
        <Tooltip title='Sao chép dòng' arrow>
          <span>
            <IconButton size='small' onClick={onCopyRow} disabled={!onCopyRow} sx={buttonSx}>
              <AritoIcon icon={11} />
            </IconButton>
          </span>
        </Tooltip>
      );

    case 'paste':
      return (
        <Tooltip title='Dán vào dòng' arrow>
          <span>
            <IconButton size='small' onClick={onPasteRow} disabled={!onPasteRow} sx={buttonSx}>
              <AritoIcon icon={4} />
            </IconButton>
          </span>
        </Tooltip>
      );

    case 'moveUp':
      return (
        <Tooltip title='Di chuyển lên' arrow>
          <span>
            <IconButton size='small' onClick={() => onMoveRow && onMoveRow('up')} disabled={!onMoveRow} sx={buttonSx}>
              <AritoIcon icon={3} />
            </IconButton>
          </span>
        </Tooltip>
      );

    case 'moveDown':
      return (
        <Tooltip title='Di chuyển xuống' arrow>
          <span>
            <IconButton size='small' onClick={() => onMoveRow && onMoveRow('down')} disabled={!onMoveRow} sx={buttonSx}>
              <AritoIcon icon={30} />
            </IconButton>
          </span>
        </Tooltip>
      );

    case 'export':
      return (
        <Tooltip title='Kết xuất dữ liệu' arrow>
          <span>
            <IconButton size='small' onClick={onExport} disabled={!onExport} sx={buttonSx}>
              <AritoIcon icon={18} />
            </IconButton>
          </span>
        </Tooltip>
      );

    case 'pin':
      return (
        <Tooltip title='Cố định cột' arrow>
          <span>
            <IconButton size='small' onClick={onPin} disabled={!onPin} sx={buttonSx}>
              <AritoIcon icon={16} />
            </IconButton>
          </span>
        </Tooltip>
      );

    case 'refresh':
      return (
        <Tooltip title='Refresh' arrow>
          <span>
            <IconButton size='small' onClick={() => {}} disabled={false} sx={buttonSx}>
              <AritoIcon icon={15} />
            </IconButton>
          </span>
        </Tooltip>
      );

    case 'edit':
      return (
        <Tooltip title='Chỉnh sửa' arrow>
          <span>
            <IconButton size='small' onClick={() => {}} disabled={false} sx={buttonSx}>
              <AritoIcon icon={864} />
            </IconButton>
          </span>
        </Tooltip>
      );

    case 'detail':
      return (
        <Tooltip title='Tổng hợp chi tiết' arrow>
          <span>
            <IconButton size='small' onClick={() => {}} disabled={false} sx={buttonSx}>
              <AritoIcon icon={770} />
            </IconButton>
            <label className='text-xs font-semibold text-black'>Tổng hợp chi tiết</label>
          </span>
        </Tooltip>
      );

    case 'viewInventory':
      return (
        <Button size='small' onClick={onViewInventory} disabled={!onViewInventory} sx={labelButtonSx}>
          <AritoIcon icon={553} />
          Xem tồn kho
        </Button>
      );

    case 'viewReceipt':
      return (
        <Button size='small' onClick={onViewReceipt} disabled={!onViewReceipt} sx={labelButtonSx}>
          <AritoIcon icon={140} />
          Xem phiếu nhập
        </Button>
      );

    case 'downloadExcel':
      return (
        <Tooltip title='Tải mẫu Excel' arrow>
          <span>
            <IconButton size='small' onClick={onDownloadExcel} disabled={!onDownloadExcel} sx={buttonSx}>
              <AritoIcon icon={28} />
            </IconButton>
          </span>
        </Tooltip>
      );

    case 'uploadExcel':
      return (
        <Tooltip title='Lấy dữ liệu từ Excel' arrow>
          <span>
            <IconButton size='small' onClick={onUploadExcel} disabled={!onUploadExcel} sx={buttonSx}>
              <AritoIcon icon={29} />
            </IconButton>
          </span>
        </Tooltip>
      );

    case 'saveAllocation':
      return (
        <Button size='small' onClick={onSaveAllocation} disabled={!onSaveAllocation} sx={labelButtonSx}>
          <AritoIcon icon={294} />
          Lưu phân bổ
        </Button>
      );

    case 'deleteAllocation':
      return (
        <Button size='small' onClick={onDeleteAllocation} disabled={!onDeleteAllocation} sx={labelButtonSx}>
          <AritoIcon icon={8} />
          Xóa phân bổ
        </Button>
      );

    case 'watch':
      return (
        <Tooltip title='Xem (Alt + W)' arrow>
          <span>
            <IconButton size='small' onClick={onPasteRow} disabled={!onPasteRow} sx={buttonSx}>
              <AritoIcon icon={19} />
            </IconButton>
          </span>
        </Tooltip>
      );

    default:
      return null;
  }
};
