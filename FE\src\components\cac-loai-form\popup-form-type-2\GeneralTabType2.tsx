import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  formMode: 'add' | 'edit' | 'view';
}

const GeneralTabType2 = ({ formMode }: Props) => {
  return (
    <div className='grid grid-cols-1 gap-x-8 space-y-2 p-4 lg:grid-cols-1 lg:space-y-0'>
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Tên ngắn'
        name='defaultAccount'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Tên ngắn khác'
        name='accountPayable'
        type='text'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
      />
      <div className='grid grid-cols-1 gap-x-20 lg:grid-cols-2'>
        <FormField
          className='mt-2 items-start gap-y-1 sm:items-center'
          label='Tài khoản sổ cái'
          name='phoneNumber'
          labelClassName='min-w-[100px]'
          type='checkbox'
          disabled={formMode === 'view'}
        />
        <FormField
          className='items-start gap-y-1 sm:items-center'
          label='Ngoại tệ gốc'
          name='fax'
          labelClassName='min-w-[100px]'
          type='select'
          disabled={formMode === 'view'}
          options={[
            { value: 'usd', label: 'USD' },
            { value: 'eur', label: 'EUR' },
            { value: 'vnd', label: 'VND' },
            { value: 'jpy', label: 'JPY' },
            { value: 'gbp', label: 'GBP' },
            { value: 'aud', label: 'AUD' },
            { value: 'cad', label: 'CAD' },
            { value: 'chf', label: 'CHF' },
            { value: 'cny', label: 'CNY' },
            { value: 'sek', label: 'SEK' },
            { value: 'nzd', label: 'NZD' }
          ]}
        />
      </div>
      <FormField
        className='w-[400px] items-start gap-y-1 sm:items-center'
        label='Tk theo dõi công nợ'
        name='paymentMethod'
        type='select'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
        options={[
          { value: 'active', label: '0. Không theo dõi công nợ' },
          { value: 'inactive', label: '2. công nợ phải thu' },
          { value: 'inactive', label: '3. công nợ phải trả' },
          { value: 'inactive', label: '3. công nợ khác' }
        ]}
      />
      <FormField
        className='w-[400px] items-start gap-y-1 sm:items-center'
        label='Phân loại tài khoản'
        name='group1'
        labelClassName='min-w-[100px]'
        type='select'
        disabled={formMode === 'view'}
        options={[
          { value: 'KPL', label: 'KPL. Không phân loại' },
          { value: 'TA', label: 'TA. Thuế' },
          { value: 'TI', label: 'TI. Tiền' },
          { value: 'CN', label: 'CN. Công nợ' },
          { value: 'NH', label: 'NH. Ngân hàng' },
          { value: 'VV', label: 'VV. Vụ việc' },
          { value: 'HD', label: 'HD. Hợp đồng' },
          { value: 'KU', label: 'KU. Khế ước' },
          { value: 'MR1', label: 'MR1. Mở rộng 1' },
          { value: 'MR2', label: 'MR2. Mở rộng 2' },
          { value: 'MR3', label: 'MR3. Mở rộng 3' }
        ]}
      />
      <FormField
        className='w-[400px] items-start gap-y-1 sm:items-center'
        label='Pp tính tggs nợ'
        name='group1'
        labelClassName='min-w-[100px]'
        type='select'
        disabled={formMode === 'view'}
        options={[
          { value: 'active', label: '0. Không tính chênh lệch' },
          { value: 'inactive', label: '1. Trung bình tháng' },
          { value: 'inactive', label: '2. Đích danh' },
          { value: 'inactive', label: '3. Trung bình di động' }
        ]}
      />
      <FormField
        className='w-[400px] items-start gap-y-1 sm:items-center'
        label='Pp tính tggs có'
        name='group1'
        labelClassName='min-w-[100px]'
        type='select'
        disabled={formMode === 'view'}
        options={[
          { value: 'active', label: '0. Không tính chênh lệch' },
          { value: 'inactive', label: '1. Trung bình tháng' },
          { value: 'inactive', label: '2. Đích danh' },
          { value: 'inactive', label: '3. Trung bình di động' }
        ]}
      />
      <FormField
        className='items-start gap-y-1 sm:items-center'
        label='Ghi chú'
        name='note'
        labelClassName='min-w-[100px]'
        type='text'
        disabled={formMode === 'view'}
      />
      <FormField
        className='w-[400px] items-start gap-y-1 sm:items-center'
        label='Trạng thái'
        name='status'
        type='select'
        labelClassName='min-w-[100px]'
        disabled={formMode === 'view'}
        options={[
          { value: 'active', label: '1. Còn sử dụng' },
          { value: 'inactive', label: '0. Không sử dụng' }
        ]}
      />
    </div>
  );
};

export default GeneralTabType2;
