import React from 'react';
import MoreInfoTabType1 from '@/components/cac-loai-form/popup-form-type-1/MoreInfoTabType1';
import { BottomBar } from '@/components/custom/arito/form/bottom-bar';
import AritoModal from '@/components/custom/arito/modal';
import AritoIcon from '@/components/custom/arito/icon';
import { AritoForm } from '@/components/custom/arito';
import BasicInfoTab from './BasicInfoTab';
import GeneralTab from './GeneralTab';
import OtherTab from './OtherTab';
import HDDTTab from './HDDTTab';

interface Props {
  showTaxCodePopupForm: boolean;
  setShowTaxCodePopupForm: (value: boolean) => void;
  formMode: 'add' | 'edit' | 'view';
  currentObj: any | null;
}

const TaxCodePopUpForm = ({ showTaxCodePopupForm, setShowTaxCodePopupForm, formMode, currentObj }: Props) => {
  const handleSubmit = (data: any) => {
    setShowTaxCodePopupForm(false);
  };
  return (
    <AritoModal
      open={showTaxCodePopupForm}
      onClose={() => {
        setShowTaxCodePopupForm(false);
      }}
      title={'Mới'}
      titleIcon={<AritoIcon icon={699} />}
      maxWidth='xl'
    >
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='max-h-[calc(100vh-120px)] flex-1 overflow-auto'>
          <AritoForm<any>
            mode={formMode}
            initialData={currentObj || {}}
            hasAritoActionBar={false}
            className='!static !w-full'
            onSubmit={handleSubmit}
            onClose={() => {
              setShowTaxCodePopupForm(false);
            }}
            headerFields={<BasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'thong-tin-chung',
                label: 'Thông tin chung',
                component: <GeneralTab formMode={formMode} />
              },
              {
                id: 'khac',
                label: 'Khác',
                component: <OtherTab formMode={formMode} />
              },
              {
                id: 'thong-tin-them',
                label: 'Thông tin thêm',
                component: <MoreInfoTabType1 formMode={formMode} />
              },
              {
                id: 'hddt',
                label: 'HĐĐT(1.Có)',
                component: <HDDTTab formMode={formMode} />
              }
            ]}
          />
        </div>

        <BottomBar
          mode={formMode}
          onSubmit={() => {}}
          onClose={() => {
            setShowTaxCodePopupForm(false);
          }}
        />
      </div>
    </AritoModal>
  );
};

export default TaxCodePopUpForm;
