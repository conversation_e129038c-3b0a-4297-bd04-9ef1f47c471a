import { useState } from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';
import { FormField } from '@/components/custom/arito/form/form-field';
import { accountSearchColumns } from '@/constants/search-columns';
import type { ViTriKho } from '@/types/schemas/vi-tri-kho.type';
import type { TaiKhoan, Lo } from '@/types/schemas';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

// Transaction search columns
const transactionSearchColumns = [
  { field: 'ma_nx', headerName: 'Mã giao dịch', width: 120 },
  { field: 'ten_nx', headerName: 'Tên giao dịch', width: 200 },
  { field: 'ten_nx2', headerName: 'Tên khác', width: 200 }
];

// Lot search columns
const lotSearchColumns = [
  { field: 'ma_lo', headerName: 'Mã lô', width: 120 },
  { field: 'ten_lo', headerName: 'Tên lô', width: 200 }
];

// Location search columns
const locationSearchColumns = [
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', width: 120 },
  { field: 'ten_vi_tri', headerName: 'Tên vị trí', width: 200 }
];

interface OtherTabProps {
  searchFieldStates: {
    transactionCode: any | null;
    setTransactionCode: (transactionCode: any | null) => void;
    itemAccount: TaiKhoan | null;
    setItemAccount: (itemAccount: TaiKhoan | null) => void;
    revenueAccount: TaiKhoan | null;
    setRevenueAccount: (revenueAccount: TaiKhoan | null) => void;
    costAccount: TaiKhoan | null;
    setCostAccount: (costAccount: TaiKhoan | null) => void;
    batchCode: Lo | null;
    setBatchCode: (batchCode: Lo | null) => void;
    locationCode: ViTriKho | null;
    setLocationCode: (locationCode: ViTriKho | null) => void;
  };
}

const OtherTab: React.FC<OtherTabProps> = ({ searchFieldStates }) => {
  // Dialog state
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);
  const [saveAnalysisTemplateDialogOpen, setSaveAnalysisTemplateDialogOpen] = useState(false);

  // Extract search field states from props
  const {
    transactionCode,
    setTransactionCode,
    itemAccount,
    setItemAccount,
    revenueAccount,
    setRevenueAccount,
    costAccount,
    setCostAccount,
    batchCode,
    setBatchCode,
    locationCode,
    setLocationCode
  } = searchFieldStates;

  const handleSaveFilterTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving filter template:', data);
    // Here you would typically save the template to your backend
    setSaveFilterTemplateDialogOpen(false);
  };

  const handleSaveAnalysisTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving analysis template:', data);
    // Here you would typically save the template to your backend
    setSaveAnalysisTemplateDialogOpen(false);
  };

  return (
    <div className='h-96 space-y-2 overflow-y-auto p-4'>
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Nhóm theo:</Label>
        <FormField
          name='group_by'
          type='select'
          options={[
            { value: '', label: 'Không phân nhóm' },
            { value: 'type', label: 'Loại vật tư' },
            { value: 'group1', label: 'Nhóm vật tư 1' },
            { value: 'group2', label: 'Nhóm vật tư 2' },
            { value: 'group3', label: 'Nhóm vật tư 3' },
            { value: 'account', label: 'Tài khoản vật tư' }
          ]}
        />
      </div>
      <div className='flex flex-col space-y-2'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã giao dịch:</Label>
          <SearchField<any>
            searchEndpoint={`/${QUERY_KEYS.DANH_MUC_NHAP_XUAT}/`}
            searchColumns={transactionSearchColumns}
            dialogTitle='Danh mục giao dịch'
            columnDisplay='ma_nx'
            displayRelatedField='ten_nx'
            value={transactionCode?.ma_nx || ''}
            onRowSelection={setTransactionCode}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản vật tư:</Label>
          <SearchField<TaiKhoan>
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            value={itemAccount?.code || ''}
            onRowSelection={setItemAccount}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản doanh thu:</Label>
          <SearchField<TaiKhoan>
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản doanh thu'
            columnDisplay='code'
            displayRelatedField='name'
            value={revenueAccount?.code || ''}
            onRowSelection={setRevenueAccount}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản giá vốn:</Label>
          <SearchField<TaiKhoan>
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản giá vốn'
            columnDisplay='code'
            displayRelatedField='name'
            value={costAccount?.code || ''}
            onRowSelection={setCostAccount}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã lô:</Label>
          <SearchField<Lo>
            searchEndpoint={`/${QUERY_KEYS.LO}/`}
            searchColumns={lotSearchColumns}
            dialogTitle='Danh mục lô'
            columnDisplay='ma_lo'
            displayRelatedField='ten_lo'
            value={batchCode?.ma_lo || ''}
            onRowSelection={setBatchCode}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vị trí:</Label>
          <SearchField<ViTriKho>
            searchEndpoint={`/${QUERY_KEYS.VI_TRI_KHO_HANG}/`}
            searchColumns={locationSearchColumns}
            dialogTitle='Danh mục vị trí kho'
            columnDisplay='ma_vi_tri'
            displayRelatedField='ten_vi_tri'
            value={locationCode?.ma_vi_tri || ''}
            onRowSelection={setLocationCode}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Diễn giải:</Label>
          <FormField name='dien_giai' label='' type='text' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu lọc báo cáo:</Label>
          <div className='flex flex-1 items-center gap-1'>
            <div className='w-[200px]'>
              <FormField
                name='reportFilterTemplate'
                label=''
                type='select'
                options={[{ value: 'user_filter', label: 'Người dùng tự lọc' }]}
              />
            </div>
            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => setSaveFilterTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu phân tích DL:</Label>
          <div className='flex flex-1 items-center gap-1'>
            <div className='w-[200px]'>
              <FormField
                name='data_analysis_struct'
                label=''
                type='select'
                options={[{ value: '', label: 'Không phân tích' }]}
              />
            </div>
            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={873}
                items={[
                  {
                    value: 'save_new',
                    label: 'Tạo mới mẫu phân tích',
                    icon: 7,
                    onClick: () => setSaveAnalysisTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />

      {/* Save Analysis Template Dialog */}
      <SaveTemplateDialog
        open={saveAnalysisTemplateDialogOpen}
        onClose={() => setSaveAnalysisTemplateDialogOpen(false)}
        onSave={handleSaveAnalysisTemplate}
        templateType='analysis'
      />
    </div>
  );
};

export default OtherTab;
