import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface PaymentTermSelectFieldProps {
  name?: string;
  label?: string;
  labelClassName?: string;
  className?: string;
  inputClassName?: string;
  disabled?: boolean;
  formMode?: 'add' | 'edit' | 'view';
}

export const PaymentTermSelectField: React.FC<PaymentTermSelectFieldProps> = ({
  name = 'paymentTerm',
  label = 'Hạn thanh toán',
  labelClassName,
  className = 'flex items-center',
  inputClassName = 'w-full',
  disabled = false,
  formMode = 'add'
}) => {
  return (
    <div className={className}>
      <Label className={cn('mt-3 flex items-center text-left text-sm font-normal sm:mb-0', labelClassName)}>
        {label}
      </Label>
      <FormField
        name={name}
        type='select'
        className={inputClassName}
        label=''
        disabled={disabled || formMode === 'view'}
        options={[
          { value: 'tra-truoc', label: 'Trả trước' },
          { value: 'tra-trong-vong-07-ngay', label: 'Trả trong vòng 07 ngày' },
          { value: 'tra-trong-vong-10-ngay', label: 'Trả trong vòng 10 ngày' },
          { value: 'tra-trong-vong-120-ngay', label: 'Trả trong vòng 120 ngày' },
          { value: 'tra-trong-vong-15-ngay', label: 'Trả trong vòng 15 ngày' },
          {
            value: 'thanh-toan-15-tra-truoc-85-sau-chung-tu',
            label: 'Thanh toán 15% trả trước, 85% sau khi nhận được chúng từ copy'
          },
          {
            value: 'thanh-toan-15-tra-truoc-85-trong-30-ngay',
            label: 'Thanh toán 15% trả trước, 85% thanh toán trong vòng 30 ngày kể từ ngày hóa đơn'
          },
          { value: 'tra-trong-vong-180-ngay', label: 'Trả trong vòng 180 ngày' },
          { value: 'tra-trong-vong-20-ngay', label: 'Trả trong vòng 20 ngày' },
          { value: 'tra-trong-vong-30-ngay', label: 'Trả trong vòng 30 ngày' }
        ]}
      />
    </div>
  );
};

export default PaymentTermSelectField;
