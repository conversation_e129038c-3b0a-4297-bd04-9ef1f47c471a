import React from 'react';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

// Options for the "Báo cáo theo" dropdown (without "Không nhóm" option)
const reportByOptions = [
  { value: 'customer', label: 'Khách hàng' },
  { value: 'customerGroup1', label: 'Nhóm khách 1' },
  { value: 'customerGroup2', label: 'Nhóm khách 2' },
  { value: 'customerGroup3', label: 'Nhóm khách 3' },
  { value: 'product', label: 'Vật tư' },
  { value: 'productGroup1', label: 'Nhóm vật tư 1' },
  { value: 'productGroup2', label: 'Nhóm vật tư 2' },
  { value: 'productGroup3', label: '<PERSON><PERSON><PERSON><PERSON> vật tư 3' },
  { value: 'unit', label: 'Đơn vị' },
  { value: 'department', label: 'Bộ phận' },
  { value: 'project', label: '<PERSON><PERSON> việc' },
  { value: 'contract', label: 'Hợp đồng' },
  { value: 'agreement', label: 'Khế ước' },
  { value: 'fee', label: 'Phí' },
  { value: 'product', label: 'Sản phẩm' },
  { value: 'productionOrder', label: 'Lệnh sản xuất' },
  { value: 'employee', label: 'Nhân viên bán hàng' }
];
const analysisByOptions = [
  { value: 'day', label: 'Ngày' },
  { value: 'week', label: 'Tuần' },
  { value: 'month', label: 'Tháng' },
  { value: 'quarter', label: 'Quý' },
  { value: 'half-year', label: '6 tháng' },
  { value: 'year', label: 'Năm' }
];

// Options for the "Nhóm theo" dropdown (includes "Không nhóm" option)
const groupByOptions = [{ value: 'no-group', label: 'Không nhóm' }, ...reportByOptions];

const BasicInfo: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Phân tích theo:</Label>
          <div>
            <FormField name='analysisBy' type='select' options={analysisByOptions} label='' className='w-48' />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày bắt đầu:</Label>
          <div className='flex items-center gap-2'>
            <FormField name='fromDate' type='date' label='' className='w-48' />
            <Input name='toDate' className='w-48 border-0 border-b py-1.5' readOnly />
          </div>
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số kỳ phân tích</Label>
          <FormField name='reportBy' type='text' label='' className='w-48' />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Báo cáo theo:</Label>
          <div>
            <FormField name='reportBy' type='select' options={reportByOptions} label='' className='w-48' />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm theo:</Label>
          <div>
            <div className='flex items-center justify-between'>
              <div className='flex-1'>
                <FormField name='groupBy' type='select' options={groupByOptions} label='' className='w-48' />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
