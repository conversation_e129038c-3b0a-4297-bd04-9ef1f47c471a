import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const GeneralTab: React.FC = () => {
  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-2'>
        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mã nhân viên:</Label>
            <FormField
              name='employeeCode'
              label=''
              type='text'
              searchEndpoint='/api/employees'
              searchResultLabelKey='employeeName'
              searchResultValueKey='employeeCode'
              searchColumns={[
                {
                  field: 'employeeCode',
                  headerName: 'Mã nhân viên',
                  width: 150
                },
                {
                  field: 'employeeName',
                  headerName: 'Tên nhân viên',
                  width: 250
                }
              ]}
            />
          </div>
        </div>

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mã khách hàng:</Label>
            <div className='flex-1'>
              <FormField
                name='customerCode'
                label=''
                type='text'
                searchEndpoint='/api/customers'
                searchResultLabelKey='customerName'
                searchResultValueKey='customerCode'
                searchColumns={[
                  {
                    field: 'customerCode',
                    headerName: 'Mã đối tượng',
                    width: 120
                  },
                  {
                    field: 'customerName',
                    headerName: 'Tên đối tượng',
                    width: 200
                  },
                  {
                    field: 'receivable',
                    headerName: 'Công nợ p/thu',
                    width: 120
                  },
                  { field: 'payable', headerName: 'Công nợ p/trả', width: 120 },
                  { field: 'taxCode', headerName: 'Mã số thuế', width: 120 },
                  { field: 'email', headerName: 'Email', width: 150 },
                  { field: 'phone', headerName: 'Số điện thoại', width: 120 }
                ]}
              />
            </div>
          </div>
        </div>

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Nhóm khách hàng:</Label>
            <div className='flex-1'>
              <div className='grid grid-cols-3 gap-2'>
                <FormField
                  name='customerGroup1'
                  label=''
                  type='text'
                  searchEndpoint='/api/customer-groups'
                  searchResultLabelKey='groupName'
                  searchResultValueKey='groupCode'
                  searchColumns={[
                    { field: 'groupCode', headerName: 'Mã nhóm', width: 120 },
                    { field: 'groupName', headerName: 'Tên nhóm', width: 200 }
                  ]}
                />
                <FormField
                  name='customerGroup2'
                  label=''
                  type='text'
                  searchEndpoint='/api/customer-groups'
                  searchResultLabelKey='groupName'
                  searchResultValueKey='groupCode'
                  searchColumns={[
                    { field: 'groupCode', headerName: 'Mã nhóm', width: 120 },
                    { field: 'groupName', headerName: 'Tên nhóm', width: 200 }
                  ]}
                />
                <FormField
                  name='customerGroup3'
                  label=''
                  type='text'
                  searchEndpoint='/api/customer-groups'
                  searchResultLabelKey='groupName'
                  searchResultValueKey='groupCode'
                  searchColumns={[
                    { field: 'groupCode', headerName: 'Mã nhóm', width: 120 },
                    { field: 'groupName', headerName: 'Tên nhóm', width: 200 }
                  ]}
                />
              </div>
            </div>
          </div>
        </div>

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Khu vực:</Label>
            <div className='flex-1'>
              <FormField
                name='region'
                label=''
                type='text'
                searchEndpoint='/api/regions'
                searchResultLabelKey='regionName'
                searchResultValueKey='regionCode'
                searchColumns={[
                  { field: 'regionCode', headerName: 'Mã khu vực', width: 120 },
                  {
                    field: 'regionName',
                    headerName: 'Tên khu vực',
                    width: 200
                  }
                ]}
              />
            </div>
          </div>
        </div>

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mã vật tư:</Label>
            <div className='flex-1'>
              <FormField
                name='itemCode'
                label=''
                type='text'
                searchEndpoint='/api/items'
                searchResultLabelKey='itemName'
                searchResultValueKey='itemCode'
                searchColumns={[
                  { field: 'itemCode', headerName: 'Mã vật tư', width: 120 },
                  { field: 'itemName', headerName: 'Tên vật tư', width: 200 },
                  { field: 'uom', headerName: 'Đvt', width: 80 },
                  { field: 'group1', headerName: 'Nhóm 1', width: 120 },
                  {
                    field: 'batchTracking',
                    headerName: 'Theo dõi lô',
                    width: 120
                  },
                  {
                    field: 'specification',
                    headerName: 'Quy cách',
                    width: 150
                  },
                  { field: 'image', headerName: 'Hình ảnh', width: 100 }
                ]}
              />
            </div>
          </div>
        </div>

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Loại vật tư:</Label>
            <div className='flex-1'>
              <div className='flex items-center gap-4'>
                <div className='flex-1'>
                  <FormField
                    name='itemType'
                    label=''
                    type='select'
                    options={[
                      { value: '', label: 'Tất cả' },
                      { value: 'service', label: 'Dịch vụ' },
                      { value: 'material', label: 'Vật tư' },
                      { value: 'spare_part', label: 'Phụ tùng' },
                      { value: 'tool', label: 'CCLĐ' },
                      { value: 'semi_finished', label: 'Bán thành phẩm' },
                      { value: 'finished', label: 'Thành phẩm' },
                      { value: 'goods', label: 'Hàng hóa' },
                      { value: 'outsourced', label: 'Hàng gia công' }
                    ]}
                  />
                </div>
                <div className='whitespace-nowrap'>
                  <FormField name='onlyItemsWithInventory' type='checkbox' label='Chỉ xem vật tư có theo dõi tồn kho' />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Nhóm vật tư:</Label>
            <div className='flex-1'>
              <div className='grid grid-cols-3 gap-2'>
                <FormField
                  name='itemGroup1'
                  label=''
                  type='text'
                  searchEndpoint='/api/item-groups'
                  searchResultLabelKey='groupName'
                  searchResultValueKey='groupCode'
                  searchColumns={[
                    { field: 'groupCode', headerName: 'Mã nhóm', width: 120 },
                    { field: 'groupName', headerName: 'Tên nhóm', width: 200 }
                  ]}
                />
                <FormField
                  name='itemGroup2'
                  label=''
                  type='text'
                  searchEndpoint='/api/item-groups'
                  searchResultLabelKey='groupName'
                  searchResultValueKey='groupCode'
                  searchColumns={[
                    { field: 'groupCode', headerName: 'Mã nhóm', width: 120 },
                    { field: 'groupName', headerName: 'Tên nhóm', width: 200 }
                  ]}
                />
                <FormField
                  name='itemGroup3'
                  label=''
                  type='text'
                  searchEndpoint='/api/item-groups'
                  searchResultLabelKey='groupName'
                  searchResultValueKey='groupCode'
                  searchColumns={[
                    { field: 'groupCode', headerName: 'Mã nhóm', width: 120 },
                    { field: 'groupName', headerName: 'Tên nhóm', width: 200 }
                  ]}
                />
              </div>
            </div>
          </div>
        </div>

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mã kho:</Label>
            <div className='flex-1'>
              <FormField
                name='warehouseCode'
                label=''
                type='text'
                searchEndpoint='/api/warehouses'
                searchResultLabelKey='warehouseName'
                searchResultValueKey='warehouseCode'
                searchColumns={[
                  { field: 'warehouseCode', headerName: 'Mã kho', width: 120 },
                  { field: 'warehouseName', headerName: 'Tên kho', width: 200 },
                  { field: 'unit', headerName: 'Đơn vị', width: 120 },
                  {
                    field: 'locationTracking',
                    headerName: 'Theo dõi vị trí',
                    width: 120
                  }
                ]}
              />
            </div>
          </div>
        </div>

        <div className='mb-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mẫu báo cáo:</Label>
            <div className='flex-1'>
              <FormField
                name='reportTemplate'
                label=''
                type='select'
                options={[
                  { value: 'quantity', label: 'Mẫu số lượng' },
                  {
                    value: 'quantityAndValue',
                    label: 'Mẫu số lượng và giá trị'
                  },
                  {
                    value: 'quantityAndForeignValue',
                    label: 'Mẫu số lượng và giá trị ngoại tệ'
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeneralTab;
