import { FieldConfig } from '../types';

export const calculateMaxLabelWidth = (fields: FieldConfig[]) => {
  // Default width when running on server side
  if (typeof window === 'undefined') {
    return 160;
  }

  try {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    if (context) {
      context.font = '12px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      return (
        Math.ceil(
          Math.max(
            ...fields
              .filter(field => field.type !== 'checkbox' || field.labelPosition === 'left')
              .map(field => context.measureText(field.label).width)
          )
        ) + 20
      );
    }
  } catch (error) {
    console.warn('Failed to calculate label width:', error);
  }

  return 160;
};

export default calculateMaxLabelWidth;
